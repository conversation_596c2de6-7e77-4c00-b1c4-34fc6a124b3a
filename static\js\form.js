// 表单页JavaScript功能 - Form Page JavaScript

// 全局变量
let currentStep = 1;
const totalSteps = 6;
let pendingOCRData = null;

// 步骤消息
const stepMessages = {
    1: '请上传所需文件开始申请流程',
    2: '请填写个人基本信息',
    3: '请填写护照相关信息',
    4: '请填写联系方式信息',
    5: '请填写签证申请详情',
    6: '申请表单已完成，可以提交'
};

// 验证规则
const validationRules = {
    surname: {
        required: true,
        minLength: 1,
        pattern: /^[A-Za-z\s]+$/,
        message: '姓氏只能包含英文字母和空格'
    },
    given_name: {
        required: true,
        minLength: 1,
        pattern: /^[A-Za-z\s]+$/,
        message: '名字只能包含英文字母和空格'
    },
    chinese_name: {
        required: false,
        pattern: /^[\u4e00-\u9fa5]+$/,
        message: '中文名只能包含中文字符'
    },
    dob: {
        required: true,
        pattern: /^\d{2}\/\d{2}\/\d{4}$/,
        message: '请输入正确的日期格式 DD/MM/YYYY'
    },
    place_of_birth: {
        required: true,
        minLength: 2,
        message: '出生地至少需要2个字符'
    },
    passport_number: {
        required: true,
        pattern: /^[A-Z0-9]{6,12}$/,
        message: '护照号码应为6-12位字母和数字组合'
    },
    date_of_issue: {
        required: true,
        pattern: /^\d{2}\/\d{2}\/\d{4}$/,
        message: '请输入正确的日期格式 DD/MM/YYYY'
    },
    passport_expiry: {
        required: true,
        pattern: /^\d{2}\/\d{2}\/\d{4}$/,
        message: '请输入正确的日期格式 DD/MM/YYYY'
    },
    email: {
        required: true,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: '请输入有效的邮箱地址'
    },
    telephone_number: {
        required: true,
        pattern: /^[\d\+\-\s\(\)]{8,20}$/,
        message: '请输入有效的电话号码'
    },
    visa_start_date: {
        required: true,
        pattern: /^\d{2}\/\d{2}\/\d{4}$/,
        message: '请输入正确的日期格式 DD/MM/YYYY'
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeFormPage();
});

function initializeFormPage() {
    // 初始化用户信息
    initializeUserInfo();

    // 初始化拖拽上传功能
    setupDragAndDrop();

    // 初始化实时表单验证
    setupRealTimeValidation();

    // 初始化表单提交处理
    setupFormSubmission();

    // 初始化OCR功能
    setupOCRProcessing();

    // 添加表单监听器来自动检查进度
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('input', Utils.debounce(checkFormProgress, 300));
        form.addEventListener('change', checkFormProgress);
    }

    // 初始化文件上传按钮事件
    setupFileUploadButtons();

    // 初始化日期选择器
    initializeDatePickers();
}

// 用户信息管理
function initializeUserInfo() {
    const username = Utils.getCookie('auth_token') || '用户';

    const usernameElement = document.getElementById('username');
    const avatarElement = document.getElementById('userAvatar');

    if (usernameElement) {
        usernameElement.textContent = username;
    }

    if (avatarElement) {
        avatarElement.textContent = username.charAt(0).toUpperCase();
    }
}

function logout() {
    Utils.deleteCookie('auth_token');
    window.location.href = '/login';
}

// 进度管理
function updateProgress(step) {
    currentStep = step;

    // 更新进度条
    const progressPercentage = ((step - 1) / (totalSteps - 1)) * 100;
    const progressFill = document.getElementById('progressLineFill');
    if (progressFill) {
        progressFill.style.width = progressPercentage + '%';
    }

    // 更新步骤状态
    for (let i = 1; i <= totalSteps; i++) {
        const stepCircle = document.getElementById(`step${i}`);
        const stepLabel = stepCircle?.nextElementSibling;

        if (!stepCircle) continue;

        if (i < step) {
            stepCircle.className = 'step-circle completed';
            if (stepLabel) stepLabel.className = 'step-label completed';
            if (i < totalSteps) stepCircle.textContent = '✓';
        } else if (i === step) {
            stepCircle.className = 'step-circle active';
            if (stepLabel) stepLabel.className = 'step-label active';
            if (i < totalSteps) stepCircle.textContent = i;
        } else {
            stepCircle.className = 'step-circle';
            if (stepLabel) stepLabel.className = 'step-label';
            if (i < totalSteps) stepCircle.textContent = i;
        }
    }

    // 更新进度信息
    const progressInfo = document.getElementById('progressInfo');
    if (progressInfo) {
        progressInfo.textContent = stepMessages[step] || '';
    }
}

function checkFormProgress() {
    // 检查文件上传
    const portraitUploaded = document.getElementById('portrait_photo')?.files.length > 0;
    const passportUploaded = document.getElementById('passport_scan')?.files.length > 0;

    if (portraitUploaded && passportUploaded && currentStep === 1) {
        updateProgress(2);
        return;
    }

    // 检查个人信息
    const surname = document.getElementById('surname')?.value.trim();
    const givenName = document.getElementById('given_name')?.value.trim();
    const sex = document.querySelector('input[name="sex"]:checked');
    const dob = document.getElementById('dob')?.value.trim();

    if (surname && givenName && sex && dob && currentStep === 2) {
        updateProgress(3);
        return;
    }

    // 检查护照信息
    const passportNumber = document.getElementById('passport_number')?.value.trim();
    const dateOfIssue = document.getElementById('date_of_issue')?.value.trim();
    const passportExpiry = document.getElementById('passport_expiry')?.value.trim();

    if (passportNumber && dateOfIssue && passportExpiry && currentStep === 3) {
        updateProgress(4);
        return;
    }

    // 检查联系信息
    const email = document.getElementById('email')?.value.trim();
    const telephone = document.getElementById('telephone_number')?.value.trim();

    if (email && telephone && currentStep === 4) {
        updateProgress(5);
        return;
    }

    // 检查签证详情
    const visaEntryType = document.querySelector('input[name="visa_entry_type"]:checked');
    const visaValidityDuration = document.querySelector('input[name="visa_validity_duration"]:checked');
    const visaStartDate = document.getElementById('visa_start_date')?.value.trim();
    const intendedEntryGate = document.getElementById('intended_entry_gate')?.value;

    if (visaEntryType && visaValidityDuration && visaStartDate && intendedEntryGate && currentStep === 5) {
        updateProgress(6);
    }
}

// 表单验证
function validateField(fieldName, value) {
    const rules = validationRules[fieldName];
    if (!rules) return { isValid: true };

    // 检查必填字段
    if (rules.required && (!value || value.trim() === '')) {
        return { isValid: false, message: '此字段为必填项' };
    }

    // 如果字段为空且非必填，则跳过其他验证
    if (!value || value.trim() === '') {
        return { isValid: true };
    }

    // 检查最小长度
    if (rules.minLength && value.length < rules.minLength) {
        return { isValid: false, message: `至少需要${rules.minLength}个字符` };
    }

    // 检查正则表达式
    if (rules.pattern && !rules.pattern.test(value)) {
        return { isValid: false, message: rules.message };
    }

    // 特殊验证：护照有效期检查
    if (fieldName === 'passport_expiry' && rules.pattern.test(value)) {
        const expiryDate = Utils.parseDate(value);
        const today = new Date();
        const sixMonthsFromNow = new Date();
        sixMonthsFromNow.setMonth(today.getMonth() + 6);

        if (expiryDate <= sixMonthsFromNow) {
            return { isValid: false, message: '护照有效期应至少还有6个月' };
        }
    }

    return { isValid: true, message: '验证通过' };
}

function showValidation(input, validation) {
    const formGroup = input.closest('.form-group');
    if (!formGroup) return;

    let validationMsg = formGroup.querySelector('.validation-message');
    let validationIcon = formGroup.querySelector('.validation-icon');

    // 移除现有的验证消息和图标
    if (validationMsg) validationMsg.remove();
    if (validationIcon) validationIcon.remove();

    // 重置输入框样式
    input.classList.remove('valid', 'invalid');

    if (validation.isValid) {
        input.classList.add('valid');

        // 添加成功图标
        validationIcon = document.createElement('span');
        validationIcon.className = 'validation-icon success';
        validationIcon.textContent = '✓';
        input.parentNode.appendChild(validationIcon);

        if (validation.message && validation.message !== '验证通过') {
            validationMsg = document.createElement('div');
            validationMsg.className = 'validation-message success';
            validationMsg.textContent = validation.message;
            input.parentNode.appendChild(validationMsg);
        }
    } else {
        input.classList.add('invalid');

        // 添加错误图标
        validationIcon = document.createElement('span');
        validationIcon.className = 'validation-icon error';
        validationIcon.textContent = '✗';
        input.parentNode.appendChild(validationIcon);

        // 添加错误消息
        validationMsg = document.createElement('div');
        validationMsg.className = 'validation-message error';
        validationMsg.textContent = validation.message;
        input.parentNode.appendChild(validationMsg);
    }
}

function setupRealTimeValidation() {
    Object.keys(validationRules).forEach(fieldName => {
        const input = document.getElementById(fieldName);
        if (input) {
            input.addEventListener('blur', function() {
                const validation = validateField(fieldName, this.value);
                showValidation(this, validation);
                setTimeout(checkFormProgress, 100);
            });

            input.addEventListener('input', Utils.debounce(function() {
                const validation = validateField(fieldName, this.value);
                showValidation(this, validation);
                setTimeout(checkFormProgress, 100);
            }, 500));
        }
    });
}

// 文件上传功能
function setupDragAndDrop() {
    document.querySelectorAll('.file-preview').forEach(preview => {
        const targetId = preview.getAttribute('data-target');
        const fileInput = document.getElementById(targetId);

        if (!fileInput) return;

        // 点击预览区域触发文件选择
        preview.addEventListener('click', () => {
            fileInput.click();
        });

        // 拖拽事件
        preview.addEventListener('dragover', (e) => {
            e.preventDefault();
            preview.classList.add('drag-over');
        });

        preview.addEventListener('dragleave', (e) => {
            e.preventDefault();
            preview.classList.remove('drag-over');
        });

        preview.addEventListener('drop', (e) => {
            e.preventDefault();
            preview.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];

                if (Utils.isValidImageFile(file)) {
                    if (Utils.isValidFileSize(file)) {
                        const dt = new DataTransfer();
                        dt.items.add(file);
                        fileInput.files = dt.files;
                        fileInput.dispatchEvent(new Event('change', { bubbles: true }));
                    } else {
                        Utils.showError('文件过大', '文件大小不能超过5MB');
                    }
                } else {
                    Utils.showError('文件格式错误', '请上传图片文件 (JPG, PNG)');
                }
            }
        });
    });
}

function previewFile(input, previewId) {
    const preview = document.getElementById(previewId);
    const file = input.files[0];

    if (file) {
        if (!Utils.isValidImageFile(file)) {
            Utils.showError('文件格式错误', '请上传图片文件 (JPG, PNG)');
            input.value = '';
            return;
        }

        if (!Utils.isValidFileSize(file)) {
            Utils.showError('文件过大', '文件大小不能超过5MB');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview">`;
        }
        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = `
            <div class="file-preview-text">
                <div class="drag-text">拖拽文件到此处或点击上传</div>
                <div class="file-types">支持 JPG, PNG 格式</div>
            </div>
        `;
    }
}

function setupFileUploadButtons() {
    document.querySelectorAll('.upload-btn').forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const fileInput = document.getElementById(targetId);
            if (fileInput) {
                fileInput.click();
            }
        });
    });

    document.querySelectorAll('input[type="file"][data-preview]').forEach(input => {
        input.addEventListener('change', function() {
            const previewId = this.getAttribute('data-preview');
            previewFile(this, previewId);
            setTimeout(checkFormProgress, 100);
        });
    });
}

// OCR功能
function setupOCRProcessing() {
    const passportInput = document.getElementById('passport_scan');
    if (passportInput) {
        passportInput.addEventListener('change', function() {
            const file = this.files[0];
            if (!file) return;

            Utils.showLoading('正在识别护照信息，请稍候...');

            const formData = new FormData();
            formData.append('passport_scan', file);

            fetch('/ocr-passport/', {
                method: 'POST',
                body: formData
            })
            .then(res => res.json())
            .then(data => {
                Utils.hideStatus();

                const hasData = Object.keys(data).some(key => data[key] && data[key].trim() !== '');

                if (hasData) {
                    showOCRConfirmation(data);
                } else {
                    Utils.showWarning('OCR识别结果为空',
                        '未能从护照图片中识别到有效信息，请确保图片清晰并手动填写信息。');
                }
            })
            .catch(err => {
                Utils.hideStatus();
                console.error('OCR错误:', err);
                Utils.showError('OCR识别失败',
                    '护照信息识别失败，可能是网络问题或图片质量问题。请手动填写信息。');
            });
        });
    }
}

function showOCRConfirmation(ocrData) {
    pendingOCRData = ocrData;
    const fieldsContainer = document.getElementById('ocrFields');
    const fieldLabels = {
        surname: '姓氏',
        given_name: '名字',
        chinese_name: '中文名',
        sex: '性别',
        dob: '出生日期',
        place_of_birth: '出生地',
        nationality: '国籍',
        passport_number: '护照号码',
        passport_type: '护照类型',
        place_of_issue: '签发地',
        date_of_issue: '签发日期',
        passport_expiry: '有效期'
    };

    if (fieldsContainer) {
        fieldsContainer.innerHTML = '';

        Object.keys(ocrData).forEach(key => {
            if (ocrData[key] && fieldLabels[key]) {
                const fieldDiv = document.createElement('div');
                fieldDiv.className = 'ocr-field';
                fieldDiv.innerHTML = `
                    <div class="ocr-field-label">${fieldLabels[key]}:</div>
                    <input type="text" class="ocr-field-value" data-field="${key}" value="${ocrData[key]}">
                `;
                fieldsContainer.appendChild(fieldDiv);
            }
        });
    }

    const ocrModal = document.getElementById('ocrConfirmation');
    if (ocrModal) {
        ocrModal.classList.add('show');
    }
}

function confirmOCR() {
    const fieldInputs = document.querySelectorAll('.ocr-field-value');
    const confirmedData = {};

    fieldInputs.forEach(input => {
        const fieldName = input.getAttribute('data-field');
        confirmedData[fieldName] = input.value;
    });

    // 填入表单
    Object.keys(confirmedData).forEach(fieldName => {
        const formField = document.getElementById(fieldName);
        if (formField) {
            formField.value = confirmedData[fieldName];

            // 触发验证
            const validation = validateField(fieldName, confirmedData[fieldName]);
            showValidation(formField, validation);
        }

        // 处理性别单选框
        if (fieldName === 'sex') {
            const maleRadio = document.getElementById('sex-male');
            const femaleRadio = document.getElementById('sex-female');
            if (confirmedData[fieldName] === 'M' || confirmedData[fieldName] === '男') {
                if (maleRadio) maleRadio.checked = true;
            } else if (confirmedData[fieldName] === 'F' || confirmedData[fieldName] === '女') {
                if (femaleRadio) femaleRadio.checked = true;
            }
        }
    });

    const ocrModal = document.getElementById('ocrConfirmation');
    if (ocrModal) {
        ocrModal.classList.remove('show');
    }

    checkFormProgress();
    Utils.showSuccess('OCR识别完成', '护照信息已成功填入表单，请检查并继续填写其他信息');
}

function cancelOCR() {
    const ocrModal = document.getElementById('ocrConfirmation');
    if (ocrModal) {
        ocrModal.classList.remove('show');
    }
    pendingOCRData = null;
}

// 表单提交
function setupFormSubmission() {
    const form = document.querySelector('form');
    if (!form) return;

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        Utils.showLoading('正在提交申请，请稍候...');

        try {
            const formData = new FormData(form);

            const response = await fetch('/apply-visa-form/', {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const result = await response.json();
                Utils.showSuccess('申请提交成功',
                    `您的申请已成功提交！追踪ID: ${result.data?.tracking_id || '未知'}。请查看邮箱获取后续更新。`);

                setTimeout(() => {
                    form.reset();
                    updateProgress(1);
                    Utils.hideStatus();
                }, 5000);
            } else {
                const errorData = await response.json();
                Utils.showError('提交失败',
                    errorData.detail || '申请提交失败，请检查表单信息并重试。');
            }
        } catch (error) {
            console.error('提交错误:', error);
            Utils.showError('网络错误',
                '网络连接出现问题，请检查网络连接后重试。');
        }
    });
}

// 日期选择器初始化
function initializeDatePickers() {
    if (typeof flatpickr !== 'undefined') {
        const dateInputs = document.querySelectorAll('input[type="text"][id*="date"], input[type="text"][id*="dob"], input[type="text"][id*="expiry"]');

        dateInputs.forEach(input => {
            flatpickr(input, {
                dateFormat: "d/m/Y",
                allowInput: true,
                locale: "zh"
            });
        });
    }
}

// 导出全局函数
window.logout = logout;
window.updateProgress = updateProgress;
window.checkFormProgress = checkFormProgress;
window.confirmOCR = confirmOCR;
window.cancelOCR = cancelOCR;
