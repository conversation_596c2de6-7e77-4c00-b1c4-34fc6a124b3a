# api/database/sqlite_adapter.py
"""
SQLite数据库适配器 - 包装现有的SQLite操作
"""
import sqlite3
import asyncio
from typing import Dict, List, Optional, Any
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

from .base import DatabaseInterface
from ..config.settings import settings


class SQLiteAdapter(DatabaseInterface):
    """SQLite数据库适配器"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or "results/visa_results.db"
        self.schema_path = "db/schema.sql"
        self.executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="sqlite_worker")
    
    async def connect(self) -> bool:
        """连接SQLite数据库"""
        try:
            # 确保目录存在
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 测试连接
            def test_connection():
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("SELECT 1")
                return True
            
            return await asyncio.get_event_loop().run_in_executor(
                self.executor, test_connection
            )
        except Exception:
            return False
    
    async def disconnect(self):
        """断开连接"""
        self.executor.shutdown(wait=False)
    
    async def init_schema(self):
        """初始化数据库结构"""
        def init_db():
            if not Path(self.schema_path).exists():
                raise FileNotFoundError(f"Schema file not found: {self.schema_path}")
            
            with sqlite3.connect(self.db_path) as conn:
                with open(self.schema_path, "r", encoding="utf-8") as f:
                    sql_script = f.read()
                    conn.executescript(sql_script)
        
        await asyncio.get_event_loop().run_in_executor(
            self.executor, init_db
        )
    
    async def insert_applicant(self, data: Dict) -> int:
        """插入申请人"""
        def _insert():
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    INSERT INTO applicant (
                        created_at, customer_source, chinese_name, surname, given_name,
                        sex, dob, place_of_birth, passport_number, place_of_issue,
                        date_of_issue, passport_expiry, nationality, email, telephone_number
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    data.get("created_at", self._get_beijing_time()),
                    data.get("customer_source"),
                    data.get("chinese_name"),
                    data.get("surname"),
                    data.get("given_name"),
                    data.get("sex"),
                    data.get("dob"),
                    data.get("place_of_birth"),
                    data.get("passport_number"),
                    data.get("place_of_issue"),
                    data.get("date_of_issue"),
                    data.get("passport_expiry"),
                    data.get("nationality"),
                    data.get("email"),
                    data.get("telephone_number")
                ))
                return cursor.lastrowid
        
        return await asyncio.get_event_loop().run_in_executor(
            self.executor, _insert
        )
    
    async def get_or_create_applicant(self, data: Dict) -> int:
        """获取或创建申请人"""
        def _get_or_create():
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT id FROM applicant WHERE passport_number = ?",
                    (data["passport_number"],)
                )
                row = cursor.fetchone()
                if row:
                    return row[0]
            
            # 如果不存在，创建新记录
            return asyncio.run(self.insert_applicant(data))
        
        return await asyncio.get_event_loop().run_in_executor(
            self.executor, _get_or_create
        )
    
    async def get_applicant_by_application_number(self, app_number: str) -> Optional[Dict]:
        """通过申请编号获取申请人"""
        def _get_applicant():
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT a.*
                    FROM applicant a
                    JOIN visa_task v ON v.applicant_id = a.id
                    WHERE v.application_number = ?
                    ORDER BY v.created_at DESC
                    LIMIT 1
                """, (app_number,))
                row = cursor.fetchone()
                if row:
                    columns = [desc[0] for desc in cursor.description]
                    return dict(zip(columns, row))
                return None
        
        return await asyncio.get_event_loop().run_in_executor(
            self.executor, _get_applicant
        )
    
    async def insert_visa_task(self, applicant_id: int, data: Dict) -> int:
        """插入签证任务"""
        def _insert():
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    INSERT INTO visa_task (
                        applicant_id, visa_type, visa_validity_days, visa_start_date, entry_gate,
                        submit_status, approval_status, is_downloaded, pdf_path, download_time,
                        screenshot_path, log_path, error_message, application_number, created_at,
                        session_id
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    applicant_id,
                    data.get("visa_type"),
                    data.get("visa_validity_days"),
                    data.get("visa_start_date"),
                    data.get("entry_gate"),
                    data.get("submit_status", "FAILED"),
                    data.get("approval_status", "WAITING"),
                    data.get("is_downloaded", 0),
                    data.get("pdf_path"),
                    data.get("download_time"),
                    data.get("screenshot_path"),
                    data.get("log_path"),
                    data.get("error_message"),
                    data.get("application_number"),
                    self._get_beijing_time(),
                    data.get("session_id")
                ))
                return cursor.lastrowid
        
        return await asyncio.get_event_loop().run_in_executor(
            self.executor, _insert
        )
    
    async def update_visa_task_status(self, task_id: int, status: str, **kwargs):
        """更新签证任务状态"""
        def _update():
            with sqlite3.connect(self.db_path) as conn:
                # 构建动态更新语句
                set_clauses = ["submit_status = ?"]
                params = [status]
                
                for key, value in kwargs.items():
                    set_clauses.append(f"{key} = ?")
                    params.append(value)
                
                params.append(task_id)
                
                sql = f"UPDATE visa_task SET {', '.join(set_clauses)} WHERE id = ?"
                conn.execute(sql, params)
        
        await asyncio.get_event_loop().run_in_executor(
            self.executor, _update
        )
    
    async def find_task_by_name_and_dob(self, full_name: str, dob: str) -> Optional[int]:
        """通过姓名和出生日期查找任务"""
        def _find():
            normalized_name = " ".join(full_name.strip().upper().split())
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT vt.id
                    FROM visa_task vt
                    JOIN applicant a ON a.id = vt.applicant_id
                    WHERE TRIM(UPPER(a.surname || ' ' || a.given_name)) = ?
                      AND a.dob = ?
                    ORDER BY vt.created_at DESC
                    LIMIT 1
                """, (normalized_name, dob))
                row = cursor.fetchone()
                return row[0] if row else None
        
        return await asyncio.get_event_loop().run_in_executor(
            self.executor, _find
        )
    
    async def insert_payment_record(self, task_id: int, data: Dict):
        """插入支付记录"""
        def _insert():
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO payment_record (
                        visa_task_id, payment_time, payment_amount, card_number_tail,
                        payment_method, payment_status, transaction_id, source_email
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    task_id,
                    data.get("payment_time"),
                    data.get("payment_amount"),
                    data.get("card_number_tail"),
                    data.get("payment_method"),
                    data.get("payment_status"),
                    data.get("transaction_id"),
                    data.get("source_email")
                ))
        
        await asyncio.get_event_loop().run_in_executor(
            self.executor, _insert
        )
    
    async def insert_email_notification(self, task_id: Optional[int], data: Dict):
        """插入邮件通知"""
        def _insert():
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO email_notification (
                        visa_task_id, received_time, from_address, subject,
                        content_excerpt, application_number, email_type
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    task_id,
                    data.get("received_time"),
                    data.get("from_address"),
                    data.get("subject"),
                    data.get("content_excerpt"),
                    data.get("application_number"),
                    data.get("email_type")
                ))
        
        await asyncio.get_event_loop().run_in_executor(
            self.executor, _insert
        )
    
    async def count_records(self) -> Dict[str, int]:
        """统计记录数量"""
        def _count():
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM applicant")
                applicant_count = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(*) FROM visa_task")
                task_count = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(*) FROM visa_task WHERE submit_status = 'SUCCESS'")
                success_count = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(*) FROM visa_task WHERE is_downloaded = 1")
                downloaded_count = cursor.fetchone()[0]
                
                return {
                    "applicants": applicant_count,
                    "tasks": task_count,
                    "success": success_count,
                    "downloaded": downloaded_count
                }
        
        return await asyncio.get_event_loop().run_in_executor(
            self.executor, _count
        )
    
    async def verify_recent_records(self, limit: int = 5) -> List[Dict]:
        """获取最近记录"""
        def _verify():
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT vt.id, vt.created_at, a.chinese_name, a.surname, a.given_name, 
                           a.passport_number, a.dob, a.sex, a.customer_source,
                           vt.visa_validity_days, vt.visa_type, vt.entry_gate, 
                           vt.visa_start_date, a.email, vt.submit_status, 
                           vt.application_number, vt.approval_status
                    FROM visa_task vt
                    JOIN applicant a ON a.id = vt.applicant_id
                    ORDER BY vt.created_at DESC
                    LIMIT ?
                """, (limit,))
                return [dict(row) for row in cursor.fetchall()]
        
        return await asyncio.get_event_loop().run_in_executor(
            self.executor, _verify
        )
    
    async def health_check(self) -> bool:
        """数据库健康检查"""
        try:
            return await self.connect()
        except Exception:
            return False
    
    def _get_beijing_time(self) -> str:
        """获取北京时间"""
        from datetime import datetime, timedelta, timezone
        beijing_tz = timezone(timedelta(hours=8))
        return datetime.now(beijing_tz).strftime("%Y-%m-%d %H:%M:%S")
