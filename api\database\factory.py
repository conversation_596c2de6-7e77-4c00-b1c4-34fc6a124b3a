# api/database/factory.py
"""
数据库工厂 - 根据配置创建相应的数据库适配器
"""
from typing import Optional
import asyncio

from .base import DatabaseInterface, DatabaseManager
from .sqlite_adapter import SQLiteAdapter
from .postgresql_adapter import PostgreSQLAdapter
from ..config.settings import settings


class DatabaseFactory:
    """数据库工厂类"""
    
    @staticmethod
    def create_database(db_type: str = None) -> DatabaseInterface:
        """
        根据类型创建数据库适配器
        
        Args:
            db_type: 数据库类型 ('sqlite' 或 'postgresql')
            
        Returns:
            DatabaseInterface: 数据库适配器实例
        """
        db_type = db_type or settings.database_type
        
        if db_type.lower() == "postgresql":
            connection_string = (
                f"postgresql://{settings.postgres_user}:{settings.postgres_password}"
                f"@{settings.postgres_host}:{settings.postgres_port}/{settings.postgres_db}"
            )
            return PostgreSQLAdapter(connection_string)
        
        elif db_type.lower() == "sqlite":
            return SQLiteAdapter(settings.sqlite_path)
        
        else:
            raise ValueError(f"Unsupported database type: {db_type}")
    
    @staticmethod
    def create_dual_database_manager() -> DatabaseManager:
        """
        创建双数据库管理器（SQLite + PostgreSQL）
        
        Returns:
            DatabaseManager: 数据库管理器实例
        """
        # 主数据库（根据配置决定）
        primary_db = DatabaseFactory.create_database(settings.database_type)
        
        # 备用数据库（另一种类型）
        backup_type = "sqlite" if settings.database_type == "postgresql" else "postgresql"
        try:
            backup_db = DatabaseFactory.create_database(backup_type)
        except Exception:
            backup_db = None
        
        return DatabaseManager(primary_db, backup_db)


# 全局数据库管理器实例
_db_manager: Optional[DatabaseManager] = None


async def get_database_manager() -> DatabaseManager:
    """
    获取全局数据库管理器实例
    
    Returns:
        DatabaseManager: 数据库管理器实例
    """
    global _db_manager
    
    if _db_manager is None:
        _db_manager = DatabaseFactory.create_dual_database_manager()
        
        # 初始化主数据库连接
        primary_db = await _db_manager.get_db()
        connected = await primary_db.connect()
        
        if not connected:
            print(f"⚠️ 主数据库连接失败，尝试切换到备用数据库")
            if _db_manager.backup_db:
                await _db_manager.switch_to_backup()
                backup_connected = await _db_manager.backup_db.connect()
                if backup_connected:
                    print(f"✅ 已切换到备用数据库")
                else:
                    raise Exception("所有数据库连接都失败")
            else:
                raise Exception("主数据库连接失败且无备用数据库")
        
        # 初始化数据库结构
        try:
            await primary_db.init_schema()
            print(f"✅ 数据库结构初始化完成")
        except Exception as e:
            print(f"⚠️ 数据库结构初始化失败: {e}")
    
    return _db_manager


async def get_database() -> DatabaseInterface:
    """
    获取当前活跃的数据库实例
    
    Returns:
        DatabaseInterface: 数据库实例
    """
    manager = await get_database_manager()
    return await manager.get_db()


async def close_database():
    """关闭数据库连接"""
    global _db_manager
    
    if _db_manager:
        await _db_manager.primary_db.disconnect()
        if _db_manager.backup_db:
            await _db_manager.backup_db.disconnect()
        _db_manager = None


# 数据库健康检查
async def check_database_health() -> dict:
    """
    检查数据库健康状态
    
    Returns:
        dict: 健康状态信息
    """
    try:
        manager = await get_database_manager()
        
        # 检查主数据库
        primary_healthy = await manager.primary_db.health_check()
        
        # 检查备用数据库
        backup_healthy = False
        if manager.backup_db:
            backup_healthy = await manager.backup_db.health_check()
        
        return {
            "primary_database": "healthy" if primary_healthy else "unhealthy",
            "backup_database": "healthy" if backup_healthy else "not_available",
            "current_database": settings.database_type,
            "using_backup": manager.use_backup
        }
    except Exception as e:
        return {
            "primary_database": "error",
            "backup_database": "error",
            "error": str(e)
        }
