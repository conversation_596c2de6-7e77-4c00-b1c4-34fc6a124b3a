import json
import os

# 读取映射表文件路径
JSON_PATH = os.path.join(os.path.dirname(__file__), '..', 'data', 'zh_to_en_province_map.json')

# 加载中文省份 → 拼音（大写）映射表
with open(JSON_PATH, 'r', encoding='utf-8') as f:
    ZH_TO_EN_PROVINCE_MAP = json.load(f)

def convert_province_to_pinyin(chinese_province: str) -> str:
    """
    将中文省份名称转换为拼音大写（如：'广东' → 'GUANGDONG'）

    参数:
        chinese_province (str): 中文省份名

    返回:
        str: 拼音大写，如果未匹配则返回空字符串
    """
    return ZH_TO_EN_PROVINCE_MAP.get(chinese_province.strip(), "")
