# 文件位置: app/utils/ocr_utils.py
# 用途：调用阿里云 OCR 接口识别护照信息（使用 APPCODE 简单认证）

import base64
import json
import os
from app.utils.logger_config import get_logger
import requests
from pathlib import Path

logger = get_logger()

# 修改为函数，在运行时获取环境变量
def get_aliyun_appcode():
    appcode = os.environ.get("ALIYUN_APPCODE")
    if not appcode:
        logger.error("❌ 环境变量ALIYUN_APPCODE未设置，OCR功能将无法使用")
    return appcode

# OCR 接口地址
OCR_API_URL = "https://ocrhz.market.alicloudapi.com/rest/160601/ocr/ocr_passport.json"

def run_aliyun_passport_ocr(image_path: str) -> dict:
    """
    调用阿里云 OCR 接口识别护照图片，返回包含护照字段信息的字典。

    :param image_path: 图片的绝对路径
    :return: 识别结果 dict，如果失败返回空 dict
    """
    logger.info(f"开始调用阿里云 OCR 识别护照: {image_path}")

    # 获取 ALIYUN_APPCODE
    aliyun_appcode = get_aliyun_appcode()
    
    # 检查API Key是否设置
    if not aliyun_appcode:
        logger.error("❌ 环境变量ALIYUN_APPCODE未设置，无法进行OCR识别")
        return {}

    # ✅ 读取并编码图片为 Base64
    try:
        with open(image_path, "rb") as f:
            image_data = base64.b64encode(f.read()).decode("utf-8")
    except Exception as e:
        logger.error(f"读取图片失败: {e}")
        return {}

    # ✅ 构建请求头（使用 APPCODE 简单认证）
    headers = {
        "Authorization": f"APPCODE {aliyun_appcode}",
        "Content-Type": "application/json; charset=UTF-8"
    }

    # ✅ 构建请求体（Body）
    payload = {
        "image": image_data
    }

    # ✅ 发送 POST 请求
    try:
        response = requests.post(OCR_API_URL, headers=headers, data=json.dumps(payload))
        response.raise_for_status()
    except Exception as e:
        logger.error(f"OCR 请求失败: {e}")
        return {}

    # ✅ 解析响应结果
    try:
        result = response.json()
        logger.info(f"OCR 原始响应: {result}")
        if result.get("success"):
            logger.info("✅ OCR 识别成功")
            logger.info("📄 OCR识别完整信息:\n%s", json.dumps(result, indent=2, ensure_ascii=False)) 


            # 格式化 birth_date 字段为 dd/mm/yyyy（直接覆盖原字段）
            dob = result.get("birth_date")
            if dob and len(dob) == 8 and dob.isdigit():
                result["birth_date"] = f"{dob[6:8]}/{dob[4:6]}/{dob[0:4]}"

            return result  # 直接返回原始结构
        else:
            logger.warning("OCR 返回未标记成功")
            logger.warning(f"原始返回内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return {}

    except Exception as e:
        logger.error(f"解析 OCR 响应失败: {e}")
        return {}

from typing import Dict

def normalize_ocr_result_for_applicant(ocr_result: Dict) -> Dict:
    """
    将OCR识别结果格式化为Applicant模型所需字段，确保字段格式与自动化流程一致。
    """
    # 1. 姓名拆分
    name_raw = ocr_result.get("name", "")
    if "," in name_raw:
        surname, given_name = name_raw.split(",", 1)
    else:
        surname = name_raw
        given_name = ""

    # 2. 出生地处理（提取拼音）
    birth_place_raw = ocr_result.get("birth_place_raw", "")
    if birth_place_raw and "/" in birth_place_raw:
        parts = birth_place_raw.split("/")
        if len(parts) > 1:
            place_of_birth = parts[1].strip().upper()
        else:
            place_of_birth = "GUANGDONG"
    elif birth_place_raw and birth_place_raw.isascii():
        place_of_birth = birth_place_raw.strip().upper()
    else:
        place_of_birth = "GUANGDONG"

    # 3. 日期格式处理（如有需要，API端可直接传递原始字符串，自动化模块会再做转换）
    # 例如 date_of_issue, passport_expiry, dob 都是 "YYYYMMDD" 字符串

    # 4. 其它字段直接映射
    return {
        "chinese_name": ocr_result.get("name_cn", "") or ocr_result.get("name_cn_raw", ""),
        "surname": surname,
        "given_name": given_name,
        "sex": ocr_result.get("sex", ""),
        "nationality": ocr_result.get("country", ""),
        "dob": ocr_result.get("birth_date", ""),
        "passport_number": ocr_result.get("passport_no", ""),
        "place_of_birth": place_of_birth,
        "place_of_issue": ocr_result.get("issue_place", ""),
        "date_of_issue": ocr_result.get("issue_date", ""),
        "passport_expiry": ocr_result.get("expiry_date", ""),
        #"passport_type": "Ordinary passport",  # 固定值
        # 其它字段如 religion、emergency_contact_name 等由表单或默认值补充
    }
