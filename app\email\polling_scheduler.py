import random
from datetime import datetime

ACTIVE_WINDOWS = [
    (9, 13),
    (16, 23)
]

def is_within_active_window():
    now = datetime.now().time()
    for start, end in ACTIVE_WINDOWS:
        if start <= now.hour < end:
            return True
    return False

def generate_two_times_this_hour():
    first = random.randint(0, 39)
    second = random.randint(first + 20, 59)
    return sorted([first, second])
