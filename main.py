import sys, os
sys.path.append(os.path.abspath("."))
from app.utils.logger_config import setup_logger, get_logger  # 直接复用导入泰国入境卡自动化项目的彩色日志设置
from PyQt6.QtWidgets import QApplication
from app.ui.main_window import MainWindow
import yaml
from pathlib import Path
from db.db_tools import show_db_stats, verify_recent_records
from app.data.models import Applicant
from dotenv import load_dotenv

# 在程序开始时加载.env文件
load_dotenv()


#设置日志级别
setup_logger(console_level="DEBUG")

# ✅ 设置控制台输出编码为 UTF-8（支持中文 + emoji）
try:
    sys.stdout.reconfigure(encoding='utf-8')
except Exception:
    pass  # 兼容旧版本 Python

# === 应用现代主题样式 ===
def apply_qss(path: str):
    from PyQt6.QtWidgets import QApplication
    with open(path, "r", encoding="utf-8") as f:
        qss = f.read()
        QApplication.instance().setStyleSheet(qss)

# 定义定义用于邮件监听的 SimpleApplicant 类
class SimpleApplicant:
    def __init__(self, email, config):
        self.email = email
        self.config = config
        self.passport_number = f"email_{email.split('@')[0]}"  # 添加唯一的 passport_number

if __name__ == "__main__":
    
    logger = get_logger()
    logger.info("应用程序启动...")

    # 直接从环境变量加载设置
    from app.utils.env_loader import load_env_var, load_email_accounts_from_env

    # 创建设置字典
    settings = {
        "vietnam_evisa_url": load_env_var("VIETNAM_EVISA_URL", "https://evisa.gov.vn/"),
        "browser": load_env_var("BROWSER", "chromium"),
        "headless": load_env_var("HEADLESS", "true").lower() == "true",
        "slow_mo": int(load_env_var("SLOW_MO", "0")),
        "anti_captcha_api_key": load_env_var("ANTI_CAPTCHA_API_KEY", "")
    }

    # 获取邮箱配置 - 直接从环境变量加载
    email_configs_data = load_email_accounts_from_env()
    email_configs = list(email_configs_data.get("email_accounts", {}).values())

    # 2.显示数据库统计信息
    show_db_stats() 
    verify_recent_records(5)  # 只显示最近5条记录   

    # 3. 创建 PyQt 应用实例
    app = QApplication(sys.argv)

    # 4. 加载现代化 QSS 样式
    apply_qss("app/ui/styles/modern_card.qss")

    # 5. 创建并显示主窗口
    logger.info("创建主窗口...")
    main_window = MainWindow()
    main_window.show()
    logger.info("主窗口已显示。")

    # ✅ 6. 启动邮件监听线程（后台运行）
    logger.info("启动邮件监听线程...")

    # 收集所有邮箱的 applicant 对象
    applicants = []
    for config in email_configs:
        # 添加检查确保 config 不是 None 且包含必要的字段
        if config and isinstance(config, dict) and "user" in config:
            applicant = SimpleApplicant(email=config.get("user"), config=config)
            applicants.append(applicant)
        else:
            logger.warning(f"跳过无效的邮箱配置: {config}")
    
    # 一次性启动所有邮箱的轮询
    from app.email.email_runner import start_polling_scheduler
    # 使用QTimer延迟启动邮件轮询，避免阻塞UI初始化
    from PyQt6.QtCore import QTimer
    QTimer.singleShot(1000, lambda: start_polling_scheduler(applicants))
    #start_polling_scheduler(applicants)
    logger.info(f"✅ 已注册 {len(applicants)} 个邮箱的轮询任务")

    # 7. 运行应用事件循环，直到窗口关闭
    logger.info("启动 PyQt 事件循环...")
    try:
        sys.exit(app.exec())
    except SystemExit:
        logger.info("应用程序正常退出。")
    except Exception as e:
         logger.critical(f"应用程序因未处理的异常而终止: {e}", exc_info=True)
         sys.exit(1) # 返回非零表示错误退出
