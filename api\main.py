# api/main.py
"""
Vietnam E-Visa Automator API - 模块化重构版本

这是新的模块化入口文件，替代原来的 api_main.py
"""
from fastapi import FastAPI, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import RedirectResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from .config.settings import settings, STATIC_DIR, TEMPLATES_DIR
from .core.exceptions import (
    VisaApplicationError, OCRProcessingError, FileProcessingError, AuthenticationError,
    visa_application_exception_handler, ocr_processing_exception_handler,
    file_processing_exception_handler, authentication_exception_handler,
    validation_exception_handler, http_exception_handler, general_exception_handler
)
from .auth.routes import router as auth_router
from .routes.visa import router as visa_router
from .routes.ocr import router as ocr_router
from .routes.health import router as health_router

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Vietnam E-Visa Application Automation API",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

# 配置静态文件服务
app.mount("/static", StaticFiles(directory=str(STATIC_DIR)), name="static")

# 配置模板
templates = Jinja2Templates(directory=str(TEMPLATES_DIR))

# 注册异常处理器
app.add_exception_handler(VisaApplicationError, visa_application_exception_handler)
app.add_exception_handler(OCRProcessingError, ocr_processing_exception_handler)
app.add_exception_handler(FileProcessingError, file_processing_exception_handler)
app.add_exception_handler(AuthenticationError, authentication_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(StarletteHTTPException, http_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# 注册路由
app.include_router(auth_router)
app.include_router(visa_router, prefix="/api")
app.include_router(ocr_router, prefix="/api")
app.include_router(health_router, prefix="/api")


@app.get("/")
async def root(request: Request):
    """
    根路径 - 重定向到登录或表单页面
    """
    if not request.cookies.get(settings.auth_cookie_name):
        return RedirectResponse(url="/login", status_code=302)
    return templates.TemplateResponse("test_form.html", {"request": request})


@app.get("/form")
async def form_page(request: Request):
    """
    表单页面
    """
    if not request.cookies.get(settings.auth_cookie_name):
        return RedirectResponse(url="/login", status_code=302)
    return templates.TemplateResponse("test_form.html", {"request": request})


# 应用生命周期事件
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    print(f"🚀 {settings.app_name} v{settings.app_version} 启动成功")
    print(f"📊 调试模式: {'开启' if settings.debug else '关闭'}")
    print(f"📁 静态文件目录: {STATIC_DIR}")
    print(f"📄 模板目录: {TEMPLATES_DIR}")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    print(f"🛑 {settings.app_name} 正在关闭...")


# 开发环境启动
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
