import sqlite3
import csv
from typing import List, Dict, Optional
from pathlib import Path
from app.utils.logger_config import get_logger

logger = get_logger()


DB_PATH = Path("results/visa_results.db")


def _query(sql: str, params: tuple = ()) -> List[Dict]:
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.execute(sql, params)
        return [dict(row) for row in cursor.fetchall()]


def query_all_tasks() -> List[Dict]:
    return _query("SELECT * FROM visa_task ORDER BY created_at DESC")


def query_tasks_by_date(date_str: str) -> List[Dict]:
    return _query("""
        SELECT * FROM visa_task
        WHERE DATE(created_at) = ?
        ORDER BY created_at DESC
    """, (date_str,))


def query_paid_tasks_by_date(date_str: str) -> List[Dict]:
    return _query("""
        SELECT * FROM visa_task
        WHERE DATE(created_at) = ?
          AND submit_status = 'SUCCESS'
          AND payment_status = 'PAID'
        ORDER BY created_at DESC
    """, (date_str,))


def query_unpaid_tasks_by_date(date_str: str) -> List[Dict]:
    return _query("""
        SELECT * FROM visa_task
        WHERE DATE(created_at) = ?
          AND submit_status = 'SUCCESS'
          AND (payment_status IS NULL OR payment_status != 'PAID')
        ORDER BY created_at DESC
    """, (date_str,))


def export_tasks_to_csv(tasks: List[Dict], output_path: Path):
    if not tasks:
        logger.warning("⚠️ 没有数据可导出")
        return

    output_path.parent.mkdir(parents=True, exist_ok=True)
    with open(output_path, "w", newline="", encoding="utf-8-sig") as f:
        writer = csv.DictWriter(f, fieldnames=tasks[0].keys())
        writer.writeheader()
        writer.writerows(tasks)
    logger.info(f"✅ 导出成功: {output_path.resolve()}")
