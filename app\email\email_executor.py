import os, json, ssl, email, time
from imapclient import IMAPClient
import yaml
from pathlib import Path
from app.email.handlers import process_submission_email, process_payment_email
from app.email.result_handler import process_result_email
from config.settings import EMAIL_ACCOUNTS
from app.data.models import Applicant
from config.settings import ALLOWED_SENDERS
from email.utils import parseaddr
from datetime import datetime
from app.email.determine_email_type import determine_email_type
from app.utils.logger_config import get_logger
from app.utils.env_loader import load_email_accounts_from_env
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()




logger = get_logger()

# 重试配置
MAX_RETRIES = 5           # 最大重试次数
RETRY_DELAY = 15           # 初始重试延迟（秒）
RETRY_BACKOFF = 3         # 退避因子（每次重试后延迟时间乘以此值）

UID_CACHE_PATH = "uid_cache.json"

def load_uid_cache():
    if os.path.exists(UID_CACHE_PATH):
        with open(UID_CACHE_PATH, "r") as f:
            return json.load(f)
    return {}

def save_uid_cache(cache):
    with open(UID_CACHE_PATH, "w") as f:
        json.dump(cache, f)

def run_single_polling(source: str, applicant: Applicant = None, mode: str = "all"):
    """
    安全轮询：用于提交后、付款后或定时触发
    参数:
        applicant: 申请人对象，包含 email 等字段
        source: 字符串，来源标记（如 event:after_payment / scheduler）
        mode: 轮询模式，'all'处理提交成功和付款成功邮件，'result_only'只处理出签结果邮件
    """
    # 刷新邮箱配置
    
    fresh_email_config = load_email_accounts_from_env()
    email_accounts = fresh_email_config["email_accounts"]
    allowed_senders = fresh_email_config["allowed_senders"]

    # 记录当前可用的邮箱配置
    logger.debug(f"当前EMAIL_ACCOUNTS中的邮箱: {list(EMAIL_ACCOUNTS.keys())}")
    logger.debug(f"从环境变量重新加载的邮箱: {list(fresh_email_config['email_accounts'].keys())}")

    # 使用重新加载的邮箱配置
    email_account = applicant.email if applicant else None
    logger.debug(f"使用的邮箱：{email_account}")
    if email_account not in EMAIL_ACCOUNTS:
        logger.warning(f"⚠️ 无效邮箱，找不到对应邮箱配置: {email_account}")
        return

    config = EMAIL_ACCOUNTS[email_account]
    uid_cache = load_uid_cache()
    last_seen = uid_cache.get(email_account, 0)

    # 添加详细的调试日志
    logger.debug(f"开始轮询 - 来源: {source}, 邮箱: {email_account}, 模式: {mode}, 上次处理UID: {last_seen}")

    # 实现重试逻辑
    retry_count = 0
    delay = RETRY_DELAY
    while retry_count <= MAX_RETRIES:
        try:
            with IMAPClient(config["host"], port=config["port"], ssl=True, ssl_context=ssl.create_default_context()) as client:
                # 添加连接和登录的调试日志
                logger.debug(f"✅ 连接到邮件服务器: {config['host']}:{config['port']}")
                client.login(config["user"], config["password"])
                logger.debug(f"✅ 登录成功: {config['user']}")

                client.select_folder("INBOX")
                messages = client.search(["UNSEEN"])
                logger.info(f"📬 触发轮询: {source}, 邮箱: {email_account}, 未读: {len(messages)}")

                # 添加消息列表的调试日志
                logger.debug(f"未读邮件UID列表: {messages}")

                if not messages:
                    logger.info(f"📭 没有未读邮件，轮询结束: {email_account}")
                    return True
                
                processed_count = 0  # 记录成功处理的邮件数量

                max_uid = last_seen
                for uid in messages:
                    if uid <= last_seen:
                        logger.debug(f"跳过已处理的邮件 UID: {uid}")
                        continue
                    
                    # 添加处理单个邮件的调试日志
                    logger.debug(f"处理邮件 UID: {uid}")

                    msg_data = client.fetch([uid], ["RFC822", "INTERNALDATE"])
                    raw = email.message_from_bytes(msg_data[uid][b"RFC822"])
                    received_time = msg_data[uid].get(b"INTERNALDATE", datetime.now())
                    payload = raw.get_payload(decode=True)
                    body = payload.decode("utf-8", errors="ignore") if payload else ""
                    sender = parseaddr(raw.get("From", ""))[1]
                    subject = raw.get("Subject", "")

                    # 记录邮件基本信息
                    logger.debug(f"邮件信息 - 发件人: {sender}, 主题: {subject}, 接收时间: {received_time}")

                    # ✅ 白名单检查
                    if sender.lower() not in [s.lower() for s in ALLOWED_SENDERS.get(email_account, [])]:
                        logger.info(f"⚠️ 发件人 {sender} 不在白名单中，已跳过")
                        continue

                    mail_obj = {
                        "from": sender,
                        "subject": raw.get("Subject", ""),
                        "body": body,
                        "raw": raw,
                        "received_time": received_time
                    }

                    # ✅ 根据模式判断处理
                    if mode == "result_only":
                        # 使用determine_email_type函数判断邮件类型
                        email_type = determine_email_type(sender, subject, body)
                        logger.debug(f"邮件类型判断结果: {email_type}")
                        
                        if email_type == "result":
                            logger.info(f"✅ 发现出签结果邮件: {sender}")
                            process_result_email(mail_obj, body)
                        else:
                            logger.info(f"📭 [result_only] 跳过非出签邮件: {sender}")
                    else:
                        # 使用determine_email_type函数判断邮件类型
                        email_type = determine_email_type(sender, subject, body)
                        logger.debug(f"邮件类型判断结果: {email_type}")
                        
                        if email_type == "submission":
                            logger.info(f"✅ 确认收到提交确认邮件: {sender}")
                            process_submission_email(mail_obj, body)
                        elif email_type == "payment":
                            logger.info(f"✅ 确认收到付款确认邮件: {sender}")
                            process_payment_email(mail_obj, body)
                        elif email_type == "result":
                            logger.info(f"✅ 确认收到出签结果邮件: {sender}")
                            process_result_email(mail_obj, body)
                        else:
                            logger.debug(f"未识别的邮件类型: {email_type}, 发件人: {sender}")

                    client.add_flags(uid, ["\\Seen"])
                    max_uid = max(max_uid, uid)
                    logger.debug(f"{email_account} - 邮件 UID: {uid} 已标记为已读")
                    processed_count += 1
                
                # 更新缓存
                if max_uid > last_seen:
                    uid_cache[email_account] = max_uid
                    save_uid_cache(uid_cache)
                    logger.debug(f"更新UID缓存: {email_account} - 从 {last_seen} 到 {max_uid}")
                
                # 成功完成，返回True
                logger.info(f"✅ 轮询完成: {source}, 邮箱: {email_account}, 处理: {processed_count}封邮件")
                return True

        except Exception as e:
            retry_count += 1
            if retry_count <= MAX_RETRIES:
                logger.warning(f"⚠️ 邮箱轮询失败 (尝试 {retry_count}/{MAX_RETRIES}): {e}")
                logger.debug(f"将在 {delay} 秒后重试...")
                time.sleep(delay)
                delay *= RETRY_BACKOFF  # 指数退避
            else:
                logger.error(f"❌ 邮箱轮询失败 [{email_account}]: 已达到最大重试次数 ({MAX_RETRIES})", exc_info=True)
                logger.debug("详细异常信息:", exc_info=True)
                return False  # 明确返回失败状态
    
    return False  # 如果执行到这里，说明所有重试都失败了


