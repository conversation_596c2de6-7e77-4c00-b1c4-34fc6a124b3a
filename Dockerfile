FROM python:3.11-slim


# 设置工作目录
WORKDIR /app



# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt


RUN playwright install --with-deps chromium


# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p screenshots payment_screenshots downloads .prefs test_data results

# 设置环境变量
ENV PYTHONUNBUFFERED=1


# 设置时区为北京时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone


# 不自动运行程序，而是启动一个 shell
CMD : ["uvicorn", "api_main:app", "--host", "0.0.0.0", "--port", "8000"]
