# api/core/dependencies.py
from fastapi import Depends, HTTPException, Request
from fastapi_login import <PERSON>gin<PERSON>anager
from typing import Optional

from ..auth.utils import create_login_manager, load_user
from ..config.settings import settings
from app.core.simple_engine import SimpleEngine


# 全局实例
login_manager: Optional[LoginManager] = None
simple_engine: Optional[SimpleEngine] = None


def get_login_manager() -> LoginManager:
    """获取登录管理器实例"""
    global login_manager
    if login_manager is None:
        login_manager = create_login_manager()
    return login_manager


def get_simple_engine() -> SimpleEngine:
    """获取自动化引擎实例"""
    global simple_engine
    if simple_engine is None:
        simple_engine = SimpleEngine()
    return simple_engine


def get_current_user(request: Request, manager: LoginManager = Depends(get_login_manager)):
    """获取当前登录用户"""
    try:
        token = request.cookies.get(settings.auth_cookie_name)
        if not token:
            raise HTTPException(status_code=401, detail="Not authenticated")
        
        user = load_user(token)
        if not user:
            raise HTTPException(status_code=401, detail="Invalid authentication")
        
        return user
    except Exception as e:
        raise HTTPException(status_code=401, detail="Authentication failed")


def require_auth(user=Depends(get_current_user)):
    """需要认证的依赖"""
    return user


# 可选的认证依赖（用于可选认证的路由）
def optional_auth(request: Request, manager: LoginManager = Depends(get_login_manager)):
    """可选认证依赖"""
    try:
        token = request.cookies.get(settings.auth_cookie_name)
        if token:
            return load_user(token)
        return None
    except Exception:
        return None
