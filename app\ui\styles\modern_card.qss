QWidget {
    background-color: #F3F4F6;
    font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
    font-size: 14px;
    color: #2C2C2C;
}

QGroupBox {
    background-color: #FFFFFF;
    border: 1px solid #DADCE0;
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
}

QLabel {
    color: #2C2C2C;
}

QLineEdit, QComboBox, QDateEdit, QTextEdit {
    background: #FFFFFF;
    border: 1px solid #DADCE0;
    border-radius: 6px;
    padding: 6px;
}

QLineEdit:focus, QComboBox:focus, QDateEdit:focus, QTextEdit:focus {
    border: 1px solid #4CAF50;
}

QPushButton {
    background-color: #4CAF50;
    color: white;
    font-weight: bold;
    border-radius: 6px;
    padding: 8px 16px;
}
QPushButton:hover {
    background-color: #66BB6A;
    border: 1px solid #3e8e41;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
}
QRadioButton::indicator:checked {
    background-color: #4CAF50;
    border: 2px solid #388E3C;
    border-radius: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
}
QCheckBox::indicator:checked {
    background-color: #4CAF50;
    border: 1px solid #388E3C;
    border-radius: 4px;
}

QTabWidget::pane {
    border-top: 2px solid #1976D2;
    top: -1px;
    background: #E3F2FD;
}
QTabBar::tab {
    background: #BBDEFB;
    border: 1px solid #90CAF9;
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}
QTabBar::tab:selected {
    background: #42A5F5;
    color: white;
}

QTableWidget {
    background-color: #FFFFFF;
    border: 1px solid #DADCE0;
    border-radius: 6px;
    gridline-color: #DADCE0;
}

QScrollArea {
    background-color: transparent;
    border: none;
}

/* 控件统一高度压缩 */
QLineEdit, QComboBox, QDateEdit, QTextEdit {
    min-height: 28px;
    font-size: 13px;
    padding: 4px 6px;
}

QPushButton {
    min-height: 32px;
    font-size: 13px;
    padding: 4px 10px;
}

QRadioButton, QCheckBox {
    font-size: 13px;
}

QLineEdit, QComboBox, QDateEdit, QTextEdit {
    font-size: 12px;
    min-height: 22px;
    padding: 1px 3px;
}

QPushButton {
    font-size: 12px;
    min-height: 26px;
    padding: 2px 6px;
}

QRadioButton, QCheckBox {
    font-size: 12px;
    padding: 0px;
    spacing: 2px;
}

QGroupBox QLabel {
    margin-top: 1px;
    margin-bottom: 1px;
}

QRadioButton::indicator {
    width: 14px;
    height: 14px;
    border: 2px solid #4CAF50;
    border-radius: 7px;
    background-color: white;
}

QRadioButton::indicator:checked {
    background-color: #4CAF50;
    border: 2px solid #388E3C;
}

/* 复选框优化（圆角矩形风格） */
QCheckBox::indicator {
    width: 14px;
    height: 14px;
    border: 2px solid #4CAF50;
    border-radius: 4px;
    background-color: white;
}

QCheckBox::indicator:checked {
    background-color: #4CAF50;
    image: url();  /* 移除默认勾选图标 */
}

QCheckBox::indicator:checked {
    background-color: #4CAF50;
    border: 2px solid #388E3C;
    border-radius: 4px;
    box-shadow: 0 0 5px #66FF66;
}

/* ✅ 替换默认勾选图标为 check.png */
QCheckBox::indicator:checked {
    image: url("app/ui/styles/check.png");
    width: 18px;
    height: 18px;
}

/* ✅ 覆盖 QCheckBox 勾选状态图标 */
QCheckBox::indicator:checked {
    image: url("app/ui/styles/check.png");
    width: 18px;
    height: 18px;
}

/* ✅ 覆盖 QRadioButton 勾选状态图标 */
QRadioButton::indicator:checked {
    image: url("app/ui/styles/check.png");
    width: 18px;
    height: 18px;
}

QTabBar::tab {
    height: 10px;              /* 原默认 32px，可根据需求微调 */
    padding: 4px 12px;         /* 减少内边距 */
    font-size: 14px;
    font-weight: 500;
}
/* 下拉框样式优化 */
QComboBox {
    border: 1px solid #CCCCCC;
    border-radius: 4px;
    padding: 4px 8px;
    background-color: white;
    selection-background-color: #4CAF50;
    selection-color: white;
}

QComboBox:hover {
    border: 1px solid #4CAF50;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #CCCCCC;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

QComboBox::down-arrow {
    image: url("app/ui/styles/dropdown_arrow.png");
    width: 12px;
    height: 12px;
}

/* 下拉列表项样式 */
QComboBox QAbstractItemView {
    border: 1px solid #CCCCCC;
    border-radius: 4px;
    background-color: white;
    selection-background-color: #4CAF50;
    selection-color: white;
}

/* 鼠标悬浮在下拉项上的效果 */
QComboBox QAbstractItemView::item:hover {
    background-color: #E8F5E9;
    color: #2E7D32;
    border-radius: 2px;
}

/* 选中项的样式 */
QComboBox QAbstractItemView::item:selected {
    background-color: #4CAF50;
    color: white;
}
