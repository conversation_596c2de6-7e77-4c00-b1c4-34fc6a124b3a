# api/models/responses.py
from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class LoginResponse(BaseResponse):
    """登录响应"""
    redirect_url: Optional[str] = Field(None, description="重定向URL")
    user_info: Optional[Dict[str, Any]] = Field(None, description="用户信息")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "登录成功",
                "redirect_url": "/dashboard",
                "user_info": {
                    "username": "admin",
                    "last_login": "2025-01-01T12:00:00"
                },
                "timestamp": "2025-01-01T12:00:00"
            }
        }


class VisaApplicationResponse(BaseResponse):
    """签证申请响应"""
    application_id: Optional[str] = Field(None, description="申请ID")
    status: Optional[str] = Field(None, description="申请状态")
    tracking_info: Optional[Dict[str, Any]] = Field(None, description="跟踪信息")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "签证申请提交成功",
                "application_id": "E2025012312345678",
                "status": "submitted",
                "tracking_info": {
                    "submission_time": "2025-01-01T12:00:00",
                    "expected_processing_days": "3-5"
                },
                "timestamp": "2025-01-01T12:00:00"
            }
        }


class OCRResponse(BaseResponse):
    """OCR识别响应"""
    fields: Optional[Dict[str, Any]] = Field(None, description="识别出的字段")
    confidence: Optional[float] = Field(None, description="识别置信度")
    processing_time: Optional[float] = Field(None, description="处理时间(秒)")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "OCR识别成功",
                "fields": {
                    "passport_number": "E12345678",
                    "surname": "ZHANG",
                    "given_name": "SAN",
                    "date_of_birth": "01/01/1990",
                    "date_of_issue": "01/01/2020",
                    "date_of_expiry": "01/01/2030"
                },
                "confidence": 0.95,
                "processing_time": 2.5,
                "timestamp": "2025-01-01T12:00:00"
            }
        }


class FileUploadResponse(BaseResponse):
    """文件上传响应"""
    file_id: Optional[str] = Field(None, description="文件ID")
    file_path: Optional[str] = Field(None, description="文件路径")
    file_size: Optional[int] = Field(None, description="文件大小(字节)")
    file_type: Optional[str] = Field(None, description="文件类型")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "文件上传成功",
                "file_id": "f_20250101_120000_001",
                "file_path": "/uploads/passport_scan.jpg",
                "file_size": 1024000,
                "file_type": "image/jpeg",
                "timestamp": "2025-01-01T12:00:00"
            }
        }


class HealthResponse(BaseResponse):
    """健康检查响应"""
    status: str = Field(..., description="服务状态")
    version: str = Field(..., description="版本号")
    uptime: Optional[str] = Field(None, description="运行时间")
    dependencies: Optional[Dict[str, str]] = Field(None, description="依赖服务状态")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "服务运行正常",
                "status": "healthy",
                "version": "1.0.0",
                "uptime": "2 days, 3 hours, 45 minutes",
                "dependencies": {
                    "database": "connected",
                    "ocr_service": "available",
                    "email_service": "running"
                },
                "timestamp": "2025-01-01T12:00:00"
            }
        }


class ErrorResponse(BaseResponse):
    """错误响应"""
    error_code: Optional[str] = Field(None, description="错误代码")
    error_type: Optional[str] = Field(None, description="错误类型")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")

    class Config:
        schema_extra = {
            "example": {
                "success": False,
                "message": "请求处理失败",
                "error_code": "VALIDATION_ERROR",
                "error_type": "validation_error",
                "details": {
                    "field": "email",
                    "issue": "Invalid email format"
                },
                "timestamp": "2025-01-01T12:00:00"
            }
        }


class ListResponse(BaseResponse):
    """列表响应"""
    data: List[Dict[str, Any]] = Field(default_factory=list, description="数据列表")
    total: int = Field(default=0, description="总数量")
    page: Optional[int] = Field(None, description="当前页码")
    page_size: Optional[int] = Field(None, description="每页大小")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "数据获取成功",
                "data": [
                    {"id": 1, "name": "申请1"},
                    {"id": 2, "name": "申请2"}
                ],
                "total": 2,
                "page": 1,
                "page_size": 10,
                "timestamp": "2025-01-01T12:00:00"
            }
        }
