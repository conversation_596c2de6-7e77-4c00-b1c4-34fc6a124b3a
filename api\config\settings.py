# api/config/settings.py
import os
from pathlib import Path
from typing import Dict, List


class Settings:
    """应用配置 - 简化版本，直接使用环境变量"""

    def __init__(self):
        # 基础配置
        self.app_name = "Vietnam E-Visa Automator API"
        self.app_version = "1.0.0"
        self.debug = os.getenv("DEBUG", "false").lower() == "true"

        # 安全配置
        self.secret_key = os.getenv("SECRET_KEY", "your-very-secret-key")

        # 服务器配置
        self.host = os.getenv("HOST", "0.0.0.0")
        self.port = int(os.getenv("PORT", "8000"))
        self.reload = self.debug

        # 认证配置
        self.auth_cookie_name = os.getenv("AUTH_COOKIE_NAME", "auth_token")
        self.auth_token_expire_hours = int(os.getenv("AUTH_TOKEN_EXPIRE_HOURS", "1"))
        self.max_failed_attempts = int(os.getenv("MAX_FAILED_ATTEMPTS", "10"))
        self.account_lock_hours = int(os.getenv("ACCOUNT_LOCK_HOURS", "100000"))

        # 文件配置
        self.max_file_size = int(os.getenv("MAX_FILE_SIZE", str(10 * 1024 * 1024)))  # 10MB
        self.allowed_file_types = os.getenv("ALLOWED_FILE_TYPES", "image/jpeg,image/png").split(",")

        # 日志配置
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.log_dir = os.getenv("LOG_DIR", "logs/fastapi")
        self.log_filename_prefix = os.getenv("LOG_FILENAME_PREFIX", "vietnam_evisa_api")

        # 数据库配置
        self.database_type = os.getenv("DATABASE_TYPE", "sqlite")  # sqlite 或 postgresql
        self.database_url = os.getenv("DATABASE_URL", "sqlite:///./results/visa_results.db")

        # PostgreSQL配置
        self.postgres_host = os.getenv("POSTGRES_HOST", "localhost")
        self.postgres_port = int(os.getenv("POSTGRES_PORT", "5432"))
        self.postgres_db = os.getenv("POSTGRES_DB", "visa_automator")
        self.postgres_user = os.getenv("POSTGRES_USER", "visa_user")
        self.postgres_password = os.getenv("POSTGRES_PASSWORD", "visa_password_2024")

        # SQLite配置
        self.sqlite_path = os.getenv("SQLITE_PATH", "results/visa_results.db")

        # Redis配置
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")

        # OCR配置
        self.ocr_temp_dir = os.getenv("OCR_TEMP_DIR", "temp/ocr")


class UserConfig:
    """用户配置管理"""

    def __init__(self):
        self.users: Dict[str, Dict] = {}
        self._load_users_from_env()

    def _load_users_from_env(self):
        """从环境变量加载用户数据"""
        api_users = os.getenv("API_BASIC_USERS", "").split(",")
        api_passwords = os.getenv("API_BASIC_PASSWORDS", "").split(",")

        if len(api_users) != len(api_passwords):
            raise ValueError("Number of users and passwords must match")

        for username, password in zip(api_users, api_passwords):
            username = username.strip()
            password = password.strip()
            if username and password:
                self.users[username] = {
                    "password": password,
                    "failed_attempts": 0,
                    "locked_until": None
                }

    def get_user(self, username: str) -> Dict:
        """获取用户信息"""
        return self.users.get(username)

    def update_user(self, username: str, data: Dict):
        """更新用户信息"""
        if username in self.users:
            self.users[username].update(data)


# 全局配置实例
settings = Settings()
user_config = UserConfig()

# 路径配置
BASE_DIR = Path(__file__).resolve().parent.parent.parent
STATIC_DIR = BASE_DIR / "static"
TEMPLATES_DIR = BASE_DIR / "templates"
TEMP_DIR = BASE_DIR / "temp"

# 确保目录存在
STATIC_DIR.mkdir(exist_ok=True)
TEMP_DIR.mkdir(exist_ok=True)
