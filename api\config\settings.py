# api/config/settings.py
import os
from pathlib import Path
from typing import Dict, List
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    app_name: str = "Vietnam E-Visa Automator API"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 安全配置
    secret_key: str = Field(..., env="SECRET_KEY")
    
    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 8000
    reload: bool = False
    
    # 认证配置
    auth_cookie_name: str = "auth_token"
    auth_token_expire_hours: int = 1
    max_failed_attempts: int = 10
    account_lock_hours: int = 100000
    
    # 文件配置
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    allowed_file_types: List[str] = ["image/jpeg", "image/png"]
    
    # 日志配置
    log_level: str = "INFO"
    log_dir: str = "logs/fastapi"
    log_filename_prefix: str = "vietnam_evisa_api"
    
    # 数据库配置
    database_url: str = "sqlite:///./results/visa_results.db"
    
    # OCR配置
    ocr_temp_dir: str = "temp/ocr"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


class UserConfig:
    """用户配置管理"""
    
    def __init__(self):
        self.users: Dict[str, Dict] = {}
        self._load_users_from_env()
    
    def _load_users_from_env(self):
        """从环境变量加载用户数据"""
        api_users = os.getenv("API_BASIC_USERS", "").split(",")
        api_passwords = os.getenv("API_BASIC_PASSWORDS", "").split(",")
        
        if len(api_users) != len(api_passwords):
            raise ValueError("Number of users and passwords must match")
        
        for username, password in zip(api_users, api_passwords):
            username = username.strip()
            password = password.strip()
            if username and password:
                self.users[username] = {
                    "password": password,
                    "failed_attempts": 0,
                    "locked_until": None
                }
    
    def get_user(self, username: str) -> Dict:
        """获取用户信息"""
        return self.users.get(username)
    
    def update_user(self, username: str, data: Dict):
        """更新用户信息"""
        if username in self.users:
            self.users[username].update(data)


# 全局配置实例
settings = Settings()
user_config = UserConfig()

# 路径配置
BASE_DIR = Path(__file__).resolve().parent.parent.parent
STATIC_DIR = BASE_DIR / "static"
TEMPLATES_DIR = BASE_DIR / "templates"
TEMP_DIR = BASE_DIR / "temp"

# 确保目录存在
STATIC_DIR.mkdir(exist_ok=True)
TEMP_DIR.mkdir(exist_ok=True)
