from app.utils.logger_config import get_logger
from typing import Optional
import email.header

logger = get_logger()

def determine_email_type(sender: str, subject: str, body: str) -> Optional[str]:
    """
    确定邮件类型，忽略大小写以提高容错率，并记录详细日志
    返回: "payment", "submission", "result" 或 "unknown"
    """
     # 解码MIME编码的主题
    try:
        # 如果主题包含MIME编码(=?utf-8?Q?...)
        if "=?" in subject and "?=" in subject:
            decoded_subject, _ = email.header.decode_header(subject)[0]
            if isinstance(decoded_subject, bytes):
                subject = decoded_subject.decode('utf-8', errors='replace')
    except Exception as e:
        logger.warning(f"解码邮件主题失败: {e}")

   
    # 转换为小写以忽略大小写
    sender_lower = sender.lower()
    subject_lower = subject.lower()
    body_lower = body.lower()

    # 提取邮件主题的前100个字符用于日志记录
    subject_preview = subject[:100] + "..." if len(subject) > 100 else subject

    logger.info(f"处理邮件: 发件人={sender}, 解码后主题={subject_preview}")

    # 1. 付款成功通知邮件 - 基于精确的发件人和主题格式（忽略大小写）
    if "<EMAIL>" in sender_lower and "payment confirmation" in subject_lower:
        logger.info(f"📨 ✅识别为: 付款成功通知邮件: {subject_preview}")
        return "payment"
    
    # 2. 来自越南移民局的邮件
    if "immigration.gov.vn" in sender_lower:
        # 出签状态通知邮件
        if "has been processed" in body_lower:
            logger.info(f"📨 ✅识别为: 出签状态通知邮件: {subject_preview}")
            return "result"
        
        # 提交成功通知邮件
        if ("we have received your e-visa application" in body_lower or 
            "proceed with the e-visa fee payment" in body_lower):
            logger.info(f"📨 ✅识别为: 提交成功通知邮件: {subject_preview}")
            return "submission"
    
    # 无法确定类型，记录警告日志
    logger.warning(f"⚠️ 无法确定邮件类型: 发件人={sender}, 主题={subject_preview} 内容={body[:100]}... 请手动检查此邮件")
    return "unknown"