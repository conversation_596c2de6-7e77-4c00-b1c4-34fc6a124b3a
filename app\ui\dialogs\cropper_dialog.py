# 文件: app/ui/dialogs/cropper_dialog.py

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QGraphicsView,
    QGraphicsScene, QGraphicsPixmapItem
)
from PyQt6.QtGui import QPixmap
from PyQt6.QtCore import Qt
from pathlib import Path
from PyQt6.QtGui import QPainter 
from PyQt6.QtWidgets import QGraphicsRectItem
from PyQt6.QtGui import QPen, QColor, QBrush
from PyQt6.QtCore import QRectF,QPointF
from PyQt6.QtCore import QRectF, Qt, QPointF
from PyQt6.QtGui import QPen, QColor, QBrush, QPainter
from PyQt6.QtCore import QSizeF


class HandleItem(QGraphicsRectItem):
    def __init__(self, parent, position: str, size: float = 10):
        super().__init__(-size/2, -size/2, size, size, parent)
        self.setFlag(QGraphicsRectItem.GraphicsItemFlag.ItemSendsGeometryChanges)
        self.position = position
        self.size = size
        self.setBrush(QBrush(Qt.GlobalColor.white))
        self.setPen(QPen(QColor("red")))
        self.setZValue(2)
        self.setFlags(
            QGraphicsRectItem.GraphicsItemFlag.ItemIsMovable |
            QGraphicsRectItem.GraphicsItemFlag.ItemSendsGeometryChanges
        )
        self.setCursor({
            'top_left': Qt.CursorShape.SizeFDiagCursor,
            'top_right': Qt.CursorShape.SizeBDiagCursor,
            'bottom_left': Qt.CursorShape.SizeBDiagCursor,
            'bottom_right': Qt.CursorShape.SizeFDiagCursor,
            'top': Qt.CursorShape.SizeVerCursor,
            'bottom': Qt.CursorShape.SizeVerCursor,
            'left': Qt.CursorShape.SizeHorCursor,
            'right': Qt.CursorShape.SizeHorCursor,
        }[position])

    def itemChange(self, change, value):
        if change == QGraphicsRectItem.GraphicsItemChange.ItemPositionChange:
            parent = self.parentItem()
            if isinstance(parent, QGraphicsRectItem):
                new_pos = value
                rect = parent.rect()
                mapped = parent.mapFromScene(new_pos)

                if self.position == 'top_left':
                    rect.setTopLeft(mapped)
                elif self.position == 'top':
                    rect.setTop(mapped.y())
                elif self.position == 'top_right':
                    rect.setTopRight(mapped)
                elif self.position == 'right':
                    rect.setRight(mapped.x())
                elif self.position == 'bottom_right':
                    rect.setBottomRight(mapped)
                elif self.position == 'bottom':
                    rect.setBottom(mapped.y())
                elif self.position == 'bottom_left':
                    rect.setBottomLeft(mapped)
                elif self.position == 'left':
                    rect.setLeft(mapped.x())

                parent.prepareGeometryChange()
                parent.setRect(rect.normalized())
                parent._update_handle_positions()
        return super().itemChange(change, value)



#支持 8个方向控制点缩放（四角 + 四边）
class ResizableRectItem(QGraphicsRectItem):
    VISIBLE_HANDLE_SIZE = 10.0
    HANDLE_SIZE = 10.0
    HANDLE_SPACE = 2.0
    HANDLE_CURSORS = {
        'top_left': Qt.CursorShape.SizeFDiagCursor,
        'top': Qt.CursorShape.SizeVerCursor,
        'top_right': Qt.CursorShape.SizeBDiagCursor,
        'right': Qt.CursorShape.SizeHorCursor,
        'bottom_right': Qt.CursorShape.SizeFDiagCursor,
        'bottom': Qt.CursorShape.SizeVerCursor,
        'bottom_left': Qt.CursorShape.SizeBDiagCursor,
        'left': Qt.CursorShape.SizeHorCursor,
    }

    def __init__(self, rect: QRectF):
        super().__init__(rect)
        self.setFlags(
            QGraphicsRectItem.GraphicsItemFlag.ItemIsSelectable |
            QGraphicsRectItem.GraphicsItemFlag.ItemIsMovable
        )
        self.setPen(QPen(QColor("red"), 2, Qt.PenStyle.DashLine))
        self.setBrush(QBrush(Qt.GlobalColor.transparent))
        self.setZValue(1)
        self.setAcceptHoverEvents(True)
        self.setFlag(QGraphicsRectItem.GraphicsItemFlag.ItemIsFocusable, True)
        self.handles = {}
        for pos in ['top_left', 'top', 'top_right', 'right',
                    'bottom_right', 'bottom', 'bottom_left', 'left']:
            handle = HandleItem(self, pos)
            self.handles[pos] = handle
        self._update_handles_pos()
         # 初始化鼠标事件相关的属性
        self._current_handle = None
        self._mouse_press_pos = None
        self._mouse_press_rect = None

    def _update_handle_positions(self):
        r = self.rect()
        centers = {
            'top_left': r.topLeft(),
            'top': r.center().toPointF().setY(r.top()),
            'top_right': r.topRight(),
            'right': r.center().toPointF().setX(r.right()),
            'bottom_right': r.bottomRight(),
            'bottom': r.center().toPointF().setY(r.bottom()),
            'bottom_left': r.bottomLeft(),
            'left': r.center().toPointF().setX(r.left()),
        }
        for pos, handle in self.handles.items():
            handle.setPos(centers[pos])


    def _update_handles_pos(self):
        r = self.rect()
        s = self.HANDLE_SIZE * 2  # 命中判定范围扩大（例如 20x20）

        self.handles = {
            'top_left': QRectF(r.topLeft().x() - s/2, r.topLeft().y() - s/2, s, s),
            'top': QRectF(r.center().x() - s/2, r.top() - s/2, s, s),
            'top_right': QRectF(r.topRight().x() - s/2, r.topRight().y() - s/2, s, s),
            'right': QRectF(r.right() - s/2, r.center().y() - s/2, s, s),
            'bottom_right': QRectF(r.bottomRight().x() - s/2, r.bottomRight().y() - s/2, s, s),
            'bottom': QRectF(r.center().x() - s/2, r.bottom() - s/2, s, s),
            'bottom_left': QRectF(r.bottomLeft().x() - s/2, r.bottomLeft().y() - s/2, s, s),
            'left': QRectF(r.left() - s/2, r.center().y() - s/2, s, s),
    }

        

    def paint(self, painter: QPainter, option, widget=None):
        super().paint(painter, option, widget)
        self._update_handles_pos()

        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setPen(QPen(QColor("red")))
        painter.setBrush(QBrush(QColor("white")))

        visible_size = 10  # 显示视觉尺寸更小
        for rect in self.handles.values():
            center = rect.center()
            painter.drawRect(QRectF(
                center.x() - visible_size / 2,
                center.y() - visible_size / 2,
                visible_size,
                visible_size
            ))


    def hoverMoveEvent(self, event):
        for key, handle_rect in self.handles.items():
            if handle_rect.contains(event.pos()):
                self.setCursor(self.HANDLE_CURSORS[key])
                return
        self.setCursor(Qt.CursorShape.ArrowCursor)
        super().hoverMoveEvent(event)

    def mousePressEvent(self, event):
        for key, rect in self.handles.items():
            if rect.contains(event.pos()):
                self._current_handle = key
                self._mouse_press_pos = event.pos()
                self._mouse_press_rect = self.rect()
                event.accept()
                return
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        if self._current_handle:
            diff = event.pos() - self._mouse_press_pos
            rect = QRectF(self._mouse_press_rect)

            if self._current_handle == 'top_left':
                rect.setTopLeft(rect.topLeft() + diff)
            elif self._current_handle == 'top':
                rect.setTop(rect.top() + diff.y())
            elif self._current_handle == 'top_right':
                rect.setTopRight(rect.topRight() + diff)
            elif self._current_handle == 'right':
                rect.setRight(rect.right() + diff.x())
            elif self._current_handle == 'bottom_right':
                rect.setBottomRight(rect.bottomRight() + diff)
            elif self._current_handle == 'bottom':
                rect.setBottom(rect.bottom() + diff.y())
            elif self._current_handle == 'bottom_left':
                rect.setBottomLeft(rect.bottomLeft() + diff)
            elif self._current_handle == 'left':
                rect.setLeft(rect.left() + diff.x())

            # 限制最小尺寸
            if rect.width() >= 20 and rect.height() >= 20:
                self.prepareGeometryChange() #消除重影
                self.setRect(rect.normalized())
            event.accept()
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        self._current_handle = None
        super().mouseReleaseEvent(event)

    def hoverEnterEvent(self, event):
        self.setAcceptHoverEvents(True)
        super().hoverEnterEvent(event)

    def hoverLeaveEvent(self, event):
        self.setCursor(Qt.CursorShape.ArrowCursor)
        super().hoverLeaveEvent(event)


class CropperDialog(QDialog):
    def __init__(self, image_path: str, parent=None):
        super().__init__(parent)
        self.setWindowTitle("手动裁剪护照图像")
        self.setMinimumSize(800, 600)

        self.image_path = image_path
        self.original_pixmap = QPixmap(image_path)

        # 初始化界面组件
        self.init_ui()

    def init_ui(self):
        # 主布局
        main_layout = QVBoxLayout(self)

        # 图像区域
        self.graphics_view = QGraphicsView()
        self.graphics_view.setRenderHint(QPainter.RenderHint.Antialiasing)
        self.scene = QGraphicsScene(self)
        self.graphics_view.setScene(self.scene)

        # 加载图像
        self.pixmap_item = QGraphicsPixmapItem(self.original_pixmap)
        self.scene.addItem(self.pixmap_item)

        # 裁剪框初始大小（可调）
        rect = QRectF(self.original_pixmap.rect()).adjusted(50, 50, -50, -50)
        self.crop_rect_item = ResizableRectItem(rect)
        self.crop_rect_item.setFlag(QGraphicsRectItem.GraphicsItemFlag.ItemIsMovable, True)
        self.crop_rect_item.setFlag(QGraphicsRectItem.GraphicsItemFlag.ItemIsSelectable, True)
        self.crop_rect_item.setPen(QPen(QColor("red"), 2, Qt.PenStyle.DashLine))
        self.scene.addItem(self.crop_rect_item)

        # 按钮区域
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("确认裁剪")
        self.cancel_button = QPushButton("取消")
        self.ok_button.clicked.connect(self.accept)
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addStretch()
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)

        # 加入布局
        main_layout.addWidget(self.graphics_view)
        main_layout.addLayout(button_layout)

    def get_original_pixmap(self) -> QPixmap:
        return self.original_pixmap


    def get_cropped_pixmap(self) -> QPixmap:
        
        '''导出裁剪区域对应的图像 Pixmap'''

        rect = self.crop_rect_item.rect()
        top_left = self.crop_rect_item.scenePos()
        crop_rect = rect.translated(top_left)  # 将相对坐标变为场景绝对坐标

        # 映射到原始图像坐标（比例一致）
        x_ratio = self.original_pixmap.width() / self.pixmap_item.boundingRect().width()
        y_ratio = self.original_pixmap.height() / self.pixmap_item.boundingRect().height()

        crop_x = int(crop_rect.x() * x_ratio)
        crop_y = int(crop_rect.y() * y_ratio)
        crop_w = int(crop_rect.width() * x_ratio)
        crop_h = int(crop_rect.height() * y_ratio)

        return self.original_pixmap.copy(crop_x, crop_y, crop_w, crop_h)
    
    def wheelEvent(self, event):
        factor = 1.15 if event.angleDelta().y() > 0 else 1 / 1.15
        self.graphics_view.scale(factor, factor)
    
    def boundingRect(self) -> QRectF:
        rect = self.rect()
        margin = self.HANDLE_SIZE * 3.0   #✅ 扩大覆盖范围
        return rect.adjusted(-margin, -margin, margin, margin)


