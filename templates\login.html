<!DOCTYPE html>
<html>
<head>
    <title>Vietnam E-Visa Login</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --background-color: #f8fafc;
            --text-color: #1f2937;
            --border-radius: 12px;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --spacing-unit: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .login-container {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            width: 100%;
            max-width: 400px;
            margin: 2rem;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header h1 {
            color: var(--primary-color);
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: #6b7280;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-color);
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #e5e7eb;
            border-radius: var(--border-radius);
            background: white;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-sm);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
        }

        .error-message {
            color: #ef4444;
            font-size: 0.875rem;
            margin-top: 1rem;
            text-align: center;
            display: none;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .remember-me label {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .login-actions {
            margin-top: 1.5rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .login-actions button {
            padding: 1rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-sm);
        }

        .login-actions button:hover {
            background-color: var(--secondary-color);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .login-actions button:active {
            transform: translateY(0);
        }

        .login-actions .forgot-password {
            text-align: right;
            font-size: 0.875rem;
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .login-actions .forgot-password:hover {
            color: var(--secondary-color);
        }

        .login-actions .register-link {
            text-align: center;
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 1rem;
        }

        .login-actions .register-link a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .login-actions .register-link a:hover {
            color: var(--secondary-color);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>电子签证自动化系统</h1>
            <p>Sign in to continue</p>
        </div>

        <form id="loginForm" method="post" action="/login">
            <div class="error-message" id="errorMessage"></div>

            <div class="form-group">
                <label for="username">Username 账号</label>
                <input type="text" id="username" name="username" required>
            </div>

            <div class="form-group">
                <label for="password">Password 密码</label>
                <input type="password" id="password" name="password" required>
            </div>

            <div class="remember-me">
                <input type="checkbox" id="rememberMe" name="remember_me">
                <label for="rememberMe">Remember me</label>
            </div>

            <div class="login-actions">
                <button type="submit">Sign In 登录</button>
                <a href="#" class="forgot-password">Forgot Password? 忘记密码？</a>
                <p class="register-link">
                    Don't have an account? <a href="#">Register 注册</a>
                </p>
            </div>
        </form>

        <script>
            // 显示URL中的错误消息
            const params = new URLSearchParams(window.location.search);
            if (params.has('error')) {
                document.getElementById('errorMessage').textContent = params.get('error');
                document.getElementById('errorMessage').style.display = 'block';
            }

            // 表单验证
            document.getElementById('loginForm').addEventListener('submit', (e) => {
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                if (!username || !password) {
                    e.preventDefault();
                    document.getElementById('errorMessage').textContent = 'Please enter both username and password';
                    document.getElementById('errorMessage').style.display = 'block';
                }
            });
        </script>
    </div>
</body>
</html>