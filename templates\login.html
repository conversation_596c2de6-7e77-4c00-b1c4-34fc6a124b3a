<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Vietnam E-Visa Application System - Secure Login">
    <title>Vietnam E-Visa Login</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --background-color: #f8fafc;
            --text-color: #1f2937;
            --border-radius: 12px;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --spacing-unit: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>I', <PERSON><PERSON>, <PERSON><PERSON>gen, <PERSON>buntu, <PERSON><PERSON>ell, sans-serif;
            background:
                radial-gradient(ellipse at top, #0f1419 0%, #1a1a2e 30%, #16213e 60%, #0f0f23 85%, #000000 100%);
            color: var(--text-color);
            line-height: 1.6;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        /* 星云效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 20% 30%, rgba(75, 0, 130, 0.15) 0%, transparent 60%),
                radial-gradient(circle at 80% 70%, rgba(25, 25, 112, 0.12) 0%, transparent 50%),
                radial-gradient(circle at 60% 20%, rgba(72, 61, 139, 0.08) 0%, transparent 70%),
                radial-gradient(circle at 30% 80%, rgba(138, 43, 226, 0.1) 0%, transparent 55%);
            background-size: 100% 100%;
            animation: nebula 50s ease-in-out infinite alternate;
            pointer-events: none;
        }

        /* 星空效果 - 第一层 */
        body::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.9), transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.7), transparent),
                radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.5), transparent),
                radial-gradient(2px 2px at 160px 30px, rgba(200,200,255,0.6), transparent),
                radial-gradient(1px 1px at 300px 300px, rgba(255,255,255,0.4), transparent),
                radial-gradient(2px 2px at 350px 100px, rgba(255,255,255,0.6), transparent),
                radial-gradient(1px 1px at 250px 200px, rgba(255,255,255,0.7), transparent);
            background-repeat: repeat;
            background-size: 400px 300px;
            animation: sparkle 30s linear infinite;
            pointer-events: none;
        }

        /* 星云动画 */
        @keyframes nebula {
            0% {
                transform: scale(1) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                transform: scale(1.1) rotate(180deg);
                opacity: 0.9;
            }
            100% {
                transform: scale(1) rotate(360deg);
                opacity: 0.7;
            }
        }

        /* 星空闪烁动画 */
        @keyframes sparkle {
            from { transform: translateX(0) translateY(0); }
            to { transform: translateX(-400px) translateY(-100px); }
        }

        /* 额外的星星层 */
        .stars-layer {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(1px 1px at 50px 50px, rgba(255,255,255,0.3), transparent),
                radial-gradient(1px 1px at 100px 100px, rgba(255,255,255,0.25), transparent),
                radial-gradient(1px 1px at 150px 150px, rgba(255,255,255,0.4), transparent),
                radial-gradient(1px 1px at 200px 200px, rgba(255,255,255,0.2), transparent),
                radial-gradient(2px 2px at 250px 50px, rgba(255,255,255,0.5), transparent),
                radial-gradient(1px 1px at 320px 180px, rgba(173,216,230,0.4), transparent),
                radial-gradient(1px 1px at 80px 280px, rgba(255,255,255,0.3), transparent);
            background-repeat: repeat;
            background-size: 380px 380px;
            animation: sparkle 40s linear infinite reverse;
            pointer-events: none;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(25px) saturate(180%);
            -webkit-backdrop-filter: blur(25px) saturate(180%);
            padding: 3.5rem 3rem;
            border-radius: 20px;
            box-shadow:
                0 25px 50px -12px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                inset 0 0 20px rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.15);
            width: 100%;
            max-width: 520px;
            margin: 2rem;
            position: relative;
            z-index: 10;
        }

        /* 增强毛玻璃效果的内容区域 */
        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            z-index: -1;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .login-header h1 {
            color: #ffffff;
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            letter-spacing: -0.025em;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .login-header p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            font-weight: 400;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .form-group input {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 1px solid #e5e7eb;
            border-radius: var(--border-radius);
            background: white;
            font-size: 1rem;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-sm);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
        }

        .error-message {
            color: #ef4444;
            font-size: 0.875rem;
            margin-top: 1rem;
            text-align: center;
            display: none;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .remember-me input[type="checkbox"] {
            width: 1.25rem;
            height: 1.25rem;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            accent-color: var(--primary-color);
        }

        .remember-me label {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .login-actions {
            margin-top: 2rem;
        }

        .login-actions button {
            width: 100%;
            padding: 1rem 1.5rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-sm);
            margin-bottom: 1.5rem;
        }

        .login-actions button:hover {
            background-color: var(--secondary-color);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .login-actions button:active {
            transform: translateY(0);
        }

        .secondary-actions {
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #f3f4f6;
            text-align: center;
        }

        .forgot-password {
            display: block;
            font-size: 0.875rem;
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.2s ease;
            margin-bottom: 1rem;
        }

        .forgot-password:hover {
            color: var(--secondary-color);
        }

        .register-link {
            font-size: 0.875rem;
            color: #6b7280;
            display: block;
        }

        .register-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .register-link a:hover {
            color: var(--secondary-color);
        }

        /* Screen reader only class for accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Remove default fieldset styling */
        fieldset {
            border: none;
            padding: 0;
            margin: 0;
        }

        /* Responsive design */
        @media (max-width: 640px) {
            body {
                padding: 1rem;
            }

            .login-container {
                margin: 0;
                padding: 2.5rem 2rem;
                max-width: none;
                border-radius: 16px;
            }

            .login-header h1 {
                font-size: 1.5rem;
            }

            .login-header {
                margin-bottom: 2rem;
            }

            .secondary-actions {
                margin-top: 1rem;
                padding-top: 1rem;
            }

            .forgot-password {
                margin-bottom: 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 2rem 1.5rem;
                border-radius: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="stars-layer"></div>
    <div class="login-container">
        <div class="login-header">
            <h1>电子签证自动化系统</h1>
            <p>Sign in to continue</p>
        </div>

        <form id="loginForm" method="post" action="/login">
            <div class="error-message" id="errorMessage" role="alert" aria-live="polite"></div>

            <fieldset>
                <legend class="sr-only">Login Credentials</legend>
                <div class="form-group">
                    <label for="username">Username 账号</label>
                    <input type="text" id="username" name="username" required aria-describedby="username-help">
                </div>

                <div class="form-group">
                    <label for="password">Password 密码</label>
                    <input type="password" id="password" name="password" required aria-describedby="password-help">
                </div>

                <div class="remember-me">
                    <input type="checkbox" id="rememberMe" name="remember_me">
                    <label for="rememberMe">Remember me 记住账号密码</label>
                </div>
            </fieldset>

            <div class="login-actions">
                <button type="submit">Sign In 登录</button>

                <div class="secondary-actions">
                    <a href="#" class="forgot-password">Forgot Password? 忘记密码？</a>
                    <div class="register-link">
                    <a href="#">Register 注册</a>
                    </div>
                </div>
            </div>
        </form>

        <script>
            // 显示URL中的错误消息
            const params = new URLSearchParams(window.location.search);
            if (params.has('error')) {
                document.getElementById('errorMessage').textContent = params.get('error');
                document.getElementById('errorMessage').style.display = 'block';
            }

            // 表单验证
            document.getElementById('loginForm').addEventListener('submit', (e) => {
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                if (!username || !password) {
                    e.preventDefault();
                    document.getElementById('errorMessage').textContent = 'Please enter both username and password';
                    document.getElementById('errorMessage').style.display = 'block';
                }
            });
        </script>
    </div>
</body>
</html>