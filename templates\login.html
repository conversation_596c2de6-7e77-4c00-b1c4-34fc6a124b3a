<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Vietnam E-Visa Application System - Secure Login">
    <title>Vietnam E-Visa Login</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --background-color: #f8fafc;
            --text-color: #1f2937;
            --border-radius: 12px;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --spacing-unit: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>I', <PERSON><PERSON>, <PERSON><PERSON>gen, <PERSON>buntu, <PERSON><PERSON><PERSON>, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .login-container {
            background: white;
            padding: 3rem 2.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            width: 100%;
            max-width: 480px;
            margin: 2rem;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .login-header h1 {
            color: var(--primary-color);
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            letter-spacing: -0.025em;
        }

        .login-header p {
            color: #6b7280;
            font-size: 1rem;
            font-weight: 400;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-color);
        }

        .form-group input {
            width: 100%;
            padding: 1rem 1.25rem;
            border: 1px solid #e5e7eb;
            border-radius: var(--border-radius);
            background: white;
            font-size: 1rem;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-sm);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
        }

        .error-message {
            color: #ef4444;
            font-size: 0.875rem;
            margin-top: 1rem;
            text-align: center;
            display: none;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .remember-me label {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .login-actions {
            margin-top: 2rem;
        }

        .login-actions button {
            width: 100%;
            padding: 1rem 1.5rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-sm);
            margin-bottom: 1.5rem;
        }

        .login-actions button:hover {
            background-color: var(--secondary-color);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .login-actions button:active {
            transform: translateY(0);
        }

        .secondary-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #f3f4f6;
        }

        .forgot-password {
            font-size: 0.875rem;
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .forgot-password:hover {
            color: var(--secondary-color);
        }

        .register-link {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .register-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .register-link a:hover {
            color: var(--secondary-color);
        }

        /* Screen reader only class for accessibility */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Remove default fieldset styling */
        fieldset {
            border: none;
            padding: 0;
            margin: 0;
        }

        /* Responsive design */
        @media (max-width: 640px) {
            .login-container {
                margin: 1rem;
                padding: 2rem 1.5rem;
                max-width: none;
            }

            .login-header h1 {
                font-size: 1.5rem;
            }

            .secondary-actions {
                flex-direction: column;
                gap: 0.75rem;
                text-align: center;
            }

            .forgot-password {
                order: 2;
            }

            .register-link {
                order: 1;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>电子签证自动化系统</h1>
            <p>Sign in to continue</p>
        </div>

        <form id="loginForm" method="post" action="/login">
            <div class="error-message" id="errorMessage" role="alert" aria-live="polite"></div>

            <fieldset>
                <legend class="sr-only">Login Credentials</legend>
                <div class="form-group">
                    <label for="username">Username 账号</label>
                    <input type="text" id="username" name="username" required aria-describedby="username-help">
                </div>

                <div class="form-group">
                    <label for="password">Password 密码</label>
                    <input type="password" id="password" name="password" required aria-describedby="password-help">
                </div>

                <div class="remember-me">
                    <input type="checkbox" id="rememberMe" name="remember_me">
                    <label for="rememberMe">Remember me</label>
                </div>
            </fieldset>

            <div class="login-actions">
                <button type="submit">Sign In 登录</button>

                <div class="secondary-actions">
                    <a href="#" class="forgot-password">Forgot Password? 忘记密码？</a>
                    <span class="register-link">
                        Don't have an account? <a href="#">Register 注册</a>
                    </span>
                </div>
            </div>
        </form>

        <script>
            // 显示URL中的错误消息
            const params = new URLSearchParams(window.location.search);
            if (params.has('error')) {
                document.getElementById('errorMessage').textContent = params.get('error');
                document.getElementById('errorMessage').style.display = 'block';
            }

            // 表单验证
            document.getElementById('loginForm').addEventListener('submit', (e) => {
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                if (!username || !password) {
                    e.preventDefault();
                    document.getElementById('errorMessage').textContent = 'Please enter both username and password';
                    document.getElementById('errorMessage').style.display = 'block';
                }
            });
        </script>
    </div>
</body>
</html>