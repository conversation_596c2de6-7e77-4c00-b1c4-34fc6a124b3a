import threading, time
from app.email.email_executor import run_single_polling
from app.data.models import Applicant
from app.utils.logger_config import get_logger

logger = get_logger()

def confirm_payment_arrived(applicant: Applicant, max_wait=120):
    """
    在付款提交后，执行两次额外的邮件检查以快速捕获付款确认邮件
    
    Args:
        applicant: 申请人对象
        max_wait: 两次检查之间的等待时间（秒）
    """
    logger.debug(f"启动付款后邮件确认流程 - 申请人: {applicant.email}, 等待时间: {max_wait}秒")
    
    def _delayed_check():
        try:
            # 第一次检查：20秒后
            logger.debug(f"等待20秒后进行首次轮询 - 申请人: {applicant.chinese_name},{applicant.email}")
            time.sleep(20)
            logger.info(f"🔍 启动基于事件(付款后)触发的首次邮箱轮询: {applicant.chinese_name},{applicant.email}")
            # 使用线程池执行轮询，避免阻塞
            from app.email.email_runner import email_thread_pool
            email_thread_pool.submit(run_single_polling, "event:try1", applicant, "all")  #第一次轮询提交成功和付款成功邮件
            #run_single_polling(source="event:try1", applicant=applicant, mode="all")  #第一次轮询提交成功和付款成功邮件
            logger.info(f"✅ 已提交基于事件(付款后)触发的第一次邮箱轮询任务: {applicant.chinese_name},{applicant.email}")
            
            
            # 第二次检查：再等待max_wait秒
            logger.info(f"⏱️ 等待 {max_wait} 秒后进行第二次轮询...")
            logger.debug(f"等待{max_wait}秒后进行第二次轮询 - 申请人: {applicant.chinese_name},{applicant.email}")
            time.sleep(max_wait)
            logger.info(f"🔍 启动基于事件(付款后)触发的第二次次邮箱轮询: {applicant.chinese_name},{applicant.email}")
            # 使用线程池执行轮询，避免阻塞
            email_thread_pool.submit(run_single_polling, "event:try2", applicant, "all")
            #run_single_polling(source="event:try2", applicant=applicant, mode="all")  #第二次轮询提交成功和付款成功邮件
            logger.info(f"✅ 已提交基于事件(付款后)触发的第二次邮箱轮询任务: {applicant.chinese_name},{applicant.email}")
            #logger.debug(f"付款后邮件确认流程完成 - 申请人: {applicant.email}")

        except Exception as e:
            logger.error(f"❌ 事件触发的邮箱轮询失败: {e}", exc_info=True)
            logger.debug(f"轮询失败详情 - 申请人: {applicant.chinese_name},{applicant.email}, 异常: {str(e)}")

    # 启动线程以执行延迟检查
    thread = threading.Thread(target=_delayed_check, daemon=True)
    thread.start()
    logger.debug(f"已启动邮件检查线程 - 线程ID: {thread.ident}")
    logger.info(f"🔍 启动基于事件(付款后)触发的邮件检查线程: {applicant.chinese_name},{applicant.email}")