import yaml
import os
from app.utils.logger_config import get_logger
from pathlib import Path
from app.utils.env_loader import (
    load_env_var, 
    load_email_accounts_from_env, 
    get_anti_captcha_key,
    load_config_with_env
)

logger = get_logger()

# 解析 settings.yaml
#BASE_DIR = Path(__file__).resolve().parent.parent  # 项目根目录
#SETTINGS_FILE = BASE_DIR / "config" / "settings.yaml"

# 环境变量到配置的映射
ENV_CONFIG_MAPPINGS = {
    "browser": "BROWSER",
    "headless": "HEADLESS",
    "slow_mo": "SLOW_MO",
    "vietnam_evisa_url": "VIETNAM_EVISA_URL",
    "anti_captcha_api_key": "ANTI_CAPTCHA_API_KEY"
}

# 加载配置，使用环境变量
_settings = load_config_with_env(ENV_CONFIG_MAPPINGS)

# 🔧 全局自动化配置 - 使用环境变量
BROWSER = load_env_var("BROWSER", _settings.get("browser", "chromium"))
HEADLESS = load_env_var("HEADLESS", "true").lower() == "true"
SLOW_MO = int(load_env_var("SLOW_MO", "0"))
VIETNAM_URL = load_env_var("VIETNAM_EVISA_URL")

# 使用环境变量中的Anti-Captcha API密钥
env_anti_captcha_key = get_anti_captcha_key()
ANTI_CAPTCHA_KEY = env_anti_captcha_key if env_anti_captcha_key else _settings.get("anti_captcha_api_key", "")

# 📬 邮箱账号结构（解析为 dict）- 使用环境变量
env_email_config = load_email_accounts_from_env()

# 从环境变量中加载邮箱配置
_loaded_email_config = load_email_accounts_from_env() # 假设这个函数能正确工作
EMAIL_ACCOUNTS = _loaded_email_config.get("email_accounts", {})
ALLOWED_SENDERS = _loaded_email_config.get("allowed_senders", {})

if EMAIL_ACCOUNTS:
    logger.info(f"✅ Loaded {len(EMAIL_ACCOUNTS)} email account(s) from environment variables.")

else:
    logger.warning("⚠️ No email accounts loaded from environment variables. Email functionality will be limited.")
