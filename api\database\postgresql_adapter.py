# api/database/postgresql_adapter.py
"""
PostgreSQL数据库适配器
"""
import asyncio
from typing import Dict, List, Optional, Any
import asyncpg
from datetime import datetime, timedelta, timezone

from .base import DatabaseInterface
from ..config.settings import settings


class PostgreSQLAdapter(DatabaseInterface):
    """PostgreSQL数据库适配器"""
    
    def __init__(self, connection_string: str = None):
        self.connection_string = connection_string or self._build_connection_string()
        self.pool = None
    
    def _build_connection_string(self) -> str:
        """构建PostgreSQL连接字符串"""
        # 从环境变量或配置中获取PostgreSQL连接信息
        import os
        
        host = os.getenv("POSTGRES_HOST", "localhost")
        port = os.getenv("POSTGRES_PORT", "5432")
        database = os.getenv("POSTGRES_DB", "visa_automator")
        user = os.getenv("POSTGRES_USER", "postgres")
        password = os.getenv("POSTGRES_PASSWORD", "password")
        
        return f"postgresql://{user}:{password}@{host}:{port}/{database}"
    
    async def connect(self) -> bool:
        """连接PostgreSQL数据库"""
        try:
            self.pool = await asyncpg.create_pool(
                self.connection_string,
                min_size=1,
                max_size=10,
                command_timeout=60
            )
            return True
        except Exception as e:
            print(f"PostgreSQL connection failed: {e}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.pool:
            await self.pool.close()
    
    async def init_schema(self):
        """初始化PostgreSQL数据库结构"""
        schema_sql = """
        -- Table: applicant
        CREATE TABLE IF NOT EXISTS applicant (
            id SERIAL PRIMARY KEY,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            customer_source TEXT,
            chinese_name TEXT,
            surname TEXT,
            given_name TEXT,
            sex TEXT,
            dob TEXT,
            place_of_birth TEXT,
            passport_number TEXT UNIQUE,
            place_of_issue TEXT,
            date_of_issue TEXT,
            passport_expiry TEXT,
            nationality TEXT,
            email TEXT,
            telephone_number TEXT
        );

        -- Table: visa_task
        CREATE TABLE IF NOT EXISTS visa_task (
            id SERIAL PRIMARY KEY,
            applicant_id INTEGER NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            visa_type TEXT,
            visa_validity_days INTEGER,
            visa_start_date TEXT,
            entry_gate TEXT,
            application_number TEXT,
            submit_status TEXT,
            approval_status TEXT,
            is_downloaded INTEGER DEFAULT 0,
            pdf_path TEXT,
            download_time TIMESTAMP WITH TIME ZONE,
            screenshot_path TEXT,
            log_path TEXT,
            error_message TEXT,
            session_id TEXT,
            FOREIGN KEY (applicant_id) REFERENCES applicant(id)
        );

        -- Table: payment_record
        CREATE TABLE IF NOT EXISTS payment_record (
            id SERIAL PRIMARY KEY,
            visa_task_id INTEGER NOT NULL,
            payment_time TIMESTAMP WITH TIME ZONE,
            payment_amount DECIMAL(10,2),
            card_number_tail TEXT,
            payment_method TEXT,
            payment_status TEXT,
            transaction_id TEXT,
            source_email TEXT,
            FOREIGN KEY (visa_task_id) REFERENCES visa_task(id)
        );

        -- Table: email_notification
        CREATE TABLE IF NOT EXISTS email_notification (
            id SERIAL PRIMARY KEY,
            visa_task_id INTEGER,
            received_time TIMESTAMP WITH TIME ZONE,
            from_address TEXT,
            subject TEXT,
            content_excerpt TEXT,
            application_number TEXT,
            email_type TEXT,
            FOREIGN KEY (visa_task_id) REFERENCES visa_task(id)
        );

        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_applicant_passport ON applicant(passport_number);
        CREATE INDEX IF NOT EXISTS idx_visa_task_applicant ON visa_task(applicant_id);
        CREATE INDEX IF NOT EXISTS idx_visa_task_application ON visa_task(application_number);
        CREATE INDEX IF NOT EXISTS idx_payment_task ON payment_record(visa_task_id);
        CREATE INDEX IF NOT EXISTS idx_email_task ON email_notification(visa_task_id);
        """
        
        async with self.pool.acquire() as conn:
            await conn.execute(schema_sql)
    
    async def insert_applicant(self, data: Dict) -> int:
        """插入申请人"""
        async with self.pool.acquire() as conn:
            result = await conn.fetchval("""
                INSERT INTO applicant (
                    created_at, customer_source, chinese_name, surname, given_name,
                    sex, dob, place_of_birth, passport_number, place_of_issue,
                    date_of_issue, passport_expiry, nationality, email, telephone_number
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
                RETURNING id
            """,
                data.get("created_at", self._get_beijing_time()),
                data.get("customer_source"),
                data.get("chinese_name"),
                data.get("surname"),
                data.get("given_name"),
                data.get("sex"),
                data.get("dob"),
                data.get("place_of_birth"),
                data.get("passport_number"),
                data.get("place_of_issue"),
                data.get("date_of_issue"),
                data.get("passport_expiry"),
                data.get("nationality"),
                data.get("email"),
                data.get("telephone_number")
            )
            return result
    
    async def get_or_create_applicant(self, data: Dict) -> int:
        """获取或创建申请人"""
        async with self.pool.acquire() as conn:
            # 先尝试查找
            result = await conn.fetchval(
                "SELECT id FROM applicant WHERE passport_number = $1",
                data["passport_number"]
            )
            
            if result:
                return result
            
            # 如果不存在，创建新记录
            return await self.insert_applicant(data)
    
    async def get_applicant_by_application_number(self, app_number: str) -> Optional[Dict]:
        """通过申请编号获取申请人"""
        async with self.pool.acquire() as conn:
            result = await conn.fetchrow("""
                SELECT a.*
                FROM applicant a
                JOIN visa_task v ON v.applicant_id = a.id
                WHERE v.application_number = $1
                ORDER BY v.created_at DESC
                LIMIT 1
            """, app_number)
            
            return dict(result) if result else None
    
    async def insert_visa_task(self, applicant_id: int, data: Dict) -> int:
        """插入签证任务"""
        async with self.pool.acquire() as conn:
            result = await conn.fetchval("""
                INSERT INTO visa_task (
                    applicant_id, visa_type, visa_validity_days, visa_start_date, entry_gate,
                    submit_status, approval_status, is_downloaded, pdf_path, download_time,
                    screenshot_path, log_path, error_message, application_number, created_at,
                    session_id
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                RETURNING id
            """,
                applicant_id,
                data.get("visa_type"),
                data.get("visa_validity_days"),
                data.get("visa_start_date"),
                data.get("entry_gate"),
                data.get("submit_status", "FAILED"),
                data.get("approval_status", "WAITING"),
                data.get("is_downloaded", 0),
                data.get("pdf_path"),
                data.get("download_time"),
                data.get("screenshot_path"),
                data.get("log_path"),
                data.get("error_message"),
                data.get("application_number"),
                self._get_beijing_time(),
                data.get("session_id")
            )
            return result
    
    async def update_visa_task_status(self, task_id: int, status: str, **kwargs):
        """更新签证任务状态"""
        # 构建动态更新语句
        set_clauses = ["submit_status = $2"]
        params = [task_id, status]
        param_index = 3
        
        for key, value in kwargs.items():
            set_clauses.append(f"{key} = ${param_index}")
            params.append(value)
            param_index += 1
        
        sql = f"UPDATE visa_task SET {', '.join(set_clauses)} WHERE id = $1"
        
        async with self.pool.acquire() as conn:
            await conn.execute(sql, *params)
    
    async def find_task_by_name_and_dob(self, full_name: str, dob: str) -> Optional[int]:
        """通过姓名和出生日期查找任务"""
        normalized_name = " ".join(full_name.strip().upper().split())
        
        async with self.pool.acquire() as conn:
            result = await conn.fetchval("""
                SELECT vt.id
                FROM visa_task vt
                JOIN applicant a ON a.id = vt.applicant_id
                WHERE TRIM(UPPER(a.surname || ' ' || a.given_name)) = $1
                  AND a.dob = $2
                ORDER BY vt.created_at DESC
                LIMIT 1
            """, normalized_name, dob)
            
            return result
    
    async def insert_payment_record(self, task_id: int, data: Dict):
        """插入支付记录"""
        async with self.pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO payment_record (
                    visa_task_id, payment_time, payment_amount, card_number_tail,
                    payment_method, payment_status, transaction_id, source_email
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            """,
                task_id,
                data.get("payment_time"),
                data.get("payment_amount"),
                data.get("card_number_tail"),
                data.get("payment_method"),
                data.get("payment_status"),
                data.get("transaction_id"),
                data.get("source_email")
            )
    
    async def insert_email_notification(self, task_id: Optional[int], data: Dict):
        """插入邮件通知"""
        async with self.pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO email_notification (
                    visa_task_id, received_time, from_address, subject,
                    content_excerpt, application_number, email_type
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7)
            """,
                task_id,
                data.get("received_time"),
                data.get("from_address"),
                data.get("subject"),
                data.get("content_excerpt"),
                data.get("application_number"),
                data.get("email_type")
            )
    
    async def count_records(self) -> Dict[str, int]:
        """统计记录数量"""
        async with self.pool.acquire() as conn:
            applicant_count = await conn.fetchval("SELECT COUNT(*) FROM applicant")
            task_count = await conn.fetchval("SELECT COUNT(*) FROM visa_task")
            success_count = await conn.fetchval("SELECT COUNT(*) FROM visa_task WHERE submit_status = 'SUCCESS'")
            downloaded_count = await conn.fetchval("SELECT COUNT(*) FROM visa_task WHERE is_downloaded = 1")
            
            return {
                "applicants": applicant_count,
                "tasks": task_count,
                "success": success_count,
                "downloaded": downloaded_count
            }
    
    async def verify_recent_records(self, limit: int = 5) -> List[Dict]:
        """获取最近记录"""
        async with self.pool.acquire() as conn:
            results = await conn.fetch("""
                SELECT vt.id, vt.created_at, a.chinese_name, a.surname, a.given_name, 
                       a.passport_number, a.dob, a.sex, a.customer_source,
                       vt.visa_validity_days, vt.visa_type, vt.entry_gate, 
                       vt.visa_start_date, a.email, vt.submit_status, 
                       vt.application_number, vt.approval_status
                FROM visa_task vt
                JOIN applicant a ON a.id = vt.applicant_id
                ORDER BY vt.created_at DESC
                LIMIT $1
            """, limit)
            
            return [dict(row) for row in results]
    
    async def health_check(self) -> bool:
        """数据库健康检查"""
        try:
            if not self.pool:
                return False
            
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            return True
        except Exception:
            return False
    
    def _get_beijing_time(self) -> datetime:
        """获取北京时间"""
        beijing_tz = timezone(timedelta(hours=8))
        return datetime.now(beijing_tz)
