from datetime import datetime

# 活动时间窗口定义（小时）
ACTIVE_WINDOWS = [
    (9, 13),  # 上午9点到下午1点
    (16, 23)  # 下午4点到晚上11点
]

def is_within_active_window():
    """
    检查当前时间是否在活动时间窗口内
    
    活动时间窗口定义为：
    - 上午9点到下午1点
    - 下午4点到晚上11点
    
    Returns:
        bool: 如果当前时间在活动窗口内，返回True；否则返回False
    """
    now = datetime.now().time()
    for start, end in ACTIVE_WINDOWS:
        if start <= now.hour < end:
            return True
    return False