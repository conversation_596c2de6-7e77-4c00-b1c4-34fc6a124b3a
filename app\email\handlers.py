from datetime import datetime
from app.utils.logger_config import get_logger
from app.email.parser import parse_submission_email, parse_payment_email
#from app.email.result_handler import extract_application_info
from db.db import (
    insert_email_notification,
    update_task_submission,
    insert_payment_record,
    update_payment_status,
    find_task_by_name_and_dob,
    find_task_by_application_number
)

logger = get_logger()

def process_submission_email(email_obj: dict, raw_body: str):
    """处理确认提交邮件"""
    parsed = parse_submission_email(raw_body)
    if not parsed:
        logger.warning("⚠️ 无法解析提交确认邮件内容")
        return

    # 插入邮件记录
    insert_email_notification(
        task_id=None,
        data={
            "received_time": datetime.now(),
            "from_address": email_obj["from"],
            "subject": email_obj["subject"],
            "content_excerpt": raw_body[:200],
            "application_number": parsed["application_number"],
            "email_type": "SUBMIT_CONFIRM"
        }
    )


    #将full_name去掉前后空格并转换为大写
    parsed["full_name"] = parsed["full_name"].strip().upper()
    #打印并观察full_name和dob的格式
    logger.info(f"🔍 准备匹配 full_name='{parsed['full_name']}', dob='{parsed['dob']}'")

    # 匹配签证任务记录并更新
    task_id = find_task_by_name_and_dob(parsed["full_name"], parsed["dob"])
    if task_id:
        update_task_submission(task_id, parsed["application_number"])
        logger.info(f"✅ 提交确认状态已更新 → task_id={task_id}")
    else:
        logger.warning(f"⚠️从姓名和出生日期{parsed['full_name']},{parsed['dob']}匹配数据库， 未找到匹配的签证任务记录")


def process_payment_email(email_obj: dict, raw_body: str):
    """处理付款成功邮件"""
    #payment_time = email_obj["raw"].get("payment_time")  # 邮件中没有payment_time字段表示付款时间
    # 解析付款邮件，从邮件内容中提取付款时间（已转换为北京时间）
    parsed = parse_payment_email(raw_body)
    if not parsed:
        logger.warning("⚠️ 无法解析付款邮件")
        return

    # 插入邮件记录 - 使用从邮件内容解析出的北京时间作为received_time
    insert_email_notification(
        task_id=None,
        data={
            "received_time":  parsed["payment_time"],  # 这里的received_time是数据库字段名
            "from_address": email_obj["from"],
            "subject": email_obj["subject"],
            "content_excerpt": raw_body[:200],
            "application_number": parsed["application_number"],
            "email_type": "PAYMENT_SUCCESS"
        }
    )

    # 匹配任务并写入付款记录
    task_id = find_task_by_application_number(parsed["application_number"])
    if task_id:
        insert_payment_record(task_id, {
            **parsed,                        # 包含payment_time字段（已转换为北京时间）
            "payment_status": "PAID",
            "source_email": email_obj["from"],
            "transaction_id": parsed["application_number"]
        })
        update_payment_status(task_id)
        logger.info(f"✅ 已写入付款记录并更新状态 → task_id={task_id}")
    else:
        logger.warning(f"⚠️ 从申请编号{parsed['application_number']}匹配数据库，未找到匹配的签证任务记录")
