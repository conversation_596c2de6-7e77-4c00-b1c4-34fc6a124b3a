"""
Payment Automation Module
-------------------------
负责自动化越南电子签证的最终信用卡付款流程
支持多卡配置和随机付款功能

依赖:
- playwright: 浏览器自动化
- random: 随机卡选择
- json: 配置文件读取
- os.path: 文件路径处理
"""

import os
import json
import random
import time
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
from playwright.sync_api import expect  # 确保导入 expect
from playwright.sync_api import sync_playwright
from app.utils.logger_config import get_logger
from app.payment.payment_models import CreditCardInfo, create_credit_card
from playwright.sync_api import TimeoutError
from playwright.sync_api import Page
from config.browser_config import launch_form_browser
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

logger = get_logger()


# 自定义异常类
class FormValidationError(Exception):
    """表单验证错误异常"""
    pass

class PaymentButtonDisabled(Exception):
    """支付按钮禁用状态异常"""
    pass

class AgreementDialogError(Exception):
    """协议对话框处理异常"""
    pass


def load_credit_cards() -> List[CreditCardInfo]:
    """
    加载信用卡配置，仅从环境变量加载
    
    Returns:
        List[CreditCardInfo]: 有效信用卡列表（card_number不为空）
    """
    # 从环境变量加载
    from app.utils.env_loader import load_credit_cards_from_env
    env_cards_data = load_credit_cards_from_env()
    
    if not env_cards_data:
        logger.error("环境变量中没有信用卡配置，请在.env文件中添加CREDIT_CARD_相关配置")
        return []
    
    logger.info(f"从环境变量加载了 {len(env_cards_data)} 张信用卡")
    cards = []
    for card_dict in env_cards_data:
        # 严格按照JSON文件中的字段顺序创建CreditCardInfo对象
        card = CreditCardInfo(
            card_type=card_dict.get('card_type', ''),
            card_number=card_dict.get('card_number', ''),
            first_name=card_dict.get('first_name', ''),
            last_name=card_dict.get('last_name', ''),
            billing_address=card_dict.get('billing_address', ''),
            city=card_dict.get('city', ''),
            country=card_dict.get('country', ''),
            exp_month=card_dict.get('exp_month', ''),
            exp_year=card_dict.get('exp_year', ''),
            cvv=card_dict.get('cvv', ''),
            note=card_dict.get('note', '')
        )
        cards.append(card)
    return cards
    
def force_click_with_retry(page: Page, selector: str, retries=3, delay=500) -> bool:
    """暴力点击重试（平衡简洁性与健壮性）"""
    

    for attempt in range(retries):
        try:
            page.locator(selector).click(timeout=3000, force=True)
            logger.info(f"✅ force_click_with_retry暴力点击Continue成功 (第{attempt+1}次)")
            return True
        except Exception as e:
            if attempt < retries - 1:
                logger.warning(f"⚡ force_click_with_retry暴力点击重试中...（错误：{str(e)}）", exc_info=True)
                page.wait_for_timeout(delay)
    logger.error(f"❌ force_click_with_retry暴力点击失败（共 {retries} 次尝试）", exc_info=True)
    return False


def wait_for_i_agree(page, timeout=5000):
    """I agree按钮"""
    #page.wait_for_selector('.modal, .dialog, .ant-modal, .bootstrap-dialog', timeout=timeout)
    page.wait_for_selector('button:has-text("I agree")', timeout=timeout)


def smart_click(
    page, 
    selector, 
    fast_mode=True,  # 默认开启快速暴力模式
    retries=3,
    fallback_to_force=True,
    post_click_check=wait_for_i_agree,  #⬅️ 默认启用检测 I agree 按钮
    retry_delay=100
):
    """智能点击：优先快速模式，支持监听响应，点击后验证，自动重试3次"""
    

    for attempt in range(1, retries + 1):
        try:
            page.wait_for_timeout(100)  # 每次点击前都等待0.1秒 ✅

            logger.info(f"🔄 尝试第 {attempt}/{retries} 次点击 {selector} ...")
            if attempt > 1:
                page.wait_for_timeout(retry_delay)  # 每次失败后等待

            if fast_mode:
                # 快速模式：直接暴力点击
                page.locator(selector).click(timeout=1000, force=True)
            else:
                # 标准模式：完整检测后点击
                page.locator(selector).click(timeout=1000)

            # ⬇️ 点击后检查 I agree按钮
            if post_click_check:
                try:
                    post_click_check(page)
                    logger.info(f"✅ smart_click第 {attempt} 次点击后成功检测到 I agree 按钮")
                    return True  # 检查成功就结束
                except Exception as e:
                    logger.warning(f"⚠️ smart_click第 {attempt} 次点击后未检测到I agree按钮: {e}")
                    return False

        except Exception as e:
            if attempt == retries - 1:
                if fallback_to_force and not fast_mode:
                    # 最终回退到暴力点击
                    return force_click_with_retry(page, selector)
                return False
            logger.info(f"暴力点击 {selector} 失败，尝试切换模式...")

            fast_mode = not fast_mode  # 切换模式
    return False
def _verify_payment_result(page, screenshots_dir):
    """严格按需求检查支付结果"""
    

    # 等待文本结果出现（非傻等）
    page.wait_for_selector("body:has-text('SUCCESSFUL'), body:has-text('success'),body:has-text('failure')", timeout=60000)
    # 方案1：严格大小写匹配（根据您描述的字段要求）
    content = page.content()
    if "SUCCESSFUL" in content:  # 严格全大写
        logger.info("✅ 检测到'SUCCESSFUL'，支付成功")
        return True
    elif"success" in content:  # 严格全小写
        logger.info("✅ 检测到'success'，支付成功")
        return True
    elif "failure" in content:   # 严格全小写
        logger.error("❌ 检测到'failure'，支付失败")
        page.screenshot(path=str(screenshots_dir / f"payment_failed_{int(time.time())}.png"))
        return False
    elif "Decline" in content:  
        logger.error("❌ 检测到'Decline'，支付失败")
        page.screenshot(path=str(screenshots_dir / f"payment_failed_{int(time.time())}.png"))
        return False
    
    else:
        # 附加检查：模糊匹配 捕获可能的变体写法（可选）
        if any(word in content.lower() for word in [ "successful", "successfully", "SUCCESS", "SUCCESSFULLY"]):
            logger.warning("⚠️ 检测到近似成功关键词，但未匹配'SUCCESSFUL'or 'success'")
        if any(word in content.lower() for word in ["FAILED", "failed", "unsuccessful", "FAILURE", "UNSUCCESSFUL", "unsuccessfully", "UNSUCCESSFULLY", "error", "ERROR"]):
            logger.warning("⚠️ 检测到近似失败关键词，但未匹配'failure'")
        logger.warning("⚠️ 付款结果不确定，请自行检查邮箱核实")
        page.screenshot(path=str(screenshots_dir / f"payment_unknown_{int(time.time())}.png"))
        return None  # 或 return "unknown"
    

def fill_payment_form(page, card, logger):
    """
    优化的表单填写函数，集中处理信用卡表单填写逻辑
    
    Args:
        page: Playwright页面对象
        card: CreditCardInfo对象
        logger: 日志对象
        
    Returns:
        bool: 表单填写是否成功
    """
    try:
        # 等待表单加载
        form_loaded = False
        for field in ["#card_number", "#card_first_name", "#card_last_name"]:
            try:
                page.wait_for_selector(field, state='visible', timeout=10000)
                form_loaded = True
                break
            except Exception:
                continue
        
        if not form_loaded:
            logger.error("表单未加载")
            return False
        
        # 填写基本字段
        logger.info("填写信用卡信息...")
        page.fill("#card_number", card.card_number)
        page.fill("#card_first_name", card.first_name)
        page.fill("#card_last_name", card.last_name)
        page.fill("#billing_address", card.billing_address)
        page.fill("#city", card.city)
        
        # 选择国家
        try:
            page.click("#select2-country-container")
            page.wait_for_timeout(100)
            search_box = page.locator(".select2-search__field")
            search_box.fill(card.country)
            page.wait_for_timeout(100)
            search_box.press("Enter")
            logger.info(f"已选择国家: {card.country}")
        except Exception as e:
            logger.warning(f"选择国家失败: {e}")
        
        # 选择过期日期（不检测，失败重试最多3次）
        for attempt in range(3):
            try:
                page.select_option("#expMonth", card.exp_month)
                page.select_option("#expYear", card.exp_year)
                logger.info(f"✅ 成功选择过期日期 (尝试第 {attempt+1} 次)")
                break  # 成功就退出循环
            except Exception as e:
                logger.warning(f"⚠️ 第 {attempt+1} 次选择过期日期失败: {e}")
                if attempt < 2:  # 还有重试机会
                    page.wait_for_timeout(100)  # 等0.1秒
                else:
                    logger.error("❌ 最终选择过期日期失败")
        
        # 填写CVV
        page.fill("#cvv2", card.cvv)
        logger.info("信用卡信息填写完成")
        return True
        
    except Exception as e:
        logger.error(f"填写表单出错: {e}", exc_info=True)
        return False

# 添加新函数，支持使用现有页面进行付款
def process_payment_with_existing_page(page, payment_url: str = None, settings: Optional[Dict[str, Any]] = None) -> bool:
    """
    使用已存在的页面处理支付流程，无需重新启动浏览器
    
    Args:
        page: 已存在的Playwright页面对象
        payment_url: 已废弃参数，保留仅为兼容性
        settings: 可选的配置参数
            
    Returns:
        bool: 支付是否成功
    """
    settings = settings or {}
    
    # 设置超时参数(默认300秒)
    timeout = settings.get('payment_timeout_ms', 300000)
            
    # 设置截图目录
    screenshots_dir = Path(settings.get('screenshots_dir', 'payment_screenshots'))
    screenshots_dir.mkdir(exist_ok=True)
    
    # 加载信用卡列表
    cards = load_credit_cards()
    if not cards:
        logger.error("没有有效的信用卡信息，无法完成支付")
        return False
    
    # 随机选择一张卡
    card = random.choice(cards)
    card_number = card.card_number
    masked_number = f"****{card_number[-4:]}" if len(card_number) >= 4 else "****"
    logger.info(f"随机选择信用卡: {card.card_type} {masked_number}")
    if card.note:
        logger.info(f"卡片备注: {card.note}")
    
    try:
        # 无缝模式：直接使用当前页面，不再导航
        logger.info(f"当前页面URL: {page.url}")
        logger.info("开始处理付款流程...")
        
        # 截图记录当前页面状态
        page.screenshot(path=str(screenshots_dir / f"payment_start_{int(time.time())}.png"))
        
        # 1. 点击付款方式区域
        logger.info("点击支付区域...")
        
        payment_area_selectors = [
            'div:has-text("PAY BY VISA / MASTERCARD / JCB / AMEX")',
            'h4:has-text("PAY BY VISA")',
            'a:has-text("VISA / MASTERCARD")',
            'a:has(img[alt*="VISA"])'
        ]
        
        clicked = False
        for selector in payment_area_selectors:
            try:
                if page.locator(selector).is_visible(timeout=5000):
                    page.locator(selector).click()
                    logger.info(f"已点击支付区域: '{selector}'")
                    page.wait_for_timeout(1000)
                    clicked = True
                    break
            except Exception:
                continue
                
        if not clicked:
            logger.warning("未找到明确的支付区域，尝试继续流程")
            # 不要返回False，尝试继续流程
        
        # 2. 选择卡类型
        logger.info(f"选择卡类型: {card.card_type}")
        card_type = card.card_type.upper()
        
        card_type_selectors = [
            f'a:has(i.{card_type})',
            f'a:has(img[alt*="{card_type}"])',
            f'div:has-text("{card_type}")'
        ]
        
        clicked = False
        for selector in card_type_selectors:
            try:
                if page.locator(selector).is_visible(timeout=10000):
                    page.locator(selector).click()
                    logger.info(f"已选择卡类型: {card_type}")
                    page.wait_for_timeout(1000)
                    clicked = True
                    break
            except Exception:
                continue
        
        if not clicked:
            logger.warning(f"未找到卡类型选择器，可能已自动选择")
        
        # 3. 使用优化的函数填写支付表单
        if not fill_payment_form(page, card, logger):
            logger.error("填写支付表单失败")
            page.screenshot(path=str(screenshots_dir / f"form_fill_failed_{int(time.time())}.png"))
            return False
        
        # 4. 提交支付
        logger.info("信用卡信息填写完成，提交最后支付流程...")

        try:
            # ===== 第一阶段：处理Continue按钮 =====
            if smart_click(page, "button#pay-button"):
                logger.info("已确认点击Continue按键成功")
            else:
                logger.error("确认点击Continue按键失败")
                page.screenshot(path=str(screenshots_dir / f"continue_click_failed_{int(time.time())}.png"))
                # 尝试其他按钮
                alternative_buttons = [
                    'button:has-text("Continue")',
                    'button:has-text("Pay")',
                    'button:has-text("Submit")',
                    'input[type="submit"]'
                ]
                for btn in alternative_buttons:
                    try:
                        if page.locator(btn).is_visible(timeout=2000):
                            page.locator(btn).click(force=True)
                            logger.info(f"尝试点击替代按钮: {btn}")
                            break
                    except Exception:
                        continue

            # ===== 第二阶段：处理I agree按钮 =====
            try:
                i_agree_button = page.locator('button:has-text("I agree")')
                if i_agree_button.is_visible(timeout=5000):
                    logger.info("检测到 I agree 按钮，点击...")
                    i_agree_button.click(force=True)
                    logger.info("已点击 I agree 按钮")
                    
                    # 等待按钮自己消失
                    try:
                        i_agree_button.wait_for(state="hidden", timeout=5000)
                        logger.info("✅ I agree 按钮已从 DOM 中移除，确认点击成功")
                    except TimeoutError:
                        # 如果未移除，检查是否隐藏
                        if not i_agree_button.is_visible():
                            logger.info("✅ I agree 按钮已被隐藏，确认点击成功")
                        else:
                            logger.warning("⚠️ 3 秒后 I agree 按钮仍可见，点击可能未生效")
            except Exception as e:
                logger.warning(f"⚠️ 处理 I agree 按钮时出错，忽略错误继续: {e}")

            # ===== 第三阶段：第二次点击Continue按键 最终确认 =====
            try:
                final_continue = page.locator('button#pay-button:has-text("Continue")')
                final_continue.wait_for(state="attached", timeout=300000)
                final_continue.click()
                logger.info("✅ 第二次点击 Continue 按钮成功")
            except Exception as e:
                logger.warning(f"⚠️ 第二次点击 Continue 失败，但继续执行: {e}")

            # ===== 支付结果验证 =====
            return _verify_payment_result(page, screenshots_dir)

        except Exception as e:
            page.screenshot(path=str(screenshots_dir / f"error_{int(time.time())}.png"))
            logger.error(f"支付流程异常: {e}", exc_info=True)
            return False
    except Exception as e:
        logger.error(f"支付过程出错: {e}")
        return False


def process_payment(payment_url: str, settings: Optional[Dict[str, Any]] = None) -> bool:
    """
    处理支付流程，从环境变量加载信用卡信息
        
    Args:
        payment_url: 支付页面URL
        settings: 可选的配置参数
            
    Returns:
        bool: 支付是否成功
    """
    
    settings = settings or {}
    
    # 设置超时参数(默认300秒)
    timeout = settings.get('payment_timeout_ms', 300000)
            
    # 设置截图目录
    screenshots_dir = Path(settings.get('screenshots_dir', 'payment_screenshots'))
    screenshots_dir.mkdir(exist_ok=True)
    
    # 加载信用卡列表
    cards = load_credit_cards()
    if not cards:
        logger.error("没有有效的信用卡信息，无法完成支付")
        return False
    
    # 随机选择一张卡
    card = random.choice(cards)
    card_number = card.card_number
    masked_number = f"****{card_number[-4:]}" if len(card_number) >= 4 else "****"
    logger.info(f"随机选择信用卡: {card.card_type} {masked_number}")
    if card.note:
        logger.info(f"卡片备注: {card.note}")
    
    try:
        logger.info(f"开始支付流程: {payment_url}")
        
        with sync_playwright() as p:
            # 启动浏览器 - 使用统一的浏览器启动函数
            browser = None
            headless = settings.get('headless', True)
            slow_mo = settings.get('slow_mo', 50)
            
            # 使用统一的浏览器启动函数
            from config.browser_config import launch_form_browser
            browser, context, page = launch_form_browser(p, {
                'headless': headless,
                'slow_mo': slow_mo
            })
            
            # 导航到支付页面
            logger.info(f"正在导航到支付页面...")
            page.goto(payment_url, timeout=timeout)
            
            # 确保页面加载完成
            page.wait_for_load_state("networkidle", timeout=timeout)
            logger.info("支付页面已加载完成")

            # 1. 点击付款方式区域
            logger.info("点击支付区域...")
            
            payment_area_selectors = [
                'div:has-text("PAY BY VISA / MASTERCARD / JCB / AMEX")',
                'h4:has-text("PAY BY VISA")',
                'a:has-text("VISA / MASTERCARD")',
                'a:has(img[alt*="VISA"])'
            ]
            
            clicked = False
            for selector in payment_area_selectors:
                try:
                    if page.locator(selector).is_visible(timeout=5000):
                        page.locator(selector).click()
                        logger.info(f"已点击支付区域: '{selector}'")
                        page.wait_for_timeout(1000)
                        clicked = True
                        break
                except Exception:
                    continue
                    
            if not clicked:
                logger.error("无法找到支付区域")
                browser.close()
                return False
            
            # 2. 选择卡类型
            logger.info(f"选择卡类型: {card.card_type}")
            card_type = card.card_type.upper()
            
            card_type_selectors = [
                f'a:has(i.{card_type})',
                f'a:has(img[alt*="{card_type}"])',
                f'div:has-text("{card_type}")'
            ]
            
            clicked = False
            for selector in card_type_selectors:
                try:
                    if page.locator(selector).is_visible(timeout=10000):
                        page.locator(selector).click()
                        logger.info(f"已选择卡类型: {card_type}")
                        page.wait_for_timeout(1000)
                        clicked = True
                        break
                except Exception:
                    continue
            
            if not clicked:
                logger.warning(f"未找到卡类型选择器，可能已自动选择")
            
            # 3. 使用优化的函数填写支付表单
            if not fill_payment_form(page, card, logger):
                logger.error("填写支付表单失败")
                browser.close()
                return False
            
            # 4. 提交支付
            logger.info("信用卡信息填写完成，提交最后支付流程...")

            try:
                # ===== 第一阶段：处理Continue按钮 =====
                #continue_button = page.locator('button#pay-button:has-text("Continue"):not([disabled])')

                # 优先快速模式，失败后自动切换暴力点击
                if smart_click(page, "button#pay-button"):

                    logger.info("已确认点击Continue按键成功")
                else:
                    logger.error("确认点击Continue按键失败")
          

                # ===== 第二阶段：处理I agree对话框 =====
                try:
                    i_agree_button = page.locator('button:has-text("I agree")')
                    try:
                        i_agree_button.wait_for(state="visible", timeout=5000)  # 智能等待按钮可见
                        if i_agree_button.is_enabled():
                            logger.info("✅ I agree 按钮可见且可点击，准备点击")
                            i_agree_button.click()
                            logger.info("✅ 已点击 I agree 按钮")
                        else:
                            logger.warning("⚠️ I agree 按钮可见但不可点击，无法点击")
                    except TimeoutError:
                        logger.warning("⚠️ 超时未检测到 I agree 按钮可见，跳过点击")
                    except Exception as e:
                        logger.warning(f"⚠️ 处理 I agree 按钮时出错，忽略错误继续: {e}")

                        # 🔥 更简单：直接等待按钮自己消失
                    try:
                        i_agree_button.wait_for(state="hidden", timeout=5000)  # 等待按钮变成不可见（无论是隐藏还是被移除）
                        logger.info("✅ I agree 按钮已从 DOM 中移除，确认点击成功")
                    except TimeoutError:
                        # 如果未移除，检查是否隐藏
                        if not i_agree_button.is_visible():
                            logger.info("✅ I agree 按钮已被隐藏，确认点击成功")
                        else:
                            logger.warning("⚠️ 3 秒后 I agree 按钮仍可见，点击可能未生效")

                    except TimeoutError:
                        logger.info("⚠️ 5秒内未检测到 I agree 按钮，跳过，继续流程")
                except Exception as e:
                    logger.warning(f"⚠️ 处理 I agree 按钮时出错，忽略错误继续: {e}")


                # ===== 第三阶段：第二次点击Continue按键 最终确认 =====
                try:
                    final_continue = page.locator('button#pay-button:has-text("Continue")')
                    final_continue.wait_for(state="attached", timeout=300000)
                    final_continue.click()
                    logger.info("✅ 第二次点击 Continue 按钮成功")
                except Exception as e:
                    logger.warning(f"⚠️ 第二次点击 Continue 失败，但继续执行: {e}")

                # ===== 支付结果验证 =====
                return _verify_payment_result(page, screenshots_dir)

            except Exception as e:
                page.screenshot(path=str(screenshots_dir / f"error_{int(time.time())}.png"))
                logger.error(f"支付流程异常: {e}", exc_info=True)
                return False
    except Exception as e:
        logger.error(f"支付过程出错: {e}")
        return False


    





