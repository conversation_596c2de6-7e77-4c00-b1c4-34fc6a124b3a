import datetime
import pytz
from typing import List, Optional

def calculate_working_days_from_today(working_days_count: int, 
                                     holidays: Optional[List[str]] = None) -> datetime.date:
    """
    计算从今天开始，经过指定工作日数后的日期。
    工作日不包括周末（周六和周日）和指定的节假日。
    使用中国北京时区。
    
    Args:
        working_days_count: 需要经过的工作日数量
        holidays: 节假日列表，默认为空列表
    
    Returns:
        经过指定工作日数后的日期
    """
    if holidays is None:
        holidays = []
    
    # 获取中国北京时区的当前日期
    beijing_tz = pytz.timezone('Asia/Shanghai')
    today = datetime.datetime.now(beijing_tz).date()
    
    # 将节假日转换为日期对象列表
    holiday_dates = [datetime.datetime.strptime(h, "%d/%m/%Y").date() for h in holidays]
    
    # 当前日期
    current_date = today
    # 已经经过的工作日数量
    days_counted = 0
    
    # 循环直到达到指定的工作日数量
    while days_counted < working_days_count:
        # 前进一天
        current_date += datetime.timedelta(days=1)
        # 检查是否是工作日（不是周末且不是节假日）
        if current_date.weekday() < 5 and current_date not in holiday_dates:
            days_counted += 1
    
    return current_date

# 越南2025年主要节假日列表（格式：DD/MM/YYYY）
VIETNAM_HOLIDAYS_2025 = [
    "01/01/2025",  # 元旦
    "28/01/2025", "29/01/2025", "30/01/2025", "31/01/2025", "01/02/2025", "02/02/2025", "03/02/2025",  # 农历新年（预估）
    "30/04/2025",  # 解放日
    "01/05/2025",  # 劳动节
    "02/09/2025",  # 国庆日
]