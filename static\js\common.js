// 通用JavaScript功能 - Common JavaScript Functions

// 通用工具函数
const Utils = {
    // 显示状态弹窗
    showStatus: function(type, title, message, showCloseButton = true) {
        const overlay = document.getElementById('submitOverlay');
        const content = document.getElementById('submitContent');
        
        if (!overlay || !content) return;
        
        let iconHtml = '';
        if (type === 'loading') {
            iconHtml = '<div class="loading-spinner"></div>';
        } else if (type === 'success') {
            iconHtml = '<div class="status-icon success">✓</div>';
        } else if (type === 'error') {
            iconHtml = '<div class="status-icon error">✗</div>';
        } else if (type === 'warning') {
            iconHtml = '<div class="status-icon warning">!</div>';
        }
        
        content.innerHTML = `
            ${iconHtml}
            <h3 style="margin-bottom: 1rem; color: var(--text-color);">${title}</h3>
            <p style="margin-bottom: 1.5rem; color: #6b7280;">${message}</p>
            ${showCloseButton ? '<button class="btn-primary" onclick="Utils.hideStatus()">确定</button>' : ''}
        `;
        
        overlay.classList.add('show');
    },

    // 隐藏状态弹窗
    hideStatus: function() {
        const overlay = document.getElementById('submitOverlay');
        if (overlay) {
            overlay.classList.remove('show');
        }
    },

    // 获取Cookie值
    getCookie: function(name) {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [cookieName, value] = cookie.trim().split('=');
            if (cookieName === name) {
                return value;
            }
        }
        return null;
    },

    // 设置Cookie
    setCookie: function(name, value, days = 7) {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${value}; expires=${expires.toUTCString()}; path=/`;
    },

    // 删除Cookie
    deleteCookie: function(name) {
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    },

    // 日期格式化
    formatDate: function(date, format = 'DD/MM/YYYY') {
        if (!(date instanceof Date)) {
            date = new Date(date);
        }
        
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        
        return format
            .replace('DD', day)
            .replace('MM', month)
            .replace('YYYY', year);
    },

    // 解析日期字符串
    parseDate: function(dateStr) {
        const parts = dateStr.split('/');
        if (parts.length === 3) {
            return new Date(parts[2], parts[1] - 1, parts[0]);
        }
        return null;
    },

    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 验证邮箱格式
    validateEmail: function(email) {
        const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return pattern.test(email);
    },

    // 验证电话号码格式
    validatePhone: function(phone) {
        const pattern = /^[\d\+\-\s\(\)]{8,20}$/;
        return pattern.test(phone);
    },

    // 验证护照号码格式
    validatePassport: function(passport) {
        const pattern = /^[A-Z0-9]{6,12}$/;
        return pattern.test(passport.toUpperCase());
    },

    // 验证日期格式
    validateDate: function(dateStr) {
        const pattern = /^\d{2}\/\d{2}\/\d{4}$/;
        if (!pattern.test(dateStr)) return false;
        
        const date = this.parseDate(dateStr);
        return date && !isNaN(date.getTime());
    },

    // 显示加载状态
    showLoading: function(message = '正在处理...') {
        this.showStatus('loading', '请稍候', message, false);
    },

    // 显示成功消息
    showSuccess: function(title, message) {
        this.showStatus('success', title, message);
    },

    // 显示错误消息
    showError: function(title, message) {
        this.showStatus('error', title, message);
    },

    // 显示警告消息
    showWarning: function(title, message) {
        this.showStatus('warning', title, message);
    },

    // 文件大小格式化
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 检查文件类型
    isValidImageFile: function(file) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
        return validTypes.includes(file.type);
    },

    // 检查文件大小
    isValidFileSize: function(file, maxSizeMB = 5) {
        const maxSizeBytes = maxSizeMB * 1024 * 1024;
        return file.size <= maxSizeBytes;
    }
};

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('全局错误:', e.error);
    Utils.showError('系统错误', '发生了意外错误，请刷新页面重试');
});

// 网络错误处理
window.addEventListener('unhandledrejection', function(e) {
    console.error('未处理的Promise拒绝:', e.reason);
    if (e.reason && e.reason.message && e.reason.message.includes('fetch')) {
        Utils.showError('网络错误', '网络连接出现问题，请检查网络连接后重试');
    }
});

// 导出到全局作用域
window.Utils = Utils;
