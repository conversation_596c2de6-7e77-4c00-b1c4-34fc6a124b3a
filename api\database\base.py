# api/database/base.py
"""
数据库抽象层 - 支持SQLite和PostgreSQL
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime


class DatabaseInterface(ABC):
    """数据库接口抽象类"""
    
    @abstractmethod
    async def connect(self) -> bool:
        """连接数据库"""
        pass
    
    @abstractmethod
    async def disconnect(self):
        """断开数据库连接"""
        pass
    
    @abstractmethod
    async def init_schema(self):
        """初始化数据库结构"""
        pass
    
    # 申请人相关操作
    @abstractmethod
    async def insert_applicant(self, data: Dict) -> int:
        """插入申请人"""
        pass
    
    @abstractmethod
    async def get_or_create_applicant(self, data: Dict) -> int:
        """获取或创建申请人"""
        pass
    
    @abstractmethod
    async def get_applicant_by_application_number(self, app_number: str) -> Optional[Dict]:
        """通过申请编号获取申请人"""
        pass
    
    # 签证任务相关操作
    @abstractmethod
    async def insert_visa_task(self, applicant_id: int, data: Dict) -> int:
        """插入签证任务"""
        pass
    
    @abstractmethod
    async def update_visa_task_status(self, task_id: int, status: str, **kwargs):
        """更新签证任务状态"""
        pass
    
    @abstractmethod
    async def find_task_by_name_and_dob(self, full_name: str, dob: str) -> Optional[int]:
        """通过姓名和出生日期查找任务"""
        pass
    
    # 支付记录相关操作
    @abstractmethod
    async def insert_payment_record(self, task_id: int, data: Dict):
        """插入支付记录"""
        pass
    
    # 邮件通知相关操作
    @abstractmethod
    async def insert_email_notification(self, task_id: Optional[int], data: Dict):
        """插入邮件通知"""
        pass
    
    # 统计和查询
    @abstractmethod
    async def count_records(self) -> Dict[str, int]:
        """统计记录数量"""
        pass
    
    @abstractmethod
    async def verify_recent_records(self, limit: int = 5) -> List[Dict]:
        """获取最近记录"""
        pass
    
    # 健康检查
    @abstractmethod
    async def health_check(self) -> bool:
        """数据库健康检查"""
        pass


class DatabaseManager:
    """数据库管理器 - 支持多数据库切换"""
    
    def __init__(self, primary_db: DatabaseInterface, backup_db: Optional[DatabaseInterface] = None):
        self.primary_db = primary_db
        self.backup_db = backup_db
        self.use_backup = False
    
    async def get_db(self) -> DatabaseInterface:
        """获取当前使用的数据库"""
        if self.use_backup and self.backup_db:
            return self.backup_db
        return self.primary_db
    
    async def switch_to_backup(self):
        """切换到备用数据库"""
        if self.backup_db:
            self.use_backup = True
    
    async def switch_to_primary(self):
        """切换到主数据库"""
        self.use_backup = False
    
    async def sync_databases(self):
        """同步两个数据库的数据"""
        if not self.backup_db:
            return
        
        # 这里实现数据同步逻辑
        # 可以从SQLite同步到PostgreSQL
        pass
