# api/auth/models.py
from datetime import datetime
from typing import Optional
from pydantic import BaseModel


class User(BaseModel):
    """用户模型"""
    username: str
    password: str
    locked_until: Optional[datetime] = None
    failed_attempts: int = 0


class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str
    password: str


class LoginResponse(BaseModel):
    """登录响应模型"""
    success: bool
    message: str
    redirect_url: Optional[str] = None
