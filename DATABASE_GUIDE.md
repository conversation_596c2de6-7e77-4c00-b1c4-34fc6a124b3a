# 数据库使用指南

## 🎯 **简化方案概述**

本项目采用**简单、成熟、可扩展**的数据库架构：
- **本地开发**: SQLite（轻量、零配置）
- **生产部署**: PostgreSQL（高性能、云友好）
- **统一接口**: 相同的API，无缝切换

## 🚀 **快速开始**

### **本地开发（推荐）**

```bash
# 1. 复制本地配置
cp .env.local .env

# 2. 启动应用（自动使用SQLite）
python -m api.main

# 3. 验证
curl http://localhost:8000/api/health/
```

### **生产部署**

```bash
# 1. 启动PostgreSQL
docker-compose up -d postgres

# 2. 复制生产配置
cp .env.production .env

# 3. 启动应用集群
docker-compose up -d --scale visa-automator=3 nginx-lb

# 4. 验证
curl http://localhost:8000/api/health/detailed
```

## 📊 **配置说明**

### **环境变量**

| 变量 | 本地开发 | 生产环境 | 说明 |
|------|----------|----------|------|
| `DATABASE_TYPE` | `sqlite` | `postgresql` | 数据库类型 |
| `SQLITE_PATH` | `results/visa_results.db` | - | SQLite文件路径 |
| `POSTGRES_HOST` | - | `postgres` | PostgreSQL主机 |
| `POSTGRES_DB` | - | `visa_automator` | 数据库名 |
| `DEBUG` | `true` | `false` | 调试模式 |

### **数据库连接字符串**

```bash
# SQLite
DATABASE_TYPE=sqlite
SQLITE_PATH=results/visa_results.db

# PostgreSQL (本地)
DATABASE_TYPE=postgresql
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=visa_automator
POSTGRES_USER=visa_user
POSTGRES_PASSWORD=visa_password_2024

# PostgreSQL (云端)
DATABASE_TYPE=postgresql
POSTGRES_HOST=your-cloud-db.amazonaws.com
POSTGRES_PORT=5432
POSTGRES_DB=visa_automator
POSTGRES_USER=your_user
POSTGRES_PASSWORD=your_secure_password
```

## 🔧 **常用操作**

### **切换数据库**

```bash
# 切换到SQLite
export DATABASE_TYPE=sqlite
python -m api.main

# 切换到PostgreSQL
export DATABASE_TYPE=postgresql
python -m api.main
```

### **数据库管理**

```bash
# 查看数据库状态
curl http://localhost:8000/api/health/detailed

# 检查数据库连接
python -c "
import asyncio
from api.database.factory import check_database_health
print(asyncio.run(check_database_health()))
"

# 查看数据库统计
python -c "
import asyncio
from api.database.factory import get_database
async def stats():
    db = await get_database()
    return await db.count_records()
print(asyncio.run(stats()))
"
```

### **Docker操作**

```bash
# 仅启动PostgreSQL
docker-compose up -d postgres

# 查看PostgreSQL日志
docker-compose logs postgres

# 连接到PostgreSQL
docker-compose exec postgres psql -U visa_user -d visa_automator

# 备份PostgreSQL数据
docker-compose exec postgres pg_dump -U visa_user visa_automator > backup.sql

# 恢复PostgreSQL数据
docker-compose exec -T postgres psql -U visa_user visa_automator < backup.sql
```

## 📈 **性能优化**

### **SQLite优化**

```python
# 在SQLite适配器中已优化
- WAL模式
- 连接池
- 异步操作
```

### **PostgreSQL优化**

```python
# 在PostgreSQL适配器中已优化
- 连接池 (1-10连接)
- 索引优化
- 查询优化
```

## 🔍 **监控和调试**

### **健康检查端点**

```bash
# 基础检查
GET /api/health/

# 详细检查
GET /api/health/detailed

# 就绪检查
GET /api/health/ready

# 存活检查
GET /api/health/live
```

### **日志监控**

```bash
# 查看应用日志
tail -f logs/fastapi/vietnam_evisa_api.log

# 查看数据库日志
docker-compose logs -f postgres
```

## 🚀 **云端部署**

### **AWS部署**

```bash
# 使用RDS PostgreSQL
export POSTGRES_HOST=your-rds.amazonaws.com
export POSTGRES_USER=your_user
export POSTGRES_PASSWORD=your_password

# 部署到ECS/EKS
docker build -t visa-automator .
docker tag visa-automator:latest your-ecr-repo/visa-automator:latest
docker push your-ecr-repo/visa-automator:latest
```

### **阿里云部署**

```bash
# 使用RDS PostgreSQL
export POSTGRES_HOST=your-rds.mysql.rds.aliyuncs.com
export POSTGRES_USER=your_user
export POSTGRES_PASSWORD=your_password

# 部署到ACK
kubectl apply -f k8s-deployment.yaml
```

## 🎯 **最佳实践**

### **开发流程**

1. **本地开发** - 使用SQLite快速迭代
2. **集成测试** - 使用PostgreSQL验证
3. **生产部署** - 使用云端PostgreSQL

### **数据备份**

```bash
# SQLite备份
cp results/visa_results.db backup/visa_results_$(date +%Y%m%d).db

# PostgreSQL备份
docker-compose exec postgres pg_dump -U visa_user visa_automator > backup_$(date +%Y%m%d).sql
```

### **故障恢复**

```bash
# 如果PostgreSQL连接失败，应用会自动报错
# 可以临时切换到SQLite
export DATABASE_TYPE=sqlite
python -m api.main
```

## 📋 **常见问题**

### **Q: 本地程序还能正常运行吗？**
A: 完全可以！默认使用SQLite，零配置启动。

### **Q: 如何在本地测试PostgreSQL？**
A: 运行 `docker-compose up -d postgres`，然后设置 `DATABASE_TYPE=postgresql`。

### **Q: 云端部署复杂吗？**
A: 非常简单！只需要设置PostgreSQL连接参数，其他都是自动的。

### **Q: 数据如何迁移？**
A: 由于数据量小，建议重新开始。如需迁移，可以导出SQLite数据后导入PostgreSQL。

### **Q: 性能如何？**
A: SQLite适合单用户，PostgreSQL适合多用户并发，都经过优化。

## 🎉 **总结**

这个方案的优势：
- ✅ **简单** - 本地零配置，生产一键部署
- ✅ **成熟** - 使用经过验证的技术栈
- ✅ **可扩展** - 支持从单机到集群的平滑扩展
- ✅ **可维护** - 统一的接口，清晰的架构
- ✅ **云友好** - 完美支持各种云平台

现在你可以专注于业务逻辑，数据库层面已经为你处理好了！🚀
