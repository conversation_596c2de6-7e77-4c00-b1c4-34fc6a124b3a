from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError
from pathlib import Path
from app.utils.captcha_solver import solve_captcha 
import re
from db.db import get_task_display_fields  
import time
from app.utils.logger_config import get_logger
import os
from datetime import datetime
from app.utils.retry_strategy import evisa_pdf_download_retry
from pypdf import PdfReader
from config.browser_config import launch_form_browser
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

logger = get_logger()


def get_desktop_download_folder() -> Path:
    """获取桌面上的下载文件夹路径，如果不存在则创建"""
    logger.debug("Getting desktop download folder...")
    # 获取桌面路径
    desktop_path = Path(os.path.expanduser("~/Desktop"))
    if not desktop_path.exists():
        # 如果找不到Desktop，尝试中文路径
        desktop_path = Path(os.path.expanduser("~/桌面"))
        if not desktop_path.exists():
            logger.warning("❌ 无法找到桌面路径，将使用默认downloads文件夹")
            return Path("downloads")
    
    # 获取当前日期（北京时间）并格式化为YYYYMMDD
    today = datetime.now().strftime("%Y%m%d")
    folder_name = f"{today}越南签证下载"
    
    # 创建完整的文件夹路径
    download_folder = desktop_path / folder_name
    logger.info(f"Download folder path: {download_folder}")
    
    # 检查文件夹是否已存在，如果不存在则创建
    if not download_folder.exists():
        logger.info(f"📁 在桌面创建新文件夹: {folder_name}")
        download_folder.mkdir(exist_ok=True)
    else:
        logger.info(f"📁 使用桌面已有文件夹: {folder_name}")
    
    return download_folder

def get_unique_filepath(target_path: Path) -> Path:
    """确保保存文件不会覆盖已有文件，若同名则递增编号"""
    if not target_path.exists():
        return target_path
    stem, suffix = target_path.stem, target_path.suffix
    parent = target_path.parent
    idx = 1
    while True:
        candidate = parent / f"{stem}({idx}){suffix}"
        if not candidate.exists():
            return candidate
        idx += 1

#添加重试装饰器
@evisa_pdf_download_retry
def download_pdf_for_application(task_id: int, url: str) -> str | None:
    """下载签证PDF并按中文名重命名，"""
    #初始化变量
    browser = None
    context = None
    logger.debug(f"Downloading PDF for task ID {task_id}...")

    logger.info(f"🚀 开始下载签证PDF，任务ID: {task_id}")
    logger.info(f"📌 使用URL: {url}")

    try:
        logger.debug(f"🔍 获取任务信息用于文件命名...")
        display = get_task_display_fields(task_id)  # 获取重命名字段
        if not display:
            logger.error("❌ 无法获取签证任务字段用于重命名")
            # 不再直接抛出异常，而是使用默认值继续执行
            display = {
                "chinese_name": "未知申请人请检查",
                "passport_number": f"unknown_{int(time.time())}",
                "valid_days": "未知",
                "visa_type": "未知",
                "entry_gate": "未知",
                "visa_start_date": datetime.now().strftime("%d/%m/%Y")
            }
            logger.info("⚠️ 使用默认值继续执行下载")
            
        logger.info(f"✅ 使用任务信息: {display.get('chinese_name', '')} - {display.get('passport_number', '')}")

        # 构造下载后的PDF文件名 - 新格式：中文姓名+签证有效期+签证类型+入境口岸+生效日期
        # 入境口岸映射表（英文 → 中文）
        gate_mapping = {
            "Tan Son Nhat Int Airport (Ho Chi Minh City)": "胡志明",
            "Noi Bai Int Airport": "河内",
            "Da Nang International Airport": "岘港",
            "Cam Ranh Int Airport (Khanh Hoa)": "芽庄",
            "Mong Cai Landport": "东兴",
            "Huu Nghi Landport": "友谊"
        }

        # 获取姓名（优先中文名，若无则用拼音/英文名）
        if display.get("chinese_name"):
            name_raw = display["chinese_name"]
            logger.info(f"📝 使用中文姓名: {name_raw}")
        else:
            name_raw = f"{display.get('surname', '')}{display.get('given_name', '')}"
            logger.info(f"📝 使用英文姓名: {name_raw}")

        clean_name = re.sub(r"[^\u4e00-\u9fa5a-zA-Z0-9]", "", name_raw)
        logger.info(f"📝 处理后的姓名: {clean_name}")
        days = f"{display.get('valid_days', '未知')}天"
        visa_type = "多次" if "Multiple" in display.get("visa_type", "") else "单次"
        raw_gate = display.get("entry_gate", "").strip()
        gate_cn = gate_mapping.get(raw_gate, raw_gate)

        # 生效日期格式化：20/05/2025 → 20052025
        date_raw = display.get("visa_start_date", "")
        start_date = "".join(date_raw.split("/")) if date_raw else "未知日期"
        logger.info(f"📝 生效日期: {date_raw} → {start_date}")

        # 最终文件名拼接
        final_filename = f"{clean_name}{days}{visa_type}{gate_cn}{start_date}.pdf"
        logger.info(f"📄 最终文件名: {final_filename}")

        #download_dir = Path("downloads")
        #download_dir.mkdir(parents=True, exist_ok=True)
        #final_path = download_dir / final_filename
        
        # 使用桌面上的日期命名文件夹
        download_dir = get_desktop_download_folder()
        final_path = download_dir / final_filename

        # 启动Playwright浏览器执行下载
        logger.info("🌐 启动浏览器并导航到签证下载页面...")
        with sync_playwright() as p:
            browser, context, page = launch_form_browser(p)
            logger.info("✅ 浏览器已启动")
            logger.info("✅ 浏览器上下文已创建，已启用下载功能")
            
            # 实现页面加载超时和自动刷新机制
            try:
                # 第一次尝试加载页面，30秒超时
                logger.info("开始加载页面...")
                page.goto(url, timeout=30000)  # 30秒超时
                logger.info("✅ 页面加载完成")
            except Exception as e:
                logger.warning(f"❌页面加载超时 (30秒)，尝试刷新: {e}")
                try:
                    # 刷新页面，再等30秒
                    page.reload(timeout=30000)  # 再给30秒尝试刷新
                    logger.info("✅ 页面刷新加载成功")
                except Exception as refresh_e:
                    # 如果刷新后仍然失败，则抛出异常
                    logger.error(f"❌页面刷新后仍然加载失败: {refresh_e}")
                    raise  # 重新抛出异常，触发外层重试

            # 智能等待验证码图片加载完成
            page.wait_for_selector('img[alt="captcha img"]', state="visible", timeout=300000)
            logger.info("✅ 验证码图片已加载")

            for attempt in range(1, 6):
                logger.info(f"🔄 验证码识别尝试 #{attempt}/5")
                try:
                    captcha_element = page.query_selector('img[alt="captcha img"]')
                    if not captcha_element:
                        logger.error("❌ 无验证码图片")
                        raise 

                    captcha_img_path = Path("screenshots") / f"captcha_{task_id}_{attempt}.png"
                    captcha_element.screenshot(path=str(captcha_img_path))
                    logger.info(f"📸 验证码截图已保存: {captcha_img_path}")

                    logger.info("🧠 开始识别验证码...")
                    # captcha_solver.py 负责从环境变量获取API密钥
                    captcha_text = solve_captcha(str(captcha_img_path), page)
                    
                    # 如果验证码识别失败，跳过当前尝试
                    if not captcha_text:
                        logger.warning(f"⚠️ 第 {attempt} 次验证码识别失败，重试")
                        continue
                    logger.info(f"✅ 成功识别验证码: {captcha_text}")

                    # 填写验证码并提交
                    logger.info(f"⌨️ 填写验证码: {captcha_text}")
                    page.fill('input#basic_captcha', captcha_text)

                    logger.info("🖱️ 点击搜索按钮")
                    page.get_by_role("button", name=re.compile("Search", re.I)).click()

                    page.wait_for_selector("text=Get e-Visa", timeout=8000)
                    logger.info("✅ 搜索成功，下载按钮可用")

                    # 等待并触发下载
                    with page.expect_download() as download_info:
                        page.get_by_role("button", name=re.compile("Get e-Visa", re.I)).click()
                        logger.info("🖱️ 点击'Get e-Visa'按钮")

                    # 等待下载完成
                    download = download_info.value
                    logger.info("⏳ 正在等待文件保存...")
                    if not download:
                        logger.error("❌ 文件下载失败")
                        logger.debug(f"下载信息：{download_info}")
                        raise Exception("文件下载失败")

                    # 下载完成，保存文件
                    save_path = get_unique_filepath(final_path)
                    download.save_as(str(save_path))
                    logger.info(f"✅ PDF 下载完成并保存为：{save_path.name}")

                    # 验证文件是否真的存在
                    if save_path.exists():
                        file_size = save_path.stat().st_size
                        logger.info(f"✅ 文件验证成功: {save_path.name}, 大小: {file_size} 字节")
                    else:
                        logger.error(f"❌ 文件保存失败: {save_path.name} 不存在")

                        raise Exception("文件保存失败")
                    
                    # Check PDF integrity
                    try:
                        with open(save_path, "rb") as f:
                            reader = PdfReader(f)
                            _ = len(reader.pages)  # Try to access pages
                            logger.info("✅ 成功检查PDF文件:有效且完整")
                    except Exception as e:
                        logger.error(f"❌ 下载的 PDF 文件无效或不完整: {e}")
                        # Raise to trigger retry
                        raise Exception("下载的 PDF 文件无效或不完整")
                
                    return str(save_path.resolve())
                
                except PlaywrightTimeoutError:
                    logger.warning(f"⚠️ 第 {attempt} 次等待失败，重试验证码")
                    continue

        logger.error("❌ 多次验证码失败")
        raise Exception("多次验证码失败")

    except Exception as e:
        logger.warning(f"❌ 下载失败: {e}")
        raise 
    
    finally:
        # 关闭页面和上下文
        logger.info("✅ 清理资源并关闭浏览器")
        #context.close()   # 不需要在这里关闭资源，with sync_playwright() 会自动处理
        #browser.close()  # 不需要在这里关闭资源，with sync_playwright() 会自动处理
