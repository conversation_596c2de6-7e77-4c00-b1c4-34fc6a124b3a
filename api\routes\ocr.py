# api/routes/ocr.py
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.concurrency import run_in_threadpool
import tempfile
import os
from pathlib import Path

from ..models.responses import OCRResponse
from ..core.dependencies import require_auth
from ..core.exceptions import OCRProcessingError, FileProcessingError
from ..config.settings import settings
from app.utils.ocr_utils import run_aliyun_passport_ocr
from app.data.applicant_mapper import normalize_ocr_result_for_applicant

router = APIRouter(prefix="/ocr", tags=["OCR Processing"])


def validate_file(file: UploadFile) -> None:
    """验证上传的文件"""
    # 检查文件类型
    if file.content_type not in settings.allowed_file_types:
        raise FileProcessingError(
            f"不支持的文件类型: {file.content_type}。支持的类型: {', '.join(settings.allowed_file_types)}",
            "INVALID_FILE_TYPE"
        )
    
    # 检查文件大小（这里需要读取文件内容来检查大小）
    if hasattr(file, 'size') and file.size > settings.max_file_size:
        raise FileProcessingError(
            f"文件大小超过限制: {file.size} bytes。最大允许: {settings.max_file_size} bytes",
            "FILE_TOO_LARGE"
        )


async def save_temp_file(file: UploadFile) -> Path:
    """保存临时文件"""
    try:
        # 创建临时目录
        temp_dir = Path(settings.ocr_temp_dir)
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成临时文件名
        file_extension = Path(file.filename).suffix if file.filename else '.jpg'
        temp_file = temp_dir / f"ocr_{file.filename}_{os.getpid()}{file_extension}"
        
        # 保存文件
        content = await file.read()
        with open(temp_file, "wb") as f:
            f.write(content)
        
        return temp_file
    except Exception as e:
        raise FileProcessingError(
            f"保存临时文件失败: {str(e)}",
            "TEMP_FILE_ERROR"
        )


def cleanup_temp_file(file_path: Path) -> None:
    """清理临时文件"""
    try:
        if file_path.exists():
            file_path.unlink()
    except Exception:
        pass  # 忽略清理错误


@router.post("/passport", response_model=OCRResponse)
async def ocr_passport(
    passport_scan: UploadFile = File(..., description="护照扫描件"),
    user=Depends(require_auth)
):
    """
    护照OCR识别
    
    - **passport_scan**: 护照扫描件图片
    - **返回**: 识别出的护照信息
    """
    temp_file_path = None
    
    try:
        # 验证文件
        validate_file(passport_scan)
        
        # 保存临时文件
        temp_file_path = await save_temp_file(passport_scan)
        
        # 执行OCR识别（在线程池中运行，避免阻塞）
        ocr_result = await run_in_threadpool(
            run_aliyun_passport_ocr, 
            str(temp_file_path)
        )
        
        if not ocr_result:
            raise OCRProcessingError(
                "OCR识别失败，请确保图片清晰且为有效的护照页面",
                "OCR_FAILED"
            )
        
        # 标准化OCR结果
        normalized_fields = normalize_ocr_result_for_applicant(ocr_result)
        
        return OCRResponse(
            success=True,
            message="护照信息识别成功",
            fields=normalized_fields,
            confidence=ocr_result.get('confidence', 0.0),
            processing_time=ocr_result.get('processing_time', 0.0)
        )
        
    except (OCRProcessingError, FileProcessingError) as e:
        raise e
    except Exception as e:
        raise OCRProcessingError(
            f"OCR处理过程中发生错误: {str(e)}",
            "OCR_PROCESSING_ERROR"
        )
    finally:
        # 清理临时文件
        if temp_file_path:
            cleanup_temp_file(temp_file_path)


@router.post("/id-card", response_model=OCRResponse)
async def ocr_id_card(
    id_card_scan: UploadFile = File(..., description="身份证扫描件"),
    user=Depends(require_auth)
):
    """
    身份证OCR识别
    
    - **id_card_scan**: 身份证扫描件图片
    - **返回**: 识别出的身份证信息
    """
    temp_file_path = None
    
    try:
        # 验证文件
        validate_file(id_card_scan)
        
        # 保存临时文件
        temp_file_path = await save_temp_file(id_card_scan)
        
        # 这里可以添加身份证OCR识别逻辑
        # 暂时返回模拟数据
        return OCRResponse(
            success=True,
            message="身份证信息识别成功",
            fields={
                "name": "张三",
                "id_number": "110101199001011234",
                "address": "北京市朝阳区某某街道",
                "birth_date": "1990-01-01",
                "gender": "男",
                "ethnicity": "汉"
            },
            confidence=0.95,
            processing_time=1.5
        )
        
    except (OCRProcessingError, FileProcessingError) as e:
        raise e
    except Exception as e:
        raise OCRProcessingError(
            f"身份证OCR处理过程中发生错误: {str(e)}",
            "ID_CARD_OCR_ERROR"
        )
    finally:
        # 清理临时文件
        if temp_file_path:
            cleanup_temp_file(temp_file_path)


@router.get("/supported-formats")
async def get_supported_formats():
    """
    获取支持的文件格式
    
    - **返回**: 支持的文件格式列表
    """
    return {
        "success": True,
        "message": "支持的文件格式",
        "formats": {
            "image_types": settings.allowed_file_types,
            "max_file_size": settings.max_file_size,
            "max_file_size_mb": settings.max_file_size / (1024 * 1024)
        }
    }
