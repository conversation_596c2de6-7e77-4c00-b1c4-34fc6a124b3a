from concurrent.futures import ThreadPoolExecutor
import random
from app.utils.logger_config import get_logger
import atexit
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from app.email.email_executor import run_single_polling
from app.email.time_window import is_within_active_window
import pytz
import threading
import queue


logger = get_logger()

# 明确指定使用北京时间
beijing_tz = pytz.timezone('Asia/Shanghai')
# 设置调度器的时区为北京时间

# 创建一个全局线程池，用于执行所有邮件轮询任务
email_thread_pool = ThreadPoolExecutor(max_workers=5, thread_name_prefix="email_worker")
# 创建一个任务队列，用于在UI线程和工作线程之间传递任务
task_queue = queue.Queue()


scheduler = BackgroundScheduler(
    jobstores={"default": SQLAlchemyJobStore(url="sqlite:///jobs.db")},
    timezone=beijing_tz,
     job_defaults={
        "coalesce": True,
        "max_instances": 1,
        "misfire_grace_time": 60
    }
)

atexit.register(lambda: scheduler.shutdown(wait=False) if scheduler.running else None)
atexit.register(lambda: email_thread_pool.shutdown(wait=False))

def safe_run_polling(source, applicant):
    """安全运行轮询任务，避免在非活动时间执行"""
    logger.debug(f"准备执行固定时间段的轮询任务 - 来源: {source}, 申请人: {applicant.email}")

    if not is_within_active_window():
        logger.info(f"⏰ 当前非活动时间，跳过轮询任务: {applicant.email}")
        return
    
    # 提交到线程池执行，而不是在当前线程执行
    future = email_thread_pool.submit(_execute_polling_task, source, applicant)
    # 不等待结果，立即返回，避免阻塞调用线程
    return future

def _execute_polling_task(source, applicant):
    """在工作线程中执行实际的轮询任务"""
    logger.info(f"🔍 开始执行定时轮询任务: {source}, {applicant.email}")
    success = False
    try:
        logger.debug(f"执行轮询 - 模式: result_only, 仅检查出签结果邮件")
        success = run_single_polling(source, applicant, mode="result_only")   #result_only模式只轮询出签状态邮件，忽略其他邮件
        
        if success:
            logger.info(f"✅ 成功完成定时轮询任务: {source}, {applicant.email}")
        else:
            logger.warning(f"⚠️ 定时轮询任务未成功完成: {source}, {applicant.email}")
    except Exception as e:
        logger.error(f"❌ 轮询任务执行失败: {e}", exc_info=True)
        logger.debug(f"轮询失败详情 - 申请人: {applicant.email}, 异常: {str(e)}")

def schedule_hourly_polling(applicant):
    """为单个申请人注册固定时间的轮询任务（只查出签）"""
    logger.debug(f"为申请人 {applicant.email} 设置轮询任务")

     # 随机选择每小时的分钟数
    minute = random.randint(0, 59)
    trigger = CronTrigger(minute=minute)
    job_id = f"poll_email_{applicant.passport_number}"
    logger.debug(f"生成的任务ID: {job_id}, 触发时间: 每小时第 {minute} 分钟")
   
     # 清除可能存在的旧任务,清除已有该申请人的任务（如果存在）,避免重复
    if scheduler.get_job(job_id):
        logger.debug(f"发现已存在的任务 {job_id}，准备移除")
        scheduler.remove_job(job_id)
        logger.info(f"🗑️ 移除旧轮询任务: {job_id}")

    # 添加新任务
    scheduler.add_job(
        safe_run_polling,
        trigger=trigger,
        args=["scheduler", applicant],
        id=job_id,
        replace_existing=True,
        misfire_grace_time=60,
        coalesce=True,
        max_instances=1,
    )
    logger.debug(f"成功添加任务 {job_id} 到调度器")
    logger.info(f"✅ 添加固定时间轮询任务: {job_id} → 每小时第 {minute} 分钟")

def start_polling_scheduler(applicant_list):
    """批量注册定时轮询任务，用于初始化系统时调用"""
    logger.debug(f"开始批量注册轮询任务，申请人数量: {len(applicant_list)}")

    # 清理所有现有任务
    for applicant in applicant_list:
        schedule_hourly_polling(applicant)
        logger.info(f"✅ 添加定时轮询任务: {applicant.email}")
    
    # 启动调度器时，检查是否已经在运行
    if not scheduler.running:
        logger.debug("调度器未运行，准备启动")
        scheduler.start()
        logger.info("✅ APScheduler 已启动（持久化+异常处理+窗口限制+防堆积）")
        
    else:
        logger.debug("调度器已在运行，准备注册任务")
    
    # 清理所有现有任务 - 无论调度器是新启动还是已经在运行，都需要清理
    # 因为新启动的调度器可能从持久化存储中恢复了旧任务
    for job in scheduler.get_jobs():
        if job.id.startswith("poll_email_"):
            logger.debug(f"准备移除任务: {job.id}")
            scheduler.remove_job(job.id)
            logger.debug(f"🗑️ 清理旧任务: {job.id}")
            #logger.info(f"🧹 清理旧任务: {job.id}")

    # 为每个申请人添加新任务
    for applicant in applicant_list:
        schedule_hourly_polling(applicant)
        logger.info(f"✅ 添加固定时间轮询任务: {applicant.email}")
    
    logger.info("✅ 启动轮询调度器: " + ", ".join([app.email for app in applicant_list]))
   
    # 打印所有已注册的任务，用于调试
    logger.info("📋 当前注册的所有基于固定时间的轮询任务:")
    for job in scheduler.get_jobs():
        logger.info(f"  - {job.id}: {job.trigger}")



