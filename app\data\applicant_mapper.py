# app/data/applicant_mapper.py
"""
表单数据到Applicant对象的映射器
"""
from typing import Dict, Any
from .models import Applicant
from app.utils.logger_config import get_logger

logger = get_logger()


def map_form_to_applicant(form_data: Dict[str, Any]) -> Applicant:
    """
    将表单数据映射为Applicant对象
    
    Args:
        form_data: 来自前端表单的数据字典
        
    Returns:
        Applicant: 映射后的申请人对象
    """
    try:
        # 创建Applicant对象，使用表单数据
        applicant = Applicant(
            # 基本信息
            surname=form_data.get("surname", ""),
            given_name=form_data.get("given_name", ""),
            chinese_name=form_data.get("chinese_name", ""),
            sex=form_data.get("sex", ""),
            dob=form_data.get("dob", ""),
            place_of_birth=form_data.get("place_of_birth", ""),
            nationality=form_data.get("nationality", "CHINA"),
            religion=form_data.get("religion", "NO"),
            
            # 护照信息
            passport_number=form_data.get("passport_number", ""),
            passport_type=form_data.get("passport_type", "Ordinary passport"),
            place_of_issue=form_data.get("place_of_issue", ""),
            date_of_issue=form_data.get("date_of_issue", ""),
            passport_expiry=form_data.get("passport_expiry", ""),
            
            # 联系信息
            email=form_data.get("email", ""),
            telephone_number=form_data.get("telephone_number", ""),
            permanent_address=form_data.get("permanent_address", ""),
            contact_address=form_data.get("contact_address", ""),
            
            # 紧急联系人
            emergency_contact_name=form_data.get("emergency_contact_name", ""),
            emergency_address=form_data.get("emergency_address", ""),
            emergency_contact_phone=form_data.get("emergency_contact_phone", ""),
            
            # 签证信息
            visa_entry_type=form_data.get("visa_entry_type", ""),
            visa_validity_duration=form_data.get("visa_validity_duration", ""),
            visa_start_date=form_data.get("visa_start_date", ""),
            intended_entry_gate=form_data.get("intended_entry_gate", ""),
            
            # 文件路径（如果有的话）
            portrait_photo_path=form_data.get("portrait_photo_path", ""),
            passport_scan_path=form_data.get("passport_scan_path", ""),
            
            # 其他字段使用默认值
            customer_source=form_data.get("customer_source", "API"),
        )
        
        logger.info(f"成功映射表单数据到Applicant对象: {applicant.surname} {applicant.given_name}")
        return applicant
        
    except Exception as e:
        logger.error(f"映射表单数据到Applicant对象失败: {e}")
        raise ValueError(f"表单数据映射失败: {str(e)}")


def normalize_ocr_result_for_applicant(ocr_result: Dict) -> Dict:
    """
    将OCR识别结果格式化为表单字段格式
    
    Args:
        ocr_result: OCR识别的原始结果
        
    Returns:
        Dict: 格式化后的字段字典
    """
    try:
        # 1. 姓名拆分
        name_raw = ocr_result.get("name", "")
        if "," in name_raw:
            surname, given_name = name_raw.split(",", 1)
            surname = surname.strip()
            given_name = given_name.strip()
        else:
            surname = name_raw.strip()
            given_name = ""

        # 2. 出生地处理（提取拼音）
        birth_place_raw = ocr_result.get("birth_place_raw", "")
        if birth_place_raw and "/" in birth_place_raw:
            parts = birth_place_raw.split("/")
            if len(parts) > 1:
                place_of_birth = parts[1].strip().upper()
            else:
                place_of_birth = "GUANGDONG"
        elif birth_place_raw and birth_place_raw.isascii():
            place_of_birth = birth_place_raw.strip().upper()
        else:
            place_of_birth = "GUANGDONG"

        # 3. 格式化结果
        normalized_result = {
            "chinese_name": ocr_result.get("name_cn", "") or ocr_result.get("name_cn_raw", ""),
            "surname": surname,
            "given_name": given_name,
            "sex": ocr_result.get("sex", ""),
            "nationality": ocr_result.get("country", "CHINA"),
            "dob": ocr_result.get("birth_date", ""),
            "passport_number": ocr_result.get("passport_no", ""),
            "place_of_birth": place_of_birth,
            "place_of_issue": ocr_result.get("issue_place", ""),
            "date_of_issue": ocr_result.get("issue_date", ""),
            "passport_expiry": ocr_result.get("expiry_date", ""),
            "passport_type": "Ordinary passport",  # 固定值
        }
        
        logger.info(f"成功格式化OCR结果: {normalized_result.get('surname')} {normalized_result.get('given_name')}")
        return normalized_result
        
    except Exception as e:
        logger.error(f"格式化OCR结果失败: {e}")
        return {}
