from app.utils.logger_config import get_logger
from app.utils.ocr_utils import run_aliyun_passport_ocr

logger = get_logger()

def extract_passport_info_via_ocr(image_path: str) -> dict:
    logger.info(f"[批量OCR] 开始处理护照图片: {image_path}")
    ocr_result = run_aliyun_passport_ocr(image_path)

    if not ocr_result:
        logger.warning("OCR识别失败，结果为空")
        return {}

    # 字段映射逻辑（完全与 vietnam_tab 中 run_passport_ocr 一致）
    result = {}

    # 拆分中英文名
    name_raw = ocr_result.get("name", "")
    if "," in name_raw:
        surname, given_name = name_raw.split(",", 1)
    else:
        surname = name_raw
        given_name = ""

    result["surname"] = surname
    result["given_name"] = given_name
    result["chinese_name"] = ocr_result.get("name_cn", "") or ocr_result.get("name_cn_raw", "")
    result["sex"] = ocr_result.get("sex", "")
    result["nationality"] = ocr_result.get("country", "")
    result["dob"] = ocr_result.get("birth_date", "")
    result["passport_number"] = ocr_result.get("passport_no", "")
    result["place_of_issue"] = ocr_result.get("issue_place", "")
    result["date_of_issue"] = ocr_result.get("issue_date", "")
    result["passport_expiry"] = ocr_result.get("expiry_date", "")

    # 处理出生地，从"河南 /HENAN"格式中提取"HENAN"(与vietnam_tab.py里面的run_passport_ocr(self, image_path: str) 保持一致)
    birth_place_raw = ocr_result.get("birth_place_raw", "")
    if birth_place_raw and "/" in birth_place_raw:
        # 分割并获取拼音部分
        parts = birth_place_raw.split("/")
        if len(parts) > 1:
            birth_place_pinyin = parts[1].strip().upper()  # 提取并大写
            result["place_of_birth"] = birth_place_pinyin
            logger.info(f"从'{birth_place_raw}'中提取出生地拼音: '{birth_place_pinyin}'")
        else:
            result["place_of_birth"] = "GUANGDONG"  # 默认值
            logger.warning(f"出生地格式异常，使用默认值GUANGDONG: '{birth_place_raw}'")
    elif birth_place_raw and birth_place_raw.isascii():
        # 如果是纯ASCII，假设已经是拼音
        result["place_of_birth"] = birth_place_raw.strip().upper()
        logger.info(f"使用ASCII出生地: '{result['place_of_birth']}'")
    else:
        # 如果为空或非ASCII且不包含/，使用默认值
        result["place_of_birth"] = "GUANGDONG"
        logger.warning(f"无法识别出生地，使用默认值GUANGDONG: '{birth_place_raw}'")

    return result
