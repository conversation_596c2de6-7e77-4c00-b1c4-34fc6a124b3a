"""
Logger Configuration Module

Configures and initializes the application logging system.
"""
import os
import sys
from pathlib import Path
from datetime import datetime
from loguru import logger

def setup_logger(
        log_dir: str = "logs",
        console_level: str = "INFO",
        file_level: str = "DEBUG",
        log_filename_prefix: str = "vietnam_evisa_app"  # <--- 1. 添加这个参数，并给一个合适的默认值
) -> None:
    """
    Set up the application logger.
    
    Configures the logger to output to both console and log files.
    
    Args:
        log_dir: Directory to store log files
        console_level: Log level for console output
        file_level: Log level for file output
    """
    # Create log directory if it doesn't exist
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)
    
    # Generate log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = log_path / f"{log_filename_prefix}_{timestamp}.log"
    
    # Remove default handler
    logger.remove()
    
    # Add console handler
    logger.add(
        sys.stderr,
        level=console_level.upper(), # 将级别转为大写，loguru 期望大写级别字符串
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # Add file handler
    logger.add(
        str(log_filename), # loguru 的 add sink 参数通常期望字符串路径
        level=file_level.upper(), # 将级别转为大写
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",  # Rotate when file reaches 10 MB
        retention="1 week",  # Keep logs for 1 week
        compression="zip"  # Compress rotated logs
    )
    
    logger.info(f"Logger initialized. Log file: {log_filename}")
    
def get_logger():
    """
    Get the configured logger.
    
    Returns:
        Configured logger instance
    """
    return logger 
