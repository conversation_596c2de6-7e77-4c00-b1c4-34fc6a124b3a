# --- START OF FILE vietnam_filler.py ---

# 文件: app/fillers/vietnam_filler.py (Refactored V25 - Hierarchical Locators)
#from time import sleep
from playwright.sync_api import Page, TimeoutError as PlaywrightTimeoutError, expect
from pathlib import Path
from datetime import datetime, timedelta
import json 
from typing import Optional, Any # Added Any for type hint
import re
import os
import sys  # 添加sys模块导入
import subprocess  # 添加subprocess模块导入
import tempfile  # 添加tempfile模块导入
from app.utils.logger_config import get_logger
import time  

# Assuming these imports are correct and modules exist
from app.fillers.interface import VisaFillerInterface
from app.data.models import Applicant
from app.utils.address_tools import generate_random_address, CHINA_PROVINCE_CITY_MAP
from app.utils.captcha_solver import solve_captcha
# --- 核心内建模块，用于文件读取、随机抽样、类型提示 ---
import json                      # 解析 JSON 地址数据文件
import random                    # 从地址列表中随机选择一条
from typing import Optional      # 用于标注可选参数类型
# --- 外部库：Playwright 浏览器自动化 ---
from playwright.sync_api import Page  # 页面对象类型，用于类型注释
# --- 项目内模块：申请人模型  ---
from app.data.models import Applicant     # 数据模型：包含 intended_entry_gate 等字段


DEFAULT_TIMEOUT_MS = 3000
ADDRESS_JSON_PATH = "app/data/vietnam_addresses_structured.json"



logger = get_logger()



class VietnamFiller(VisaFillerInterface):
    """Fills the Vietnam E-Visa online application form using Playwright."""
    """
    VietnamFiller 依赖父类提供以下方法：
    - self.get_locator(locator_key: str) -> str
    - self._safe_action(callable, description: str) -> bool
    - self._input_and_enter(page, input_locator, trigger_locator, value, description) -> bool
    """

    # --- Constants (moved potentially variable strings here) ---
    DEFAULT_RELIGION = "NO"
    DEFAULT_EMERGENCY_RELATIONSHIP = "Friend"
    DEFAULT_PURPOSE_OF_ENTRY = "Tourist"
    DEFAULT_EMERGENCY_NAME_PREFIX = "Mr "
    DEFAULT_BIRTH_PLACE_FALLBACK_ZH = "GUANGDONG" # Assume Pinyin is expected by API?

    # --- Initialization and Preparation ---
    def __init__(self):
        """Initializes the filler instance."""
        self.locators: dict = {}
        self.settings: dict = {}
        self.base_url: str = ""
        self._vietnam_addresses: Optional[dict[str, list[dict[str, str]]]] = None # 缓存越南地址结构数据（懒加载）
        self.session_id = None  # 新增 会话ID，默认为None，由SimpleEngine设置
        self.application_number = None  # 新增 申请编号，默认为None，由fill_step1_personal_info设置
       

    def prepare(self, all_locators: dict, settings: dict): # Rename parameter for clarity
        """
        Prepares the filler using the relevant section of the loaded locators.
        """
        # --- !!! 从传入的字典中获取越南的部分 !!! ---
        self.locators = all_locators.get('vietnam_evisa', {}) # Use the filename stem as the key
        self.settings = settings
        self.base_url = settings.get('vietnam_evisa_url')

        if not self.locators:
             # 如果连 'vietnam_evisa' 这个键都没有，或者其值为空字典
             logger.critical("❌ CRITICAL: 未在加载的总定位器配置中找到 'vietnam_evisa' 部分！检查 config/locators/ 目录及文件。")
             # 这里可能应该抛出异常而不是仅仅警告，因为没有定位器无法工作
             raise ValueError("Configuration for 'vietnam_evisa' locators is missing or empty.")
        elif not self.base_url:
             raise ValueError("Vietnam E-Visa URL not configured in settings.")
        else:
             logger.info(f"Vietnam locators structure loaded (top keys): {list(self.locators.keys())}")
             logger.info("Vietnam E-Visa filler prepared.")


    def _safe_action(self, action_func, description):
        """Safely executes a Playwright action, results."""
        try:
            action_func()
            logger.info(f"✅ Success: {description}")
            return True
        except PlaywrightTimeoutError as timeout_error:
            message = str(timeout_error).split('\n')[0]
            logger.error(f"❌ Failure: Timeout - {description} - {message}")
            return False
        except Exception as e:
            logger.error(f"❌ Failure: Error - {description} - {e}", exc_info=True)
            return False

    # --- Updated get_locator to handle dot notation ---
    def get_locator(self, key_path: str) -> Optional[str]:
        """
        Retrieves a locator value from the potentially nested locators dictionary
        using a dot-separated key path.

        Args:
            key_path: The dot-separated path to the locator (e.g., "homepage.apply_now_button").

        Returns:
            The locator string if found and is a string, otherwise None. Logs warnings/errors.
        """
        if not self.locators: # Check if locators were loaded at all
             logger.error("Locators dictionary is empty, cannot get locator.")
             return None

        keys = key_path.split('.')
        current_level: Any = self.locators # Start at the top level dictionary
        path_traversed = []

        try:
            for key in keys:
                path_traversed.append(key)
                if isinstance(current_level, dict):
                    current_level = current_level.get(key)
                    if current_level is None:
                        # Key not found at this level
                        logger.warning(f"⚠️ Configuration missing locator key '{key}' at path: '{'.'.join(path_traversed)}'")
                        return None
                else:
                    # Tried to access a key on a non-dictionary item along the path
                    logger.warning(f"⚠️ Invalid locator structure: Expected a dictionary at '{'.'.join(path_traversed[:-1])}' but got {type(current_level)}. Cannot access key '{key}'.")
                    return None

            # After iterating through all keys, check the final value
            if isinstance(current_level, str):
                 logger.info(f"Retrieved locator for '{key_path}': '{current_level}'")
                 return current_level
            else:
                 logger.warning(f"⚠️ Locator value found for path '{key_path}' is not a string (type: {type(current_level)}). Value: {current_level}")
                 return None # Return None if the final value isn't a string

        except Exception as e: # Catch potential errors during access
            logger.error(f"❌ Error accessing locator path '{key_path}': {e}")
            return None


    def _calculate_end_date(self, start_date_str: str, duration_text: str) -> Optional[str]:
        """Calculates the visa end date."""
        try:
            start_date = datetime.strptime(start_date_str, "%d/%m/%Y")
            days_to_add = 0
            if duration_text == "30天": days_to_add = 29
            elif duration_text == "90天": days_to_add = 89
            else: logger.error(f"❓ Unknown visa duration: '{duration_text}'"); return None
            end_date = start_date + timedelta(days=days_to_add)
            return end_date.strftime("%d/%m/%Y")
        except Exception as e:
            logger.error(f"❌ Error calculating end date: {e}", exc_info=True)
            return None


    def fill_step1_personal_info(self, page: Page, applicant: Applicant):
        """Main method to fill the form."""
        logger.info(f"🚀 Starting Step 1: Personal Info Filling Process,会话ID: {self.session_id}, 申请人: {applicant.passport_number}")
        # Log received data (moved here from original code)
        # Use json.dumps for potentially better formatting of the dataclass dict
        try:
             from dataclasses import asdict
             logger.info(f"Received applicant data:{json.dumps(asdict(applicant), indent=2, ensure_ascii=False, default=str)}")
        except ImportError:
             logger.info(f"Received applicant data: {applicant}") # Fallback if asdict not available
        except Exception as json_err:
             logger.warning(f"Could not json-format applicant data for logging: {json_err}")
             logger.info(f"Received applicant data (raw): {applicant}")


        if not self.locators:
            logger.critical("❌ Locators not loaded. Aborting.")
            return False # Critical failure

        # --- Auto-generate address if missing ---
        self._ensure_addresses(applicant) # Pass applicant to modify it directly

        # --- Handle Initial Page and Modal ---
        if not self._handle_initial_steps(page):
             logger.critical("❌ Failed during initial steps (Apply button or Modal). Aborting.")
             return False # Critical failure

        # --- Handle File Uploads ---
        # File uploads might fail, but we might want to continue filling the form
        self._handle_file_uploads(page, applicant)

        # --- Wait for Website's OCR ---
        self._wait_for_ocr(page) # Warns on timeout but continues

        # --- Fill Main Form Fields ---
        logger.info("📝 Starting main form field filling...")
        page.wait_for_timeout(500) # Stabilization pause

        # Use a flag to track if any critical fill operation failed
        fill_success = True

        # --- Section: Personal Info ---
        if not self._fill_personal_info(page, applicant): fill_success = False

        # --- Section: Passport Details ---
        if not self._fill_passport_details(page, applicant): fill_success = False

        # --- Section: Contact Info (including addresses) ---
        if not self._fill_contact_info(page, applicant): fill_success = False

        # --- Section: Travel Info ---
        if not self._fill_travel_info(page, applicant): fill_success = False

        # --- Section: Emergency Contact ---
        if not self._fill_emergency_contact(page, applicant): fill_success = False

        # --- Section: Previous Visits ---
        # This section might be optional depending on answers, don't let its failure block everything?
        self._handle_previous_visits(page, applicant) # Log errors inside

        # --- Section: Final Declarations ---
        if not self._handle_final_declarations(page):
            logger.warning("⚠️ Failed to check final declarations. Form might be incomplete.")
            # Decide if this should be a critical failure: fill_success = False

        # --- Click Next button to proceed ---
        if fill_success:
            next_button_clicked = self._handle_next_button(page)
            if not next_button_clicked:
                logger.warning("⚠️ Failed to click Next button. Form submission might be incomplete.")
                fill_success = False
            else:
                # 如果成功点击了Next按钮，等待跳转验证码页面
                logger.info("🔄 主表单页面填充完成，已点击Next按钮，等待跳转到验证码页面...")

                # --- 验证跳转到验证码页面 ---
                captcha_image_loc = self.get_locator('captcha_page.captcha_image')
                # 等待验证码图片出现，表示页面已跳转
                try:
                    page.wait_for_selector(captcha_image_loc, state="visible", timeout=5000)
                    logger.info("✅ 成功跳转到验证码页面")
                    fill_success = True
                except PlaywrightTimeoutError:
                    logger.error("❌ 超时未能跳转到验证码页面,请检查填表页面是否成功填充")
                    fill_success = False
                    return fill_success  # 直接返回，不执行后续代码

                # 处理验证码页面
                captcha_success = self.handle_captcha_page(page)
                if not captcha_success:
                    logger.error("❌ 验证码处理失败")
                    fill_success = False
                else:
                    logger.info("✅ 验证码处理成功，准备进入下一个页面...")
                     # ✅ 添加此步骤：点击Confirm按钮进入付款页
                    try:
                        confirmation_success = self._handle_confirmation_flow(page)

                        if not confirmation_success:
                            logger.error("❌ 确认流程处理失败")
                            fill_success = False
                        # 在确认流程成功后，执行付款流程
                        # 注意：_handle_confirmation_flow会关闭浏览器，所以我们不能在这里继续使用page
                        elif hasattr(self, 'payment_url') and self.payment_url:
                            logger.info(f"✅ 填表流程成功执行，会话ID: {self.session_id}, 申请人: {applicant.passport_number}已获取支付URL: {self.payment_url}")
                            
                    except Exception as e:
                        logger.error(f"❌ 执行确认流程时出错: {e}")
                        fill_success = False

        
        try:
            # 安全返回之前，确保 Qt 相关资源解绑
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(0, lambda: None)  # 刷新 Qt 事件循环
        except Exception:
            pass  # 忽略任何错误，确保函数可以返回
        return fill_success

    def _handle_next_button(self, page: Page) -> bool:
        """
        Waits for the Next button to become enabled and clicks it.
        Returns True if successful, False otherwise.
        """
        logger.info("⏭️ Attempting to click Next button...")

        # Locate the Next button
        next_button_loc = self.get_locator('main_form.next_button')
        if not next_button_loc:
            logger.error("❌ Next button locator not found.")
            return False

        try:
            # First make sure the button exists
            button = page.locator(next_button_loc)
            button.wait_for(state="visible", timeout=5000)
            logger.info("✅ Next button found on page.")

            # Check if button is disabled
            is_disabled = button.get_attribute("disabled") == "true" or button.get_attribute("disabled") == ""
            if is_disabled:
                logger.info("⏳ Next button is currently disabled. Checking for form errors...")

                # Wait a bit and check for form validation errors that might be visible
                page.wait_for_timeout(1000)

                # Look for potential validation error messages
                error_messages = page.locator('.ant-form-item-explain-error').all()
                if error_messages:
                    error_texts = [err.text_content() for err in error_messages if err.is_visible()]
                    if error_texts:
                        logger.error(f"❌ Form has validation errors: {', '.join(error_texts)}")
                        # Take screenshot of the form with errors
                        page.screenshot(path="form_validation_errors.png")
                        return False

                # Wait for the button to become enabled (no longer having the disabled attribute)
                logger.info("⏳ Waiting for Next button to become enabled (max 15 seconds)...")
                page.wait_for_selector(f"{next_button_loc}:not([disabled])", timeout=15000)
                logger.info("✅ Next button is now enabled.")

            # Scroll to make sure button is in view
            button.scroll_into_view_if_needed()
            page.wait_for_timeout(500)  # Small pause to ensure it's ready for clicking

            # Attempt to click the button
            button.click()
            logger.info("✅ Successfully clicked Next button.")

            # Wait for page transition indication (can be customized based on the site's behavior)
            page.wait_for_timeout(2000)  # Wait for page to transition

            return True
        except PlaywrightTimeoutError:
            logger.error("❌ Timeout waiting for Next button to become enabled. Form may have validation errors.")
            # Take screenshot for debugging
            page.screenshot(path="next_button_timeout.png")
            return False
        except Exception as e:
            logger.error(f"❌ Error clicking Next button: {e}")
            # Take screenshot for debugging
            page.screenshot(path="next_button_error.png")
            return False

    def _fill_vietnam_address(self, page: Page, applicant: Applicant, address_path: Optional[str] = None) -> bool:
        """
        填写越南地址的 4 个字段：街道、省市、区、乡。
        """
        port = applicant.intended_entry_gate
        logger.info(f" 开始填充地址，入境口岸: {port}")

        try:
            addresses = self._get_vietnam_addresses(address_path)
            address_list = addresses.get(port)
            if not address_list:
                logger.warning(f" 未找到该口岸对应地址数据: {port}")
                return False
            address = random.choice(address_list)
        except FileNotFoundError:
            logger.error(f" 地址文件未找到: {address_path or ADDRESS_JSON_PATH}")
            return False
        except json.JSONDecodeError:
            logger.error(" 地址文件格式错误，JSON 无法解析")
            return False
        except Exception as e:
            logger.exception(f" 加载地址数据失败: {e}")
            return False

        required = ["street", "province_city", "district"]
        missing = [k for k in required if not address.get(k)]
        if missing:
            logger.warning(f" 地址字段缺失: {missing} -> {address}")
            return False

        return all([
            self._fill_street(page, address["street"]),
            self._fill_province_city(page, address["province_city"]),
            self._fill_district(page, address["district"]),
            self._fill_ward(page),
        ])

    def _fill_street(self, page: Page, street: str) -> bool:
        locator = self.get_locator("main_form.vietnam_address.residential_address_input")
        return self._safe_action(lambda: page.fill(locator, street), f" 填写街道: {street}")

    def _fill_province_city(self, page: Page, value: str) -> bool:
        input_loc = self.get_locator("main_form.vietnam_address.province_city_input")
        trigger_loc = self.get_locator("main_form.vietnam_address.province_city_select")

        logger.info(f" 开始填充省市: {value}")

        # 恢复使用_input_and_enter方式，但增加更好的日志记录
        ok = self._input_and_enter(page, input_loc, trigger_loc, value, f" 省市: {value}")

        if ok:
            try:
                # 等待区选择器加载，表示省市已成功选择
                page.wait_for_selector(self.get_locator("main_form.vietnam_address.district_select"), timeout=DEFAULT_TIMEOUT_MS)
            except Exception as e:
                logger.error(f" 等待区选择框加载失败: {e}")
                page.screenshot(path="error_district_select_not_loaded.png")
                return False

        return ok

    def _fill_district(self, page: Page, value: str) -> bool:
        # 特殊处理：Unknown区名处理
        if value.lower() == "unknown" or value.lower() == "none":
            logger.info(f" 检测到未知区名: {value}, 将使用备选值")
            district_value = "1"
        else:
            district_value = value
            # 匹配Quận X格式，转换为District X
            district_match = re.match(r"Quận\s+(\d+)", value)
            if district_match:
                district_num = district_match.group(1)
                district_value = f"District {district_num}"
                logger.info(f" 区名转换: {value} -> {district_value}")

        input_loc = self.get_locator("main_form.vietnam_address.district_input")
        trigger_loc = self.get_locator("main_form.vietnam_address.district_select")

        logger.info(f" 开始填充区域: {district_value}")

        # 确保区域选择框已激活可点击
        try:
            page.wait_for_selector(trigger_loc, state="visible", timeout=5000)
            logger.info(" 区域选择框已可见")
        except Exception as e:
            logger.error(f" 等待区域选择框可见失败: {e}")
            page.screenshot(path="error_district_select_not_visible.png")
            return False

        # 使用修改后的值填充区域
        ok = self._input_and_enter(page, input_loc, trigger_loc, district_value, f" 区域: {district_value}")

        if ok:
            try:
                # 等待街道选择器加载，表示区域已成功选择
                page.wait_for_selector(self.get_locator("main_form.vietnam_address.ward_commune_select"), timeout=DEFAULT_TIMEOUT_MS)
                logger.info(" 区域选择成功，街道选择框已加载")
                return True
            except Exception as e:
                logger.error(f" 等待街道选择框加载失败: {e}")
                page.screenshot(path="error_ward_select_not_loaded.png")
                return False

        return False

    def _fill_ward(self, page: Page) -> bool:
        trigger = self.get_locator("main_form.vietnam_address.ward_commune_select")
        return all([
            self._safe_action(lambda: page.locator(trigger).click(), " 点击乡/镇下拉"),
            self._safe_action(lambda: page.keyboard.press("ArrowDown"), " 选择第一项"),
            self._safe_action(lambda: page.keyboard.press("Enter"), " 确认乡/镇")
        ])

    def _get_vietnam_addresses(self, path: Optional[str] = None) -> dict:
        if self._vietnam_addresses is None:
            with open(path or ADDRESS_JSON_PATH, "r", encoding="utf-8") as f:
                self._vietnam_addresses = json.load(f)
        return self._vietnam_addresses


    # --- Helper methods for logical sections ---

    def _ensure_addresses(self, applicant: Applicant) -> None:

        if all([applicant.contact_address, applicant.permanent_address, applicant.emergency_address]):
            return

        logger.info("🔧 One or more address fields are missing, generating default address...")

        # 直接使用 place_of_birth，因为它已经是大写拼音
        province_pinyin = applicant.place_of_birth
        if not province_pinyin:
            province_pinyin = "GUANGDONG"
            logger.warning("⚠️ 出生地为空，使用默认值: 'GUANGDONG'")
        else:
            logger.info(f"✅ 使用出生地拼音: {province_pinyin}")

        if province_pinyin not in CHINA_PROVINCE_CITY_MAP:
            logger.warning(f"⚠️ '{province_pinyin}' 不在地址映射中，使用默认 'GUANGDONG'")
            province_pinyin = "GUANGDONG"

        random_address = generate_random_address(province_pinyin)

        if random_address:
            applicant.permanent_address = applicant.permanent_address or random_address
            applicant.contact_address = applicant.contact_address or random_address
            applicant.emergency_address = applicant.emergency_address or random_address
            logger.info(f"✅ 生成地址: {random_address}")
        else:
            logger.error("❌ 地址生成失败")


    def _handle_initial_steps(self, page: Page) -> bool:
        """Handles clicking 'Apply Now', the modal, and waiting for the form."""
        logger.info("✅ Handling initial steps: Apply Now & Terms Modal...")

        # Click Apply Now using hierarchical key
        if not self._safe_action(
            lambda: page.locator(self.get_locator('homepage.apply_now_button')).click(),
            "Click 'Apply now' button"
        ): return False

        # Modal interactions using hierarchical keys
        # scroll_container_loc = self.get_locator('terms_modal.scrollable_container') # Scrolling if needed
        if not self._safe_action(
            lambda: page.locator(self.get_locator('terms_modal.accept_terms_checkbox')).check(),
            "Check first confirmation checkbox (Laws)"
        ): return False
        if not self._safe_action(
            lambda: page.locator(self.get_locator('terms_modal.confirm_read_checkbox')).check(),
            "Check second confirmation checkbox (Read)"
        ): return False

        # Modal Next button
        modal_next_locator_str = self.get_locator('terms_modal.next_button')
        if not modal_next_locator_str: return False # Locator itself missing
        try:
            expect(page.locator(modal_next_locator_str)).not_to_have_class("bg-evisa-gray300", timeout=10000)
            logger.info("✅ 'Next' button is enabled.")
        except Exception as e:
            logger.error(f"❌ Timeout/Error waiting for 'Next' button enable: {e}")
            return False
        if not self._safe_action(lambda: page.locator(modal_next_locator_str).click(), "Click 'Next' button in modal"):
             return False

        # Wait for Main Form anchor using hierarchical key
        form_anchor_loc = self.get_locator('main_form.anchor_element')
        if not form_anchor_loc: return False
        logger.info("⏳ Waiting for main form page to load...")
        if not self._safe_action(
            lambda: page.locator(form_anchor_loc).wait_for(state="visible", timeout=45000),
            "Wait for main form anchor element"
        ):
            logger.critical("❌ Main form failed to load within timeout.")
            return False
        logger.info("✅ Main form loaded.")
        return True

    def _handle_file_uploads(self, page: Page, applicant: Applicant) -> bool:
        """Handles uploading files."""
        logger.info("✅ Handling file uploads...")
        all_uploads_attempted_ok = True
        # Portrait photo
        photo_loc = self.get_locator('main_form.file_uploads.portrait_photo_input')
        if photo_loc and applicant.portrait_photo_path:
             photo_path = Path(applicant.portrait_photo_path)
             if photo_path.is_file() and photo_path.suffix.lower() in ['.jpg', '.jpeg', '.png']:
                  if not self._safe_action(lambda: page.locator(photo_loc).set_input_files(photo_path), f"Upload portrait photo '{photo_path.name}'"):
                       all_uploads_attempted_ok = False
             else:
                  logger.error(f"❌ Invalid portrait photo file: '{applicant.portrait_photo_path}'")
                  all_uploads_attempted_ok = False
        elif not photo_loc: all_uploads_attempted_ok = False # Logged by get_locator
        else: logger.info("ℹ️ Skipping portrait photo upload: Path not provided.")

        # Passport scan
        passport_loc = self.get_locator('main_form.file_uploads.passport_scan_input')
        if passport_loc and applicant.passport_scan_path:
            passport_path = Path(applicant.passport_scan_path)
            if passport_path.is_file() and passport_path.suffix.lower() in ['.jpg', '.jpeg', '.png']:
                 if not self._safe_action(lambda: page.locator(passport_loc).set_input_files(passport_path), f"Upload passport scan '{passport_path.name}'"):
                      all_uploads_attempted_ok = False
            else:
                 logger.error(f"❌ Invalid passport scan file: '{applicant.passport_scan_path}'")
                 all_uploads_attempted_ok = False
        elif not passport_loc: all_uploads_attempted_ok = False
        else: logger.info("ℹ️ Skipping passport scan upload: Path not provided.")

        logger.info(f"File upload attempts finished (overall success state: {all_uploads_attempted_ok}).")
        return all_uploads_attempted_ok


    def _wait_for_ocr(self, page: Page) -> None:
        """Waits for website OCR to partially populate the form."""
        logger.info("⏳ Waiting for website OCR activity (max 10s)...")
        try:
            # Using hierarchical keys now
            surname_loc = self.get_locator('main_form.personal_info.surname_input')
            dob_loc = self.get_locator('main_form.personal_info.dob_input')
            nationality_loc = self.get_locator('main_form.personal_info.nationality_select')

            if not all([surname_loc, dob_loc, nationality_loc]):
                raise ValueError("Missing one or more locators for OCR check.")

            js_check = f"""
            () => {{
                const s = document.querySelector('{surname_loc}')?.value;
                const d = document.querySelector('{dob_loc}')?.value;
                const n = document.querySelector('{nationality_loc}')?.querySelector('.ant-select-selection-item');
                return (s && s.trim()!=='') || (d && d.trim()!=='') || n != null;
            }}
            """
            page.wait_for_function(js_check, timeout=10000)
            logger.info("✅ Detected OCR activity or field pre-population.")
        except ValueError as ve:
            logger.error(f"❌ Cannot wait for OCR: {ve}")
        except Exception as e:
            logger.warning(f"⚠️ Timeout/Error waiting for website OCR: {e}. Continuing form fill...")


    def _fill_personal_info(self, page: Page, applicant: Applicant) -> bool:
        """Fills fields in the Personal Info section."""
        logger.info("📝 Filling Personal Info...")
        success = True

        # Place of Birth
        pob_pinyin = applicant.place_of_birth
        if not pob_pinyin:
            pob_pinyin = "GUANGDONG"
            logger.warning(f"⚠️ 出生地为空，使用默认值: GUANGDONG")
        else:
            logger.info(f"✅ 使用出生地: {pob_pinyin}")

        logger.info(f"Attempting to fill Place of Birth with Pinyin: {pob_pinyin}")
        if not self._safe_action(
            lambda: page.locator(self.get_locator('main_form.personal_info.place_of_birth_input')).fill(pob_pinyin),
            f"Fill Place of Birth (Pinyin)"
        ):
            success = False

        # Religion
        if not self._safe_action(
             lambda: page.locator(self.get_locator('main_form.personal_info.religion_input')).fill(applicant.religion or self.DEFAULT_RELIGION),
             f"Fill Religion with '{applicant.religion or self.DEFAULT_RELIGION}'"
        ): success = False
        return success

    def _fill_passport_details(self, page: Page, applicant: Applicant) -> bool:
        """Fills fields in the Passport Details section."""

        logger.info("📝 Filling Passport Details...")
        success = True
        # Passport Type (Selection)
        pt_trigger_loc = self.get_locator('main_form.passport_details.passport_type_select')
        pt_option_tmpl = self.get_locator('main_form.passport_details.passport_type_option_template') # Assume template is also a locator value string
        passport_type_value = applicant.passport_type

        if pt_trigger_loc and pt_option_tmpl and passport_type_value:
            logger.info(f"Attempting to select Passport Type: '{passport_type_value}'")
            if self._safe_action(lambda: page.locator(pt_trigger_loc).click(), "Click Passport Type trigger"):
                specific_option_loc_str = pt_option_tmpl.format(option_text=passport_type_value)
                option_locator = page.locator(specific_option_loc_str)
                if self._safe_action(lambda: option_locator.wait_for(state="visible", timeout=5000), f"Wait for option '{passport_type_value}' visible"):
                    if not self._safe_action(lambda: option_locator.click(), f"Click Passport Type option '{passport_type_value}'"):
                        success = False
                else: success = False
            else: success = False
        elif not passport_type_value: logger.warning("⚠️ Skipping Passport Type selection: Data missing.")
        else: logger.warning("⚠️ Skipping Passport Type selection: Trigger or option template locator missing."); success = False

        # Date of Issue (Click -> Fill, with Format Conversion)
        doi_loc_str = self.get_locator('main_form.passport_details.date_of_issue_input')
        doi_value_ocr = applicant.date_of_issue # 从 OCR 获取的是 YYYYMMDD

        if doi_loc_str and doi_value_ocr:
            # --- V V V 添加格式转换 V V V ---
            formatted_doi_value = None
            if isinstance(doi_value_ocr, str) and len(doi_value_ocr) == 8: # 确保是8位字符串
                try:
                    dt_obj = datetime.strptime(doi_value_ocr, "%Y%m%d")
                    formatted_doi_value = dt_obj.strftime("%d/%m/%Y") # 转换为 DD/MM/YYYY
                    logger.info(f"Formatted Date of Issue from '{doi_value_ocr}' to '{formatted_doi_value}'")
                except ValueError:
                    logger.warning(f"⚠️ Could not parse Date of Issue '{doi_value_ocr}' from YYYYMMDD format. Using original value.")
                    formatted_doi_value = doi_value_ocr # 解析失败则用回原值（可能仍会错）
            else:
                 logger.warning(f"⚠️ Date of Issue '{doi_value_ocr}' is not an 8-character string. Using as is.")
                 formatted_doi_value = doi_value_ocr # 不是期望格式，直接用

            if not formatted_doi_value:
                 logger.error("❌ Date of Issue value became None after processing. Skipping.")
                 success = False
            else:
                 # --- ^ ^ ^ 格式转换结束 ^ ^ ^ ---

                 logger.info(f"Attempting to fill Date of Issue: '{formatted_doi_value}' (using Click->Fill)")
                 if self._safe_action(lambda: page.locator(doi_loc_str).click(), "Click Date of Issue input"):
                    page.wait_for_timeout(200)
                    if self._safe_action(lambda: page.locator(doi_loc_str).type(formatted_doi_value, delay=10), f"Type Date of Issue '{formatted_doi_value}'"):
                        if not self._safe_action(lambda: page.locator(doi_loc_str).press("Enter"), "Press Enter after Date of Issue"):
                            success = False
                 else:
                     success = False

        elif not doi_value_ocr:
             logger.info("⚠️ Skipping Date of Issue: Data missing.")
        else:
             logger.warning("⚠️ Skipping Date of Issue: Locator missing.");
             success = False

        return success

    def _fill_contact_info(self, page: Page, applicant: Applicant) -> bool:
        """Fills contact info fields (Email, Phone, Addresses)."""
        logger.info("📝 Filling Contact Info...")
        success = True
        # Email
        email_loc = self.get_locator('main_form.contact_info.email_input')
        re_email_loc = self.get_locator('main_form.contact_info.re_enter_email_input')
        email_val = applicant.email
        if email_loc and re_email_loc and email_val:
             if self._safe_action(lambda: page.locator(email_loc).fill(email_val), "Fill Email"):
                  if not self._safe_action(lambda: page.locator(re_email_loc).fill(email_val), "Fill Re-enter Email"):
                       success = False # Still problematic if re-enter fails
             else: success = False
        elif not email_val: logger.info("ℹ️ Skipping Email fields: Data missing.")
        else: logger.warning("⚠️ Skipping Email fields: Locators missing."); success = False

        # Phone
        phone_loc = self.get_locator('main_form.contact_info.telephone_input')
        phone_val = applicant.telephone_number
        if phone_loc and phone_val:
            if not self._safe_action(lambda: page.locator(phone_loc).fill(phone_val), f"Fill Telephone Number"):
                success = False
        elif not phone_val: logger.info("ℹ️ Skipping Telephone Number: Data missing.")
        else: logger.warning("⚠️ Skipping Telephone Number: Locator missing."); success = False

        # Addresses (Permanent, Contact)
        # Get the address locators dictionary first
        # Need to handle potential missing 'address_fields' key or sub-keys
        contact_info_section = self.locators.get('main_form', {}).get('contact_info', {})
        addr_locs = contact_info_section.get('address_fields', {}) if isinstance(contact_info_section, dict) else {}

        perm_addr_loc = addr_locs.get('permanent_address')
        if perm_addr_loc and applicant.permanent_address:
            if not self._safe_action(lambda: page.locator(perm_addr_loc).fill(applicant.permanent_address), "Fill Permanent Address"):
                 success = False
        elif not applicant.permanent_address: logger.info("ℹ️ Skipping Permanent Address: Value is None/Empty (might be expected).")
        else: logger.warning("⚠️ Skipping Permanent Address: Locator missing."); success = False # Fail if locator missing but value present

        contact_addr_loc = addr_locs.get('contact_address')
        if contact_addr_loc and applicant.contact_address:
             if not self._safe_action(lambda: page.locator(contact_addr_loc).fill(applicant.contact_address), "Fill Contact Address"):
                 success = False
        elif not applicant.contact_address: logger.info("ℹ️ Skipping Contact Address: Value is None/Empty.")
        else: logger.warning("⚠️ Skipping Contact Address: Locator missing."); success = False

        # 添加越南地址填充
        if hasattr(applicant, 'intended_entry_gate') and applicant.intended_entry_gate:
            logger.info(f"开始填充越南地址，使用入境口岸: {applicant.intended_entry_gate}")
            if not self._fill_vietnam_address(page, applicant):
                logger.warning("越南地址填充失败，但继续其他部分")

        return success

    def _fill_travel_info(self, page: Page, applicant: Applicant) -> bool:
        """Fills travel information fields."""
        logger.info(f"⬅️ Filler received: applicant.intended_entry_gate = '{applicant.intended_entry_gate}'") # <--- 看这里
        logger.info("📝 Filling Travel Info...")
        success = True

        # Purpose of Entry (Selection)
        poe_trigger = self.get_locator('main_form.travel_info.purpose_of_entry_select')
        poe_template = self.get_locator('main_form.travel_info.purpose_of_entry_option_template')
        poe_value = self.DEFAULT_PURPOSE_OF_ENTRY # Using constant
        if poe_trigger and poe_template and poe_value:
             # Refactored Select Option Logic (similar to Passport Type)
            logger.info(f"Attempting to select Purpose of Entry: '{poe_value}'")
            if self._safe_action(lambda: page.locator(poe_trigger).click(), "Click Purpose of Entry trigger"):
                specific_option_loc_str = poe_template.format(option_text=poe_value)
                option_locator = page.locator(specific_option_loc_str)
                if self._safe_action(lambda: option_locator.wait_for(state="visible", timeout=5000), f"Wait for option '{poe_value}' visible"):
                    if not self._safe_action(lambda: option_locator.click(), f"Click Purpose of Entry option '{poe_value}'"):
                        success = False
                else: success = False
            else: success = False
        else: logger.warning("⚠️ Skipping Purpose of Entry: Locators missing."); success = False

        # Visa Validity Dates (Click -> Fill)
        vfrom_loc = self.get_locator('main_form.travel_info.visa_valid_from_input')
        vto_loc = self.get_locator('main_form.travel_info.visa_valid_to_input')
        vfrom_val = applicant.visa_start_date
        duration_val = applicant.visa_validity_duration

        if vfrom_loc and vto_loc and vfrom_val and duration_val:
             vto_val = self._calculate_end_date(vfrom_val, duration_val)
             logger.info(f"Attempting to fill Visa Dates: From '{vfrom_val}', To '{vto_val}'")
             # From Date
             if self._safe_action(lambda: page.locator(vfrom_loc).click(), "Click Visa From Date input"):
                 page.wait_for_timeout(200)
                 if not self._safe_action(lambda: page.locator(vfrom_loc).fill(vfrom_val), f"Fill Visa From Date value '{vfrom_val}'"): success = False
             else: success = False
             # To Date (only if From was ok and To calculated)
             if success and vto_val:
                 if self._safe_action(lambda: page.locator(vto_loc).click(), "Click Visa To Date input"):
                     page.wait_for_timeout(200)
                     if not self._safe_action(lambda: page.locator(vto_loc).fill(vto_val), f"Fill Visa To Date value '{vto_val}'"): success = False
                 else: success = False
             elif not vto_val: logger.error("❌ Could not calculate Visa To Date."); success = False # Mark as failure
        elif not vfrom_val or not duration_val: logger.info("ℹ️ Skipping Visa Validity Dates: Missing data.") # Not critical? Maybe.
        else: logger.warning("⚠️ Skipping Visa Validity Dates: Locators missing."); success = False

        # Visa Type (Radio)
        visa_type = applicant.visa_entry_type
        radio_key = None
        if visa_type == "Single-entry": radio_key = 'main_form.travel_info.visa_type_single_entry_radio'
        elif visa_type == "Multiple-entry": radio_key = 'main_form.travel_info.visa_type_multiple_entry_radio'
        else: logger.warning(f"❓ Invalid Visa Entry Type in data: '{visa_type}'. Skipping selection.")

        if radio_key:
            radio_loc = self.get_locator(radio_key)
            if radio_loc:
                if not self._safe_action(lambda: page.locator(radio_loc).check(), f"Check Visa Type '{visa_type}'"): success = False
            else: success = False # Locator missing
        elif visa_type: success = False # Data present but invalid type

        # Entry/Exit Gates (Keyboard Input + Enter)
        entry_gate_val = applicant.intended_entry_gate
        entry_input_loc = self.get_locator('main_form.travel_info.intended_entry_gate_input')
        entry_trigger_loc = self.get_locator('main_form.travel_info.intended_entry_gate_select')
        exit_input_loc = self.get_locator('main_form.travel_info.intended_exit_gate_input')
        exit_trigger_loc = self.get_locator('main_form.travel_info.intended_exit_gate_select')

        if entry_input_loc and exit_input_loc and entry_gate_val:
             logger.info(f"Attempting to set Entry/Exit Gates via keyboard: '{entry_gate_val}'")
             # --- Entry Gate ---
             if entry_trigger_loc: # Optional click trigger
                 try: page.locator(entry_trigger_loc).click(timeout=3000)
                 except Exception: logger.info("Optional click on entry trigger failed.")
             # Fill + Enter
             if self._safe_action(lambda: page.locator(entry_input_loc).fill(entry_gate_val), "Fill Entry Gate"): # Try fill first
                 page.wait_for_timeout(200)
                 if not self._safe_action(lambda: page.locator(entry_input_loc).press('Enter'), "Press Enter for Entry Gate"): success = False
             else: success = False # Fill failed
             page.wait_for_timeout(300) # Pause

             # --- Exit Gate (if entry seemed ok) ---
             if success:
                 if exit_trigger_loc: # Optional click trigger
                     try: page.locator(exit_trigger_loc).click(timeout=3000)
                     except Exception: logger.info("Optional click on exit trigger failed.")
                 # Fill + Enter
                 if self._safe_action(lambda: page.locator(exit_input_loc).fill(entry_gate_val), "Fill Exit Gate"): # Use same value
                     page.wait_for_timeout(200)
                     if not self._safe_action(lambda: page.locator(exit_input_loc).press('Enter'), "Press Enter for Exit Gate"): success = False
                 else: success = False # Fill failed

        elif not entry_gate_val: logger.info("ℹ️ Skipping Entry/Exit Gates: Missing data.")
        else: logger.warning("⚠️ Skipping Entry/Exit Gates: Input locators missing."); success = False

        return success


    def _fill_emergency_contact(self, page: Page, applicant: Applicant) -> bool:
        """Fills the Emergency Contact section."""
        logger.info("📝 Filling Emergency Contact...")
        success = True
        # Name
        emerg_name_val = f"{self.DEFAULT_EMERGENCY_NAME_PREFIX}{applicant.surname}" if applicant.surname else None
        if not self._safe_action(
             lambda: page.locator(self.get_locator('main_form.emergency_contact.fullname_input')).fill(emerg_name_val or ""),
             "Fill Emergency Contact Name"
        ): success = False
        # Address (using previously set applicant emergency address)
        emerg_addr_loc = self.get_locator('main_form.emergency_contact.address_input') # Assuming this locator key exists
        if emerg_addr_loc and applicant.emergency_address:
            if not self._safe_action(lambda: page.locator(emerg_addr_loc).fill(applicant.emergency_address), "Fill Emergency Address"): success = False
        elif not applicant.emergency_address: logger.info("ℹ️ Skipping Emergency Address: Value not set.")
        else: logger.warning("⚠️ Skipping Emergency Address: Locator missing."); success = False
        # Phone (using applicant phone)
        if not self._safe_action(
             lambda: page.locator(self.get_locator('main_form.emergency_contact.phone_input')).fill(applicant.telephone_number or ""),
             "Fill Emergency Phone"
        ): success = False
        # Relationship (fixed)
        if not self._safe_action(
             lambda: page.locator(self.get_locator('main_form.emergency_contact.relationship_input')).fill(self.DEFAULT_EMERGENCY_RELATIONSHIP),
             "Fill Emergency Relationship"
        ): success = False
        return success

    # 在 VietnamFiller 类内部 (vietnam_filler.py)

    def _handle_previous_visits(self, page: Page, applicant: Applicant) -> bool:
        """
        根据申请人数据处理 '曾访问越南' 部分。
        只有当 applicant.visited_vietnam_last_year 为 True 时，才与网页交互。
        """
        logger.info("📝 处理 '曾访问越南' 部分...")
        # visited 变量现在直接从 applicant 对象获取，类型应该是 bool 或 None
        visited = applicant.visited_vietnam_last_year is True
        # 初始化最终成功状态，假设默认是成功的（因为'No'分支直接视为成功）
        final_success = True

        # --- 只有当用户通过UI表示 'Yes' (即 visited is True)，才与网页交互 ---
        if visited:
            logger.info("检测到用户选择 'Yes'，开始处理网页交互...")
            # 既然进入了Yes分支，先假设会失败，除非所有步骤都明确成功
            final_success = False

            # --- 步骤 1: 定位包含问题的父级容器 ---
            question_section_loc_str = self.get_locator('main_form.previous_visits.question_section')
            if not question_section_loc_str:
                logger.error("❌ 无法处理曾访问越南：缺少问题区域(question_section)的定位器。")
                return False # 缺少关键定位器，硬错误

            question_section = page.locator(question_section_loc_str)

            # 检查问题区域是否存在且可见
            try:
                question_section.wait_for(state='visible', timeout=5000)
                logger.info("✅ '曾访问越南' 问题区域可见。")
            except Exception:
                logger.error(f"❌ '曾访问越南' 问题区域定位器 ('{question_section_loc_str}') 没找到或不可见。")
                return False # 找不到问题区域，无法继续

            # --- 步骤 2: 在父容器内部查找并点击 "Yes" 单选按钮 ---
            yes_radio_relative_loc = self.get_locator('main_form.previous_visits.visited_last_year_yes_radio')
            if not yes_radio_relative_loc:
                logger.error("❌ 无法处理曾访问越南：缺少 Yes 按钮的相对定位器配置。")
                return False

            logger.info("尝试在问题区域内选择 'Yes'...")
            yes_radio_within_section = question_section.locator(yes_radio_relative_loc)
            if not self._safe_action(lambda: yes_radio_within_section.check(), "Check 'Yes' (visited Vietnam)"):
                logger.error("❌ 点击 'Yes' 单选按钮失败。")
                return False # 点击 Yes 失败，无法填充后续内容

            # --- 步骤 3: 填充访问细节 (只有点击 Yes 成功后才执行) ---
            page.wait_for_timeout(300) # 等待可能的动画或字段加载

            logger.info("开始填充上次访问细节...")
            success_details = True # 用于跟踪细节填充本身的成功状态

            # --- 步骤 3a: 获取所有细节的定位器 ---
            from_date_loc_str = self.get_locator('main_form.previous_visits.visit_details.from_date_input')
            to_date_loc_str = self.get_locator('main_form.previous_visits.visit_details.to_date_input')
            purpose_loc_str = self.get_locator('main_form.previous_visits.visit_details.purpose_input')

            if not all([from_date_loc_str, to_date_loc_str, purpose_loc_str]):
                 logger.error("❌ 缺少上次访问细节的定位器 (From/To/Purpose)。")
                 # 返回 False，因为无法填充必要信息
                 # 注意：final_success 在这里仍然是 False (因为我们进入了 if visited 分支)
                 return False

            # --- 步骤 3b: 填充上次入境日期 ---
            if applicant.previous_entry_date:
                entry_date_str = applicant.previous_entry_date
                logger.info(f"尝试填充上次入境日期: '{entry_date_str}' (Click->Type)")
                if self._safe_action(lambda: page.locator(from_date_loc_str).click(), "点击上次入境日期"):
                    page.wait_for_timeout(100)
                    if not self._safe_action(lambda: page.locator(from_date_loc_str).type(entry_date_str, delay=10), f"输入上次入境日期 '{entry_date_str}'"):
                        success_details = False
                else:
                    success_details = False
            else:
                logger.warning("⚠️ 跳过上次入境日期: 数据缺失。")
                # 缺失数据是否算失败？根据业务决定，这里暂时不算硬失败，允许继续

            # --- 步骤 3c: 填充上次离境日期 ---
            if applicant.previous_exit_date:
                exit_date_val = applicant.previous_exit_date
                logger.info(f"尝试填充上次离境日期: '{exit_date_val}' (Click->Type->Enter)")
                if self._safe_action(lambda: page.locator(to_date_loc_str).click(), "点击上次离境日期"):
                    page.wait_for_timeout(100)
                    if self._safe_action(lambda: page.locator(to_date_loc_str).type(exit_date_val, delay=10), f"输入上次离境日期 '{exit_date_val}'"):
                        page.wait_for_timeout(100)
                        # 按回车失败只记录警告，可能不影响提交？
                        if not self._safe_action(lambda: page.locator(to_date_loc_str).press('Enter'), "按 Enter 确认上次离开日期"):
                             logger.warning("⚠️ 按 Enter 确认上次离开日期失败。")
                    else:
                        success_details = False
                else:
                    success_details = False
            else:
                logger.warning("⚠️ 跳过上次离境日期: 数据缺失。")

            # --- 步骤 3d: 填充上次目的 ---
            if applicant.previous_purpose:
                purpose_val = applicant.previous_purpose
                logger.info(f"尝试填充上次目的: '{purpose_val}'")
                if not self._safe_action(lambda: page.locator(purpose_loc_str).fill(purpose_val), "填充上次目的"):
                    success_details = False
            else:
                logger.warning("⚠️ 跳过上次目的: 数据缺失.")

            # --- 步骤 3 结束 ---
            # 如果所有填充细节的操作都成功（或者跳过了但没报错），则最终状态为成功
            final_success = success_details

        # --- 与 if visited: 同一级别的 else ---
        else:
            # 如果 applicant.visited_vietnam_last_year is not True (是 False 或 None)
            logger.info("用户未选择 'Yes' (保持默认 'No')，跳过 '曾访问越南' 部分的网页交互。")
            # 在这种情况下，我们认为这部分操作是"成功"的，因为不需要做任何事
            final_success = True

        # --- 在方法的最后，返回最终的执行结果 ---
        logger.info(f"'曾访问越南' 部分处理完成，最终状态: {'成功' if final_success else '失败'}")
        return final_success

    def _handle_final_declarations(self, page: Page) -> bool:
        """Checks the final declaration checkboxes."""
        logger.info("📝 Handling Final Declarations...")
        success = True

        # Uncheck Email account agreement (using hierarchical key)
        if not self._safe_action(
            lambda: page.locator(self.get_locator('main_form.declarations.agree_email_account_checkbox')).uncheck(),
             "Uncheck 'Agree to create account by email'"
         ): success = False # Or maybe just warning? Depends if it needs unchecking

        # Check Commit Residence (using ID based hierarchical key)
        if not self._safe_action(
            lambda: page.locator(self.get_locator('main_form.declarations.commit_temp_residence_checkbox')).check(),
             "Check 'Commit temporary residence'"
        ): success = False # This is likely mandatory

        # Check Final Declaration (using text based hierarchical key)
        if not self._safe_action(
             lambda: page.locator(self.get_locator('main_form.declarations.final_declaration_checkbox')).check(),
             "Check 'Final declaration (I hereby declare...)'"
        ): success = False # This is likely mandatory

        return success

    def _input_and_enter(self, page, input_locator, trigger_locator, value, description):
        """输入内容并按回车选择下拉选项"""
        try:
            if trigger_locator:
                page.locator(trigger_locator).click(timeout=5000)
            page.locator(input_locator).click()
            page.wait_for_timeout(100)
            page.locator(input_locator).type(value, delay=10)
            page.locator(input_locator).press('Enter')
            logger.info(f"✅ Success: {description}")
            return True
        except Exception as e:
            logger.error(f"❌ Failure: {description} - {e}")
            return False

    def handle_captcha_page(self, page: Page) -> bool:
        """
        处理验证码页面：截图保存、识别验证码、填写和提交验证码。
        如果验证码验证失败，会自动重试。

        Args:
            page: Playwright页面对象

        Returns:
            bool: 验证码处理成功返回True，否则返回False
        """
        logger.info("🔐 开始处理验证码页面...")

        # 获取验证码输入框和下一步按钮的定位器
        captcha_input_loc = self.get_locator('captcha_page.captcha_input')
        next_button_loc = self.get_locator('captcha_page.next_button')
        captcha_image_loc = self.get_locator('captcha_page.captcha_image')
        refresh_button_loc = self.get_locator('captcha_page.refresh_button')  # 添加刷新按钮定位器

        if not all([captcha_input_loc, next_button_loc, captcha_image_loc]):
            logger.error("❌ 缺少验证码页面的必要定位器")
            return False

        # 等待验证码页面加载 - 主要是等待输入框可见
        try:
            page.wait_for_selector(captcha_input_loc, state='visible', timeout=5000)
            logger.info("✅ 验证码页面已加载，验证码输入框可见")
        except Exception as e:
            logger.error(f"❌ 验证码页面加载失败: {e}")
            return False

        # 创建截图目录
        screenshots_dir = Path("screenshots")
        screenshots_dir.mkdir(exist_ok=True)

        max_attempts = 15  # 最大重试次数
        consecutive_failures = 0  # 连续失败计数
        
        for attempt in range(1, max_attempts + 1):
            logger.info(f"🔄 验证码处理尝试 {attempt}/{max_attempts}")

            # 查找并截取验证码图片
            try:
                # 使用alt属性定位验证码图片 - 这是最准确的方式
                captcha_element = page.locator('img[alt="captcha img"]').first

                # 截取验证码图片
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                captcha_screenshot = screenshots_dir / f"captcha_{timestamp}.png"

                captcha_element.screenshot(path=str(captcha_screenshot))
                logger.info(f"📸 验证码截图已保存: {captcha_screenshot}")

                # 使用Anti-Captcha API识别验证码
                # captcha_solver.py 负责从环境变量获取API密钥
                captcha_text = solve_captcha(str(captcha_screenshot), page)

                if not captcha_text:
                    logger.warning(f"⚠️ 第{attempt}次尝试: 验证码识别失败，将自动重试")
                    # 如果连续3次识别失败，尝试刷新验证码
                    if consecutive_failures >= 3 and refresh_button_loc:
                        logger.info("🔄 连续多次识别失败，尝试刷新验证码...")
                        try:
                            page.click(refresh_button_loc)
                            page.wait_for_timeout(300)  # 等待新验证码加载
                            logger.info("✅ 验证码已刷新")
                            consecutive_failures = 0  # 重置连续失败计数
                        except Exception as e:
                            logger.warning(f"⚠️ 刷新验证码失败: {e}")

                    continue

                logger.info(f"✅ 第{attempt}次尝试: 验证码识别结果: {captcha_text}")

                # 填写验证码
                page.fill(captcha_input_loc, captcha_text)
                logger.info(f"✅ 验证码已填写: {captcha_text}")

                # 点击下一步按钮
                page.click(next_button_loc)
                logger.info("✅ 已点击下一步按钮")

                # 检查是否已经跳转到下一页 - 判断以下任一条件满足即视为成功
                success_locators = [

                    "text=Notice",
                    "button:has-text('Confirm')"
                ]
                
                # 检查是否已成功进入任一后续页面
                for locator in success_locators:
                    try:
                        page.wait_for_selector(locator, timeout=5000)
                        logger.info(f"✅ 页面跳转成功: 检测到 {locator}")
                        return True
                    except Exception:
                        continue  # 继续检测下一个


                # 如果没有跳转，检查是否有错误信息
                error_locator = self.get_locator('captcha_page.error_message')
                if error_locator and page.is_visible(error_locator):
                    error_text = page.locator(error_locator).text_content() or "未知错误"
                    logger.warning(f"⚠️ 第{attempt}次尝试: 验证码验证失败: {error_text}")
                else:
                    logger.warning(f"⚠️ 第{attempt}次尝试: 验证码验证失败，未检测到具体错误信息")

                # 验证码会自动刷新，无需手动刷新
                logger.info("🔄 等待下一次尝试...")
                page.wait_for_timeout(200)  # 给页面一点时间刷新验证码

            except Exception as e:
                logger.error(f"❌ 第{attempt}次尝试: 处理验证码过程中出错: {e}")
                # 截图以便调试
                screenshot_dir = Path("screenshots")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                screenshot_path = screenshot_dir / f"step1_before_next_{timestamp}.png"
                page.screenshot(path=str(screenshot_path), full_page=True)
                logger.info(f"已保存主填充页面截图: {screenshot_path}")
                # 继续下一次尝试

        logger.error(f"❌ 验证码处理失败，已达到最大尝试次数: {max_attempts}")
        return False

    def _handle_confirmation_flow(self, page):
        """处理从Declaration页面到付款页面的完整流程"""
        try:
            logger.info("开始处理确认流程...")

            # 第一阶段：确认DECLARATION页面
            self._verify_declaration_page(page)

            # 第二阶段：点击Confirm按钮
            page.click("button:has-text('Confirm')")
            logger.info("已点击Confirm按钮，等待页面跳转...")

            #第三阶段 新增流程 提取并保存申请编号和付款金额
            payment_info = self.extract_payment_info(page)
            application_number = payment_info["application_number"]
            logger.debug(f"✅ 成功提取申请编号: {application_number}")
            payment_amount = payment_info["payment_amount"]
            logger.debug(f"✅ 成功提取付款金额: {payment_amount}")
            
            if application_number:
                self.application_number = application_number
                logger.info(f"✅申请编号已保存: {self.application_number}, 会话ID: {self.session_id}")
                
            else:
                logger.error(f"❌ 申请编号提取失败, 会话ID: {self.session_id}")
                logger.debug("❌ 申请编号提取失败", exc_info=True)
            
            # 记录付款金额
            if payment_amount:
                logger.info(f"✅ 成功记录付款金额: {payment_amount}, 会话ID: {self.session_id}")
                self.payment_amount = payment_amount
            
            # ✅ 第四阶段：确认进入付款确认页面
            keywords = [
                "text=I agree to pay",
                "text=PAYMENT'S INFORMATION",
                "button:has-text('Payment')"
            ]

            payment_page_loaded = False
            for selector in keywords:
                try:
                    page.wait_for_selector(selector, timeout=5000)
                    logger.info(f"✅ 成功进入付款确认页面: 匹配到元素 {selector}")
                    payment_page_loaded = True
                    break
                except:
                    continue

            if not payment_page_loaded:
                raise RuntimeError("❌ 未检测到付款确认页面关键元素，页面可能未跳转成功")

            # ✅ 第五阶段：勾选支付并点击 Payment
            page.check('input.ant-checkbox-input')  # 勾选 'I agree to pay'
            page.click("button:has-text('Payment')")
            logger.info("✅ 已勾选支付并点击 Payment 按钮")

            # ✅ 第六阶段：智能等待并验证支付页面加载
            # 定义支付页面的特征关键词和选择器
            payment_selectors = [
                "text=PAY BY ATM CARD",
                "text=PAY BY VISA / MASTERCARD / JCB / AMEX",
                "text=VISA", "text=MASTERCARD", "text=JCB", "text=AMEX",
                "a:has(img[alt*='VISA'])",
                "div:has-text('Payment Method')"
            ]

            # 记录当前URL用于诊断
            previous_url = page.url
            logger.debug(f"当前页面 URL: {previous_url}")

            # 智能等待任一支付页面元素出现
            logger.info("等待支付页面加载...")
            payment_page_loaded = False
            timeout_ms = 30000  # 30秒超时

            for selector in payment_selectors:
                try:
                    # 使用wait_for_selector替代硬等待
                    page.wait_for_selector(selector, state="visible", timeout=timeout_ms)
                    logger.info(f"✅ 成功进入支付页面: 检测到元素 '{selector}'")
                    payment_page_loaded = True
                    break
                except Exception:
                    continue

            # 如果所有选择器都未找到，记录错误
            if not payment_page_loaded:
                # 记录URL变化情况作为额外信息
                current_url = page.url
                
                if current_url != previous_url:
                    logger.warning(f"⚠️ URL已变化 ({previous_url} → {current_url})，但未检测到支付页面元素")
                else:
                    logger.warning("⚠️ URL未变化，且未检测到支付页面元素")
                
                # 截图记录错误状态
                screenshots_dir = Path(self.settings.get('screenshots_dir', 'screenshots'))
                screenshots_dir.mkdir(exist_ok=True)
                screenshot_path = str(screenshots_dir / f"payment_page_error_{int(time.time())}.png")
                page.screenshot(path=screenshot_path)
                logger.info(f"已保存错误状态截图: {screenshot_path}")
                
                raise RuntimeError("未能成功进入支付页面，可能跳转失败")

            # ✅ 第七阶段：准备进入付款流程
            logger.info(f"✅ 表单填充流程成功执行，会话ID: {self.session_id}，准备进入付款流程")
            return True
        except Exception as e:
            logger.error(f"❌ 确认流程异常: {e}")
            raise
    def _verify_declaration_page(self, page):
        """验证是否成功进入Declaration页面"""
        success_locators = [
            "text=Notice",
            "button:has-text('Confirm')"
        ]

        for selector in success_locators:
            try:
                if page.locator(selector).is_visible(timeout=5000):
                    logger.info(f"✅ 成功进入DECLARATION页面: 匹配到元素 '{selector}'")
                    return True
            except Exception:
                continue  # 忽略单个失败，继续尝试下一个 selector

        error_msg = "❌ 未能确认DECLARATION页面"
        logger.error(error_msg)
        raise RuntimeError(error_msg)
    

    def extract_application_number(self, page):
        """
        提取 e-Visa 申请编号和付款金额，并做校验。必须以 E 开头，长度为 20 或 21。
        """
        try:
            # 等待申请编号元素出现
            page.wait_for_selector("span.text-evisa-main4.font-medium", timeout=8000)
            logger.debug("✅ 申请编号元素已出现")

            # 获取所有可能的元素
            elements = page.locator("span.text-evisa-main4.font-medium").all()
            logger.debug(f"找到 {len(elements)} 个可能的申请编号元素")

            # 打印页面上所有可能的申请编号元素
            logger.debug(f"页面上所有可能的申请编号元素:")
            for i, el in enumerate(elements):
                logger.debug(f"元素 {i}: {el.text_content().strip()}")
            
            # 遍历所有元素文本
            for i, el in enumerate(elements):
                text = el.text_content().strip()
                logger.debug(f"元素 {i}: {text}")
                
                # 检查是否符合申请编号格式：以E开头，长度20-21
                if text.startswith("E") and 20 <= len(text) <= 21:
                    logger.debug(f"✅ 成功提取申请编号: {text}")
                    return text

            # 如果没找到符合条件的元素，记录错误
            logger.error("❌ 未找到符合格式的申请编号")
            
            return None

        except Exception as e:
            logger.error(f"❌ 提取申请编号异常: {e}")
            return None
    
    def extract_payment_info(self, page):
        """
        提取付款页面的付款金额和申请编号，并返回一个包含这两个信息的字典。如果提取失败，返回None。
        """
        try:
            # 等待页面加载
            page.wait_for_timeout(2000)
            
            # 提取申请编号
            application_number = self.extract_application_number(page)

            # 明确设置application_number属性，确保能被simple_engine.py访问
            self.application_number = application_number
            
            # 提取付款金额 - 简化版本，直接搜索"25 USD"或"50 USD"
            payment_amount = None
            try:
                # 获取页面文本内容
                page_content = page.content()
                
                # 直接搜索特定金额模式
                if "25 USD" in page_content:
                    payment_amount = "25 USD"
                    logger.info(f"✅ 成功提取付款金额: {payment_amount} (单次入境)")
                elif "50 USD" in page_content:
                    payment_amount = "50 USD"
                    logger.info(f"✅ 成功提取付款金额: {payment_amount} (多次入境)")
                else:
                    # 备用方案：使用正则表达式搜索数字+USD模式
                    import re
                    amount_match = re.search(r'(\d+)\s*USD', page_content)
                    if amount_match:
                        payment_amount = amount_match.group(0)
                        logger.info(f"✅ 成功提取付款金额: {payment_amount}")
                    else:
                        logger.warning("⚠️ 未找到付款金额")
                        
            except Exception as e:
                logger.warning(f"⚠️ 提取付款金额失败: {e}")
            
            return {
                "application_number": application_number,
                "payment_amount": payment_amount
            }
                
        except Exception as e:
            logger.error(f"❌ 提取付款信息异常: {e}")
            return {
                "application_number": None,
                "payment_amount": None
            }

# --- End of VietnamFiller class ---
