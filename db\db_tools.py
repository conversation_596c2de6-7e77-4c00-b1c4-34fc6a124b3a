"""数据库工具模块，提供命令行工具和辅助函数"""
from app.utils.logger_config import get_logger
from db.db import count_records, verify_recent_records, DB_PATH

logger = get_logger()

def show_db_stats():
    """显示数据库统计信息"""
    try:
        stats = count_records()
        logger.info(f"📊 数据库统计: {stats['applicants']} 位申请人, {stats['tasks']} 条签证任务")
        logger.info(f"📊 任务状态: {stats['success']} 条成功提交, {stats['downloaded']} 条已下载签证")
        return stats
    except Exception as e:
        logger.warning(f"⚠️ 无法获取数据库统计: {e}")
        return None

def print_recent_records(limit=5):
    """打印最近的数据库记录"""
    try:
        records = verify_recent_records(limit)
        
        if records:
            print(f"\n✅ 最近 {len(records)} 条签证任务记录:")
            for i, record in enumerate(records, 1):
                print(f"\n--- 记录 {i} ---")
                for key, value in record.items():
                    print(f"{key}: {value}")
        else:
            print("\n❌ 未找到任何签证任务记录")
            
        return records
    except Exception as e:
        print(f"\n❌ 验证数据库记录失败: {e}")
        return None

if __name__ == "__main__":
        
    # 显示数据库信息
    print(f"\n数据库路径: {DB_PATH}")
    
    # 显示统计信息
    show_db_stats()
    
    # 显示最近记录
    print_recent_records(10)