<!DOCTYPE html>
<html>
<head>
    <title>Vietnam E-Visa Application Form</title>
    <link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --background-color: #f8fafc;
            --text-color: #1f2937;
            --border-radius: 12px;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-input: 0 4px 8px rgba(0, 0, 0, 0.1);
            --shadow-input-focus: 0 0 0 4px rgba(37, 99, 235, 0.15);
            --spacing-unit: 1rem;
            --section-padding: 1.5rem;
            --form-spacing: 1.25rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        h1 {
            text-align: center;
            color: var(--primary-color);
            margin-bottom: 3rem;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .form-container {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            max-width: 800px;
            margin: 0 auto;
        }

        .form-header {
            margin-bottom: 2.5rem;
            text-align: center;
        }

        .form-header h2 {
            color: var(--text-color);
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
        }

        .form-header p {
            color: #6b7280;
            font-size: 0.95rem;
        }

        .form-group {
            margin-bottom: 1.25rem;
        }

        .form-group.inline {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 1.25rem;
        }

        .form-group.inline .form-group {
            flex: 1;
        }

        .form-group.inline label {
            margin-bottom: 0.5rem;
        }

        .form-group.inline input,
        .form-group.inline select {
            width: 100%;
        }

        .file-upload {
            width: 100%;
        }

        .file-uploads {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.25rem;
        }

        .file-upload {
            background: white;
            padding: 1.25rem;
            border-radius: var(--border-radius);
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-input);
        }

        .file-upload:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-input-focus);
        }

        .file-upload label {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: var(--text-color);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .file-upload input[type="file"] {
            display: none;
        }

        .file-upload .upload-btn {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--primary-color);
            background: var(--primary-color);
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.95rem;
        }

        .file-upload .upload-btn:hover {
            background: var(--secondary-color);
            transform: translateY(-1px);
        }

        .file-upload .upload-btn::before {
            content: '';
            display: inline-block;
            width: 24px;
            height: 24px;
            background-image: url('/static/images/upload arrows.png');
            background-size: contain;
            background-repeat: no-repeat;
            margin-right: 0.5rem;
        }

        .file-upload .upload-btn + input[type="file"] {
            display: none;
        }

        .file-preview {
            width: 100%;
            height: 200px;
            border: 1px solid #e5e7eb;
            margin-top: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius);
            transition: all 0.2s ease;
            background: #f9fafb;
        }

        .file-preview:hover {
            border-color: var(--primary-color);
            background: white;
        }

        .file-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        input, select {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #e5e7eb;
            border-radius: var(--border-radius);
            background: white;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-input);
        }

        input:focus, select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-input-focus);
        }

        .radio-group {
            display: flex;
            gap: 1.5rem;
            margin-top: 0.5rem;
            flex-wrap: wrap;
        }

        .radio-option {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            background: white;
            cursor: pointer;
            transition: all 0.2s ease;
            flex: 1 1 auto;
            min-width: 120px;
            border: 1px solid #e5e7eb;
        }

        .radio-option:hover {
            background: #f3f4f6;
            border-color: var(--primary-color);
        }

        .radio-option input[type="radio"] {
            appearance: none;
            width: 20px;
            height: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
        }

        .radio-option input[type="radio"]:hover {
            border-color: var(--primary-color);
        }

        .radio-option input[type="radio"]:checked {
            border-color: var(--primary-color);
            background: var(--primary-color);
        }

        .radio-option input[type="radio"]:checked::after {
            content: '\2714'; /* 更优雅的勾号符号 */
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(15deg);
            font-size: 16px;
            color: white;
            transition: all 0.2s ease;
            font-weight: 700;
            line-height: 1;
        }

        .radio-option label {
            font-weight: 500;
            color: var(--text-color);
            font-size: 1rem;
        }

        .form-actions {
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e5e7eb;
            text-align: right;
        }

        .submit-btn {
            padding: 1rem 2rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-md);
        }

        .submit-btn:hover {
            background-color: var(--secondary-color);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        button {
            padding: 1rem 2rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 1rem;
        }

        button:hover {
            background-color: var(--secondary-color);
            transform: translateY(-1px);
        }

        button:disabled {
            background-color: #e5e7eb;
            cursor: not-allowed;
        }

        .form-section h3 {
            color: var(--text-color);
            margin-bottom: 1.5rem;
            font-size: 1.25rem;
        }

        .form-section p {
            color: #6b7280;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
        }

        .form-section .optional {
            color: #6b7280;
            font-size: 0.85rem;
        }

        .form-section .required {
            color: #ef4444;
            font-size: 0.85rem;
        }

        .date-input {
            display: flex;
            gap: 0.5rem;
        }

        .flatpickr-calendar {
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
        }

        .flatpickr-day.selected, .flatpickr-day:hover {
            background-color: var(--primary-color);
        }

        .flatpickr-day.selected:hover {
            background-color: var(--secondary-color);
        }

        .flatpickr-day.today {
            border-color: var(--primary-color);
        }

        .flatpickr-monthSelect-months {
            background: var(--background-color);
            border-color: #e5e7eb;
        }

        .flatpickr-month {
            color: var(--text-color);
        }

        .flatpickr-current-month {
            color: var(--text-color);
        }

        .flatpickr-day {
            color: var(--text-color);
        }

        .flatpickr-day:hover {
            background: var(--primary-color);
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .flatpickr-day.today:hover {
            border-color: var(--primary-color);
            background: var(--primary-color);
        }

        .flatpickr-day.selected:hover {
            background-color: var(--secondary-color);
            color: white;
        }

        .flatpickr-day.nextMonthDay:hover {
            background: var(--primary-color);
            color: #6b7280;
        }

        .flatpickr-day.prevMonthDay:hover {
            background: var(--primary-color);
            color: #6b7280;
        }

        .flatpickr-day.disabled:hover {
            background: none;
            cursor: not-allowed;
            color: #d1d5db;
        }

        .flatpickr-day.inRange:hover {
            background: var(--primary-color);
        }
    </style>
</head>
<body>
    <h1>Vietnam E-Visa Application Form</h1>
    <div class="form-container">
        <div class="form-header">
            <h2>Apply for Vietnam E-Visa</h2>
            <p>Please fill out this form to apply for your Vietnam electronic visa.</p>
        </div>

        <form action="http://localhost:8000/apply-visa-form/" method="post" enctype="multipart/form-data">
            <!-- 文件上传区域 -->
            <div class="form-section">
                <h3>Document Uploads</h3>
                <div class="file-uploads">
                    <div class="file-upload">
                        <label for="portrait_photo">Portrait Photo 上传照片</label>
                        <button class="upload-btn" type="button" onclick="document.getElementById('portrait_photo').click()">Upload Portrait Photo</button>
                        <input type="file" id="portrait_photo" name="portrait_photo" accept="image/jpeg,image/png" onchange="previewFile(this, 'portrait-preview')" required>
                        <div id="portrait-preview" class="file-preview">Drag and drop or click to upload</div>
                    </div>
                    <div class="file-upload">
                        <label for="passport_scan">Passport Scan 上传护照</label>
                        <button class="upload-btn" type="button" onclick="document.getElementById('passport_scan').click()">Upload Passport Scan</button>
                        <input type="file" id="passport_scan" name="passport_scan" accept="image/jpeg,image/png" onchange="previewFile(this, 'passport-preview')" required>
                        <div id="passport-preview" class="file-preview">Drag and drop or click to upload</div>
                    </div>
                </div>
            </div>

            <!-- 个人基本信息 -->
            <div class="form-section">
                <h3>Personal Information 个人基本信息</h3>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="surname">Surname 姓 <span class="required">*</span></label>
                        <input type="text" id="surname" name="surname" required>
                    </div>
                    <div class="form-group">
                        <label for="given_name">Given Name 名 <span class="required">*</span></label>
                        <input type="text" id="given_name" name="given_name" required>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="chinese_name">Chinese Name 中文名 <span class="optional">(Optional)</span></label>
                        <input type="text" id="chinese_name" name="chinese_name">
                    </div>
                    <div class="form-group">
                        <label for="sex">Sex 性别 <span class="required">*</span></label>
                        <div class="radio-group">
                            <div class="radio-option">
                                <input type="radio" id="sex-male" name="sex" value="M" required>
                                <label for="sex-male">男</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="sex-female" name="sex" value="F" required>
                                <label for="sex-female">女</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="dob">Date of Birth 出生日期 <span class="required">*</span></label>
                        <input type="text" id="dob" name="dob" class="date-input" placeholder="DD/MM/YYYY" required>
                        <p class="note">Format: DD/MM/YYYY</p>
                    </div>
                    <div class="form-group">
                        <label for="place_of_birth">Place of Birth 出生地 <span class="required">*</span></label>
                        <input type="text" id="place_of_birth" name="place_of_birth" required>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="nationality">Nationality 国籍 <span class="required">*</span></label>
                        <input type="text" id="nationality" name="nationality" value="CHINA" required>
                    </div>
                    <div class="form-group">
                        <label for="religion">Religion 宗教 <span class="required">*</span></label>
                        <input type="text" id="religion" name="religion" value="NO" required>
                    </div>
                </div>
            </div>

            <!-- 护照信息 -->
            <div class="form-section">
                <h3>Passport Information 护照信息</h3>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="passport_number">Passport Number 护照号码 <span class="required">*</span></label>
                        <input type="text" id="passport_number" name="passport_number" required>
                    </div>
                    <div class="form-group">
                        <label for="passport_type">Passport Type 护照类型 <span class="required">*</span></label>
                        <input type="text" id="passport_type" name="passport_type" value="Ordinary passport" required>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="place_of_issue">Place of Issue 签发地 <span class="required">*</span></label>
                        <input type="text" id="place_of_issue" name="place_of_issue" required>
                    </div>
                    <div class="form-group">
                        <label for="date_of_issue">Date of Issue 签发日期 <span class="required">*</span></label>
                        <input type="text" id="date_of_issue" name="date_of_issue" class="date-input" placeholder="DD/MM/YYYY" required>
                        <p class="note">Format: DD/MM/YYYY</p>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="passport_expiry">Passport Expiry 护照有效期 <span class="required">*</span></label>
                        <input type="text" id="passport_expiry" name="passport_expiry" class="date-input" placeholder="DD/MM/YYYY" required>
                        <p class="note">Format: DD/MM/YYYY</p>
                    </div>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-section">
                <h3>Contact Information 联系信息</h3>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="email">Email 邮箱 <span class="required">*</span></label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="telephone_number">Telephone Number 电话号码 <span class="required">*</span></label>
                        <input type="text" id="telephone_number" name="telephone_number" required>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="permanent_address">Permanent Address 永久地址 <span class="optional">(Optional)</span></label>
                        <input type="text" id="permanent_address" name="permanent_address">
                    </div>
                    <div class="form-group">
                        <label for="contact_address">Contact Address 联系地址 <span class="optional">(Optional)</span></label>
                        <input type="text" id="contact_address" name="contact_address">
                    </div>
                </div>
            </div>

            <!-- 紧急联系人 -->
            <div class="form-section">
                <h3>Emergency Contact 紧急联系人</h3>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="emergency_contact_name">Emergency Contact Name 紧急联系人 <span class="optional">(Optional)</span></label>
                        <input type="text" id="emergency_contact_name" name="emergency_contact_name">
                    </div>
                    <div class="form-group">
                        <label for="emergency_address">Emergency Address 紧急地址 <span class="optional">(Optional)</span></label>
                        <input type="text" id="emergency_address" name="emergency_address">
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="emergency_contact_phone">Emergency Contact Phone 紧急联系人电话 <span class="optional">(Optional)</span></label>
                        <input type="text" id="emergency_contact_phone" name="emergency_contact_phone">
                    </div>
                </div>
            </div>

            <!-- 签证申请详情 -->
            <div class="form-section">
                <h3>Visa Request Details 签证申请详情</h3>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="visa_entry_type">Visa Entry Type 签证类型 <span class="required">*</span></label>
                        <div class="radio-group">
                            <div class="radio-option">
                                <input type="radio" id="visa-entry-single" name="visa_entry_type" value="Single-entry" required>
                                <label for="visa-entry-single">单次</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="visa-entry-multiple" name="visa_entry_type" value="Multiple-entry" required>
                                <label for="visa-entry-multiple">多次</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="visa_validity_duration">Visa Validity Duration 签证有效期 <span class="required">*</span></label>
                        <div class="radio-group" style="flex-direction: column;">
                            <div class="radio-option">
                                <input type="radio" id="visa-duration-30" name="visa_validity_duration" value="30天" required>
                                <label for="visa-duration-30">30天</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="visa-duration-90" name="visa_validity_duration" value="90天" required>
                                <label for="visa-duration-90">90天</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="visa_start_date">Visa Start Date 签证生效日期 <span class="required">*</span></label>
                        <input type="text" id="visa_start_date" name="visa_start_date" class="date-input" placeholder="DD/MM/YYYY" required>
                        <p class="note">Format: DD/MM/YYYY</p>
                    </div>
                    <div class="form-group">
                        <label for="intended_entry_gate">Intended Entry Gate 入境口岸 <span class="required">*</span></label>
                        <select id="intended_entry_gate" name="intended_entry_gate" required>
                            <option value="">Select Entry Gate</option>
                            <option value="Tan Son Nhat Int Airport (Ho Chi Minh City)" selected>胡志明</option>
                            <option value="Noi Bai Int Airport">河内</option>
                            <option value="Da Nang International Airport">岘港</option>
                            <option value="Cam Ranh Int Airport (Khanh Hoa)">芽庄</option>
                            <option value="Mong Cai Landport">芒街</option>
                            <option value="Huu Nghi Landport">友谊</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 之前访问信息 -->
            <div class="form-section">
                <h3>Previous Visit Information 访问越南信息</h3>
                <div class="form-group">
                    <label>Have you visited Vietnam in the last year? 您是否在一年内访问过越南？<span class="required">*</span></label>
                    <div class="radio-group">
                        <div class="radio-option">
                            <input type="radio" id="visited-no" name="visited_vietnam_last_year" value="false" checked>
                            <label for="visited-no">否</label>
                        </div>
                        <div class="radio-option">
                            <input type="radio" id="visited-yes" name="visited_vietnam_last_year" value="true">
                            <label for="visited-yes">是</label>
                        </div>
                    </div>
                </div>
                <div id="previous-visit-details" style="display: none;">
                    <div class="form-group inline">
                        <div class="form-group">
                            <label for="previous_entry_date">Previous Entry Date 上次入境日期 <span class="required">*</span></label>
                            <input type="text" id="previous_entry_date" name="previous_entry_date" class="date-input" placeholder="DD/MM/YYYY">
                            <p class="note">Format: DD/MM/YYYY</p>
                        </div>
                        <div class="form-group">
                            <label for="previous_exit_date">Previous Exit Date 上次出境日期 <span class="required">*</span></label>
                            <input type="text" id="previous_exit_date" name="previous_exit_date" class="date-input" placeholder="DD/MM/YYYY">
                            <p class="note">Format: DD/MM/YYYY</p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="previous_purpose">Previous Purpose 上次入境目的 <span class="required">*</span></label>
                        <input type="text" id="previous_purpose" name="previous_purpose" placeholder="Tourism">
                    </div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="form-actions">
                <button class="submit-btn" type="submit">提交申请</button>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        // 文件预览功能
        function previewFile(input, previewId) {
            const preview = document.getElementById(previewId);
            const file = input.files[0];

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `<img src="${e.target.result}" alt="Preview">`;
                }
                reader.readAsDataURL(file);
            } else {
                preview.innerHTML = 'No file selected';
            }
        }

        // 显示/隐藏上次访问越南的详细信息
        document.querySelectorAll('input[name="visited_vietnam_last_year"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const detailsDiv = document.getElementById('previous-visit-details');
                if (this.value === 'true') {
                    detailsDiv.style.display = 'block';
                } else {
                    detailsDiv.style.display = 'none';
                }
            });
        });

        // 自动OCR识别并填充表单
        document.getElementById('passport_scan').addEventListener('change', function() {
            const fileInput = this;
            const file = fileInput.files[0];
            if (!file) return;

            const formData = new FormData();
            formData.append('passport_scan', file);

            fetch('/ocr-passport/', {
                method: 'POST',
                body: formData
            })
            .then(res => res.json())
            .then(data => {
                if (data.surname) document.getElementById('surname').value = data.surname;
                if (data.given_name) document.getElementById('given_name').value = data.given_name;
                if (data.chinese_name) document.getElementById('chinese_name').value = data.chinese_name;
                if (data.sex) document.getElementById('sex-male').checked = data.sex === 'M';
                if (data.sex) document.getElementById('sex-female').checked = data.sex === 'F';
                if (data.dob) document.getElementById('dob').value = data.dob;
                if (data.place_of_birth) document.getElementById('place_of_birth').value = data.place_of_birth;
                if (data.nationality) document.getElementById('nationality').value = data.nationality;
                if (data.passport_number) document.getElementById('passport_number').value = data.passport_number;
                if (data.passport_type) document.getElementById('passport_type').value = data.passport_type;
                if (data.place_of_issue) document.getElementById('place_of_issue').value = data.place_of_issue;
                if (data.date_of_issue) document.getElementById('date_of_issue').value = data.date_of_issue;
                if (data.passport_expiry) document.getElementById('passport_expiry').value = data.passport_expiry;
                // 你可以继续补充其它字段
            })
            .catch(err => {
                alert('OCR识别失败，请手动填写信息');
                console.error(err);
            });
        });

        flatpickr('.date-input', {
            dateFormat: 'd/m/Y',
            allowInput: true,
            theme: 'light',
            locale: {
                firstDayOfWeek: 1, // Monday
                weekdays: {
                    shorthand: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
                    longhand: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
                },
                months: {
                    shorthand: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    longhand: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
                }
            }
        });
    </script>
</body>
</html>