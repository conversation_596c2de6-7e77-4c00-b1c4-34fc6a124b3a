<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Vietnam E-Visa Application Form - Apply for your electronic visa">
    <title>Vietnam E-Visa Application Form</title>
    <link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --background-color: #f8fafc;
            --text-color: #1f2937;
            --border-radius: 12px;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-input: 0 4px 8px rgba(0, 0, 0, 0.1);
            --shadow-input-focus: 0 0 0 4px rgba(37, 99, 235, 0.15);
            --spacing-unit: 1rem;
            --section-padding: 1.5rem;
            --form-spacing: 1.25rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        /* 顶部导航栏 */
        .top-navbar {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .navbar-user {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-color);
            font-size: 0.9rem;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.8rem;
        }

        .logout-btn {
            padding: 0.5rem 1rem;
            background: #f3f4f6;
            color: var(--text-color);
            border: 1px solid #e5e7eb;
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .logout-btn:hover {
            background: #e5e7eb;
            border-color: #d1d5db;
        }

        /* 主内容区域 */
        .main-content {
            padding: 0 2rem 2rem 2rem;
        }

        /* 进度指示器 */
        .progress-container {
            background: white;
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
        }

        .progress-header {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .progress-header h3 {
            color: var(--text-color);
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            margin-bottom: 1rem;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            flex: 1;
            z-index: 2;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            border: 2px solid #e5e7eb;
            background: white;
            color: #6b7280;
        }

        .step-circle.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .step-circle.completed {
            background: #10b981;
            border-color: #10b981;
            color: white;
        }

        .step-label {
            font-size: 0.8rem;
            color: #6b7280;
            text-align: center;
            max-width: 80px;
        }

        .step-label.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        .step-label.completed {
            color: #10b981;
            font-weight: 600;
        }

        .progress-line {
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e5e7eb;
            z-index: 1;
        }

        .progress-line-fill {
            height: 100%;
            background: var(--primary-color);
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-info {
            text-align: center;
            font-size: 0.9rem;
            color: #6b7280;
        }

        /* 实时验证样式 */
        .form-group {
            position: relative;
        }

        .validation-message {
            font-size: 0.8rem;
            margin-top: 0.25rem;
            padding: 0.25rem 0;
            transition: all 0.2s ease;
        }

        .validation-message.success {
            color: #10b981;
        }

        .validation-message.error {
            color: #ef4444;
        }

        .validation-message.warning {
            color: #f59e0b;
        }

        input.valid {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        input.invalid {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        select.valid {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        select.invalid {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .validation-icon {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1rem;
            pointer-events: none;
        }

        .validation-icon.success {
            color: #10b981;
        }

        .validation-icon.error {
            color: #ef4444;
        }

        h1 {
            text-align: center;
            color: var(--primary-color);
            margin-bottom: 3rem;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .form-container {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            max-width: 800px;
            margin: 0 auto;
        }

        .form-header {
            margin-bottom: 2.5rem;
            text-align: center;
        }

        .form-header h2 {
            color: var(--text-color);
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
        }

        .form-header p {
            color: #6b7280;
            font-size: 0.95rem;
        }

        .form-group {
            margin-bottom: 1.25rem;
        }

        .form-group.inline {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 1.25rem;
        }

        .form-group.inline .form-group {
            flex: 1;
        }

        .form-group.inline label {
            margin-bottom: 0.5rem;
        }

        .form-group.inline input,
        .form-group.inline select {
            width: 100%;
        }

        .file-upload {
            width: 100%;
        }

        .file-uploads {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.25rem;
        }

        .file-upload {
            background: white;
            padding: 1.25rem;
            border-radius: var(--border-radius);
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-input);
        }

        .file-upload:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-input-focus);
        }

        .file-upload label {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: var(--text-color);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .file-upload input[type="file"] {
            display: none;
        }

        .file-upload .upload-btn {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--primary-color);
            background: var(--primary-color);
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.95rem;
        }

        .file-upload .upload-btn:hover {
            background: var(--secondary-color);
            transform: translateY(-1px);
        }

        .file-upload .upload-btn::before {
            content: '';
            display: inline-block;
            width: 24px;
            height: 24px;
            background-image: url('/static/images/upload arrows.png');
            background-size: contain;
            background-repeat: no-repeat;
            margin-right: 0.5rem;
        }

        .file-upload .upload-btn + input[type="file"] {
            display: none;
        }

        .file-preview {
            width: 100%;
            height: 200px;
            border: 2px dashed #e5e7eb;
            margin-top: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius);
            transition: all 0.2s ease;
            background: #f9fafb;
            cursor: pointer;
            position: relative;
        }

        .file-preview:hover {
            border-color: var(--primary-color);
            background: white;
        }

        .file-preview.drag-over {
            border-color: var(--primary-color);
            background: rgba(37, 99, 235, 0.05);
            transform: scale(1.02);
        }

        .file-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .file-preview-text {
            text-align: center;
            color: #6b7280;
            font-size: 0.9rem;
        }

        .file-preview-text .drag-text {
            font-weight: 600;
            color: var(--primary-color);
        }

        .file-preview-text .file-types {
            font-size: 0.8rem;
            margin-top: 0.5rem;
            color: #9ca3af;
        }

        input, select {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #e5e7eb;
            border-radius: var(--border-radius);
            background: white;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-input);
        }

        input:focus, select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-input-focus);
        }

        .radio-group {
            display: flex;
            gap: 1.5rem;
            margin-top: 0.5rem;
            flex-wrap: wrap;
        }

        .radio-option {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            background: white;
            cursor: pointer;
            transition: all 0.2s ease;
            flex: 1 1 auto;
            min-width: 120px;
            border: 1px solid #e5e7eb;
        }

        .radio-option:hover {
            background: #f3f4f6;
            border-color: var(--primary-color);
        }

        .radio-option input[type="radio"] {
            appearance: none;
            width: 20px;
            height: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 50%;
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
        }

        .radio-option input[type="radio"]:hover {
            border-color: var(--primary-color);
        }

        .radio-option input[type="radio"]:checked {
            border-color: var(--primary-color);
            background: var(--primary-color);
        }

        .radio-option input[type="radio"]:checked::after {
            content: '\2714'; /* 更优雅的勾号符号 */
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(15deg);
            font-size: 16px;
            color: white;
            transition: all 0.2s ease;
            font-weight: 700;
            line-height: 1;
        }

        .radio-option label {
            font-weight: 500;
            color: var(--text-color);
            font-size: 1rem;
        }

        .form-actions {
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e5e7eb;
            text-align: right;
        }

        .submit-btn {
            padding: 1rem 2rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: var(--shadow-md);
        }

        .submit-btn:hover {
            background-color: var(--secondary-color);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        button {
            padding: 1rem 2rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 1rem;
        }

        button:hover {
            background-color: var(--secondary-color);
            transform: translateY(-1px);
        }

        button:disabled {
            background-color: #e5e7eb;
            cursor: not-allowed;
        }

        .form-section h3 {
            color: var(--text-color);
            margin-bottom: 1.5rem;
            font-size: 1.25rem;
        }

        .form-section p {
            color: #6b7280;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
        }

        .form-section .optional {
            color: #6b7280;
            font-size: 0.85rem;
        }

        .form-section .required {
            color: #ef4444;
            font-size: 0.85rem;
        }

        .date-input {
            display: flex;
            gap: 0.5rem;
        }

        .flatpickr-calendar {
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
        }

        .flatpickr-day.selected, .flatpickr-day:hover {
            background-color: var(--primary-color);
        }

        .flatpickr-day.selected:hover {
            background-color: var(--secondary-color);
        }

        .flatpickr-day.today {
            border-color: var(--primary-color);
        }

        .flatpickr-monthSelect-months {
            background: var(--background-color);
            border-color: #e5e7eb;
        }

        .flatpickr-month {
            color: var(--text-color);
        }

        .flatpickr-current-month {
            color: var(--text-color);
        }

        .flatpickr-day {
            color: var(--text-color);
        }

        .flatpickr-day:hover {
            background: var(--primary-color);
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .flatpickr-day.today:hover {
            border-color: var(--primary-color);
            background: var(--primary-color);
        }

        .flatpickr-day.selected:hover {
            background-color: var(--secondary-color);
            color: white;
        }

        .flatpickr-day.nextMonthDay:hover {
            background: var(--primary-color);
            color: #6b7280;
        }

        .flatpickr-day.prevMonthDay:hover {
            background: var(--primary-color);
            color: #6b7280;
        }

        .flatpickr-day.disabled:hover {
            background: none;
            cursor: not-allowed;
            color: #d1d5db;
        }

        .flatpickr-day.inRange:hover {
            background: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="top-navbar">
        <div class="navbar-brand">
            电子签证自动化系统
        </div>
        <div class="navbar-user">
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">U</div>
                <span id="username">用户</span>
            </div>
            <a href="/login" class="logout-btn" onclick="logout()">退出登录</a>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-content">
        <h1>Vietnam E-Visa Application Form</h1>

        <!-- 进度指示器 -->
        <div class="progress-container">
            <div class="progress-header">
                <h3>申请进度</h3>
            </div>
            <div class="progress-steps">
                <div class="progress-line">
                    <div class="progress-line-fill" id="progressLineFill"></div>
                </div>
                <div class="progress-step">
                    <div class="step-circle active" id="step1">1</div>
                    <div class="step-label active">文件上传</div>
                </div>
                <div class="progress-step">
                    <div class="step-circle" id="step2">2</div>
                    <div class="step-label">个人信息</div>
                </div>
                <div class="progress-step">
                    <div class="step-circle" id="step3">3</div>
                    <div class="step-label">护照信息</div>
                </div>
                <div class="progress-step">
                    <div class="step-circle" id="step4">4</div>
                    <div class="step-label">联系信息</div>
                </div>
                <div class="progress-step">
                    <div class="step-circle" id="step5">5</div>
                    <div class="step-label">签证详情</div>
                </div>
                <div class="progress-step">
                    <div class="step-circle" id="step6">✓</div>
                    <div class="step-label">提交完成</div>
                </div>
            </div>
            <div class="progress-info" id="progressInfo">
                请上传所需文件开始申请流程
            </div>
        </div>

        <div class="form-container">
        <div class="form-header">
            <h2>Apply for Vietnam E-Visa</h2>
            <p>Please fill out this form to apply for your Vietnam electronic visa.</p>
        </div>

        <form action="/apply-visa-form/" method="post" enctype="multipart/form-data">
            <!-- 文件上传区域 -->
            <div class="form-section">
                <h3>Document Uploads</h3>
                <div class="file-uploads">
                    <div class="file-upload">
                        <label for="portrait_photo">Portrait Photo 上传照片</label>
                        <button class="upload-btn" type="button" data-target="portrait_photo">Upload Portrait Photo</button>
                        <input type="file" id="portrait_photo" name="portrait_photo" accept="image/jpeg,image/png" data-preview="portrait-preview" required>
                        <div id="portrait-preview" class="file-preview" data-target="portrait_photo">
                            <div class="file-preview-text">
                                <div class="drag-text">拖拽文件到此处或点击上传</div>
                                <div class="file-types">支持 JPG, PNG 格式</div>
                            </div>
                        </div>
                    </div>
                    <div class="file-upload">
                        <label for="passport_scan">Passport Scan 上传护照</label>
                        <button class="upload-btn" type="button" data-target="passport_scan">Upload Passport Scan</button>
                        <input type="file" id="passport_scan" name="passport_scan" accept="image/jpeg,image/png" data-preview="passport-preview" required>
                        <div id="passport-preview" class="file-preview" data-target="passport_scan">
                            <div class="file-preview-text">
                                <div class="drag-text">拖拽文件到此处或点击上传</div>
                                <div class="file-types">支持 JPG, PNG 格式</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人基本信息 -->
            <div class="form-section">
                <h3>Personal Information 个人基本信息</h3>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="surname">Surname 姓 <span class="required">*</span></label>
                        <input type="text" id="surname" name="surname" required>
                    </div>
                    <div class="form-group">
                        <label for="given_name">Given Name 名 <span class="required">*</span></label>
                        <input type="text" id="given_name" name="given_name" required>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="chinese_name">Chinese Name 中文名 <span class="optional">(Optional)</span></label>
                        <input type="text" id="chinese_name" name="chinese_name">
                    </div>
                    <div class="form-group">
                        <label for="sex">Sex 性别 <span class="required">*</span></label>
                        <div class="radio-group">
                            <div class="radio-option">
                                <input type="radio" id="sex-male" name="sex" value="M" required>
                                <label for="sex-male">男</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="sex-female" name="sex" value="F" required>
                                <label for="sex-female">女</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="dob">Date of Birth 出生日期 <span class="required">*</span></label>
                        <input type="text" id="dob" name="dob" class="date-input" placeholder="DD/MM/YYYY" required>
                        <p class="note">Format: DD/MM/YYYY</p>
                    </div>
                    <div class="form-group">
                        <label for="place_of_birth">Place of Birth 出生地 <span class="required">*</span></label>
                        <input type="text" id="place_of_birth" name="place_of_birth" required>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="nationality">Nationality 国籍 <span class="required">*</span></label>
                        <input type="text" id="nationality" name="nationality" value="CHINA" required>
                    </div>
                    <div class="form-group">
                        <label for="religion">Religion 宗教 <span class="required">*</span></label>
                        <input type="text" id="religion" name="religion" value="NO" required>
                    </div>
                </div>
            </div>

            <!-- 护照信息 -->
            <div class="form-section">
                <h3>Passport Information 护照信息</h3>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="passport_number">Passport Number 护照号码 <span class="required">*</span></label>
                        <input type="text" id="passport_number" name="passport_number" required>
                    </div>
                    <div class="form-group">
                        <label for="passport_type">Passport Type 护照类型 <span class="required">*</span></label>
                        <input type="text" id="passport_type" name="passport_type" value="Ordinary passport" required>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="place_of_issue">Place of Issue 签发地 <span class="required">*</span></label>
                        <input type="text" id="place_of_issue" name="place_of_issue" required>
                    </div>
                    <div class="form-group">
                        <label for="date_of_issue">Date of Issue 签发日期 <span class="required">*</span></label>
                        <input type="text" id="date_of_issue" name="date_of_issue" class="date-input" placeholder="DD/MM/YYYY" required>
                        <p class="note">Format: DD/MM/YYYY</p>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="passport_expiry">Passport Expiry 护照有效期 <span class="required">*</span></label>
                        <input type="text" id="passport_expiry" name="passport_expiry" class="date-input" placeholder="DD/MM/YYYY" required>
                        <p class="note">Format: DD/MM/YYYY</p>
                    </div>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-section">
                <h3>Contact Information 联系信息</h3>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="email">Email 邮箱 <span class="required">*</span></label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="telephone_number">Telephone Number 电话号码 <span class="required">*</span></label>
                        <input type="text" id="telephone_number" name="telephone_number" required>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="permanent_address">Permanent Address 永久地址 <span class="optional">(Optional)</span></label>
                        <input type="text" id="permanent_address" name="permanent_address">
                    </div>
                    <div class="form-group">
                        <label for="contact_address">Contact Address 联系地址 <span class="optional">(Optional)</span></label>
                        <input type="text" id="contact_address" name="contact_address">
                    </div>
                </div>
            </div>

            <!-- 紧急联系人 -->
            <div class="form-section">
                <h3>Emergency Contact 紧急联系人</h3>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="emergency_contact_name">Emergency Contact Name 紧急联系人 <span class="optional">(Optional)</span></label>
                        <input type="text" id="emergency_contact_name" name="emergency_contact_name">
                    </div>
                    <div class="form-group">
                        <label for="emergency_address">Emergency Address 紧急地址 <span class="optional">(Optional)</span></label>
                        <input type="text" id="emergency_address" name="emergency_address">
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="emergency_contact_phone">Emergency Contact Phone 紧急联系人电话 <span class="optional">(Optional)</span></label>
                        <input type="text" id="emergency_contact_phone" name="emergency_contact_phone">
                    </div>
                </div>
            </div>

            <!-- 签证申请详情 -->
            <div class="form-section">
                <h3>Visa Request Details 签证申请详情</h3>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="visa_entry_type">Visa Entry Type 签证类型 <span class="required">*</span></label>
                        <div class="radio-group">
                            <div class="radio-option">
                                <input type="radio" id="visa-entry-single" name="visa_entry_type" value="Single-entry" required>
                                <label for="visa-entry-single">单次</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="visa-entry-multiple" name="visa_entry_type" value="Multiple-entry" required>
                                <label for="visa-entry-multiple">多次</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="visa_validity_duration">Visa Validity Duration 签证有效期 <span class="required">*</span></label>
                        <div class="radio-group" style="flex-direction: column;">
                            <div class="radio-option">
                                <input type="radio" id="visa-duration-30" name="visa_validity_duration" value="30天" required>
                                <label for="visa-duration-30">30天</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="visa-duration-90" name="visa_validity_duration" value="90天" required>
                                <label for="visa-duration-90">90天</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="visa_start_date">Visa Start Date 签证生效日期 <span class="required">*</span></label>
                        <input type="text" id="visa_start_date" name="visa_start_date" class="date-input" placeholder="DD/MM/YYYY" required>
                        <p class="note">Format: DD/MM/YYYY</p>
                    </div>
                    <div class="form-group">
                        <label for="intended_entry_gate">Intended Entry Gate 入境口岸 <span class="required">*</span></label>
                        <select id="intended_entry_gate" name="intended_entry_gate" required>
                            <option value="">Select Entry Gate</option>
                            <option value="Tan Son Nhat Int Airport (Ho Chi Minh City)" selected>胡志明</option>
                            <option value="Noi Bai Int Airport">河内</option>
                            <option value="Da Nang International Airport">岘港</option>
                            <option value="Cam Ranh Int Airport (Khanh Hoa)">芽庄</option>
                            <option value="Mong Cai Landport">芒街</option>
                            <option value="Huu Nghi Landport">友谊</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 之前访问信息 -->
            <div class="form-section">
                <h3>Previous Visit Information 访问越南信息</h3>
                <div class="form-group">
                    <label>Have you visited Vietnam in the last year? 您是否在一年内访问过越南？<span class="required">*</span></label>
                    <div class="radio-group">
                        <div class="radio-option">
                            <input type="radio" id="visited-no" name="visited_vietnam_last_year" value="false" checked>
                            <label for="visited-no">否</label>
                        </div>
                        <div class="radio-option">
                            <input type="radio" id="visited-yes" name="visited_vietnam_last_year" value="true">
                            <label for="visited-yes">是</label>
                        </div>
                    </div>
                </div>
                <div id="previous-visit-details" style="display: none;">
                    <div class="form-group inline">
                        <div class="form-group">
                            <label for="previous_entry_date">Previous Entry Date 上次入境日期 <span class="required">*</span></label>
                            <input type="text" id="previous_entry_date" name="previous_entry_date" class="date-input" placeholder="DD/MM/YYYY">
                            <p class="note">Format: DD/MM/YYYY</p>
                        </div>
                        <div class="form-group">
                            <label for="previous_exit_date">Previous Exit Date 上次出境日期 <span class="required">*</span></label>
                            <input type="text" id="previous_exit_date" name="previous_exit_date" class="date-input" placeholder="DD/MM/YYYY">
                            <p class="note">Format: DD/MM/YYYY</p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="previous_purpose">Previous Purpose 上次入境目的 <span class="required">*</span></label>
                        <input type="text" id="previous_purpose" name="previous_purpose" placeholder="Tourism">
                    </div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="form-actions">
                <button class="submit-btn" type="submit">提交申请</button>
            </div>
        </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        // 退出登录功能
        function logout() {
            // 清除认证cookie
            document.cookie = "auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            // 重定向到登录页面
            window.location.href = "/login";
        }

        // 获取用户信息并显示
        function initializeUserInfo() {
            // 从cookie中获取用户名
            const cookies = document.cookie.split(';');
            let username = '用户';

            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'auth_token' && value) {
                    username = value;
                    break;
                }
            }

            // 更新用户显示
            document.getElementById('username').textContent = username;
            document.getElementById('userAvatar').textContent = username.charAt(0).toUpperCase();
        }

        // 进度跟踪功能
        let currentStep = 1;
        const totalSteps = 6;
        const stepMessages = {
            1: '请上传所需文件开始申请流程',
            2: '请填写个人基本信息',
            3: '请填写护照相关信息',
            4: '请填写联系方式信息',
            5: '请填写签证申请详情',
            6: '申请表单已完成，可以提交'
        };

        function updateProgress(step) {
            currentStep = step;

            // 更新进度条
            const progressPercentage = ((step - 1) / (totalSteps - 1)) * 100;
            document.getElementById('progressLineFill').style.width = progressPercentage + '%';

            // 更新步骤状态
            for (let i = 1; i <= totalSteps; i++) {
                const stepCircle = document.getElementById(`step${i}`);
                const stepLabel = stepCircle.nextElementSibling;

                if (i < step) {
                    stepCircle.className = 'step-circle completed';
                    stepLabel.className = 'step-label completed';
                    if (i < totalSteps) stepCircle.textContent = '✓';
                } else if (i === step) {
                    stepCircle.className = 'step-circle active';
                    stepLabel.className = 'step-label active';
                    if (i < totalSteps) stepCircle.textContent = i;
                } else {
                    stepCircle.className = 'step-circle';
                    stepLabel.className = 'step-label';
                    if (i < totalSteps) stepCircle.textContent = i;
                }
            }

            // 更新进度信息
            document.getElementById('progressInfo').textContent = stepMessages[step] || '';
        }

        // 检查表单完成度并自动更新进度
        function checkFormProgress() {
            const portraitUploaded = document.getElementById('portrait_photo').files.length > 0;
            const passportUploaded = document.getElementById('passport_scan').files.length > 0;

            if (portraitUploaded && passportUploaded && currentStep === 1) {
                updateProgress(2);
            }

            // 检查个人信息
            const surname = document.getElementById('surname').value.trim();
            const givenName = document.getElementById('given_name').value.trim();
            const sex = document.querySelector('input[name="sex"]:checked');
            const dob = document.getElementById('dob').value.trim();

            if (surname && givenName && sex && dob && currentStep === 2) {
                updateProgress(3);
            }

            // 检查护照信息
            const passportNumber = document.getElementById('passport_number').value.trim();
            const dateOfIssue = document.getElementById('date_of_issue').value.trim();
            const passportExpiry = document.getElementById('passport_expiry').value.trim();

            if (passportNumber && dateOfIssue && passportExpiry && currentStep === 3) {
                updateProgress(4);
            }

            // 检查联系信息
            const email = document.getElementById('email').value.trim();
            const telephone = document.getElementById('telephone_number').value.trim();

            if (email && telephone && currentStep === 4) {
                updateProgress(5);
            }

            // 检查签证详情
            const visaEntryType = document.querySelector('input[name="visa_entry_type"]:checked');
            const visaValidityDuration = document.querySelector('input[name="visa_validity_duration"]:checked');
            const visaStartDate = document.getElementById('visa_start_date').value.trim();
            const intendedEntryGate = document.getElementById('intended_entry_gate').value;

            if (visaEntryType && visaValidityDuration && visaStartDate && intendedEntryGate && currentStep === 5) {
                updateProgress(6);
            }
        }

        // 文件预览功能
        function previewFile(input, previewId) {
            const preview = document.getElementById(previewId);
            const file = input.files[0];

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `<img src="${e.target.result}" alt="Preview">`;
                }
                reader.readAsDataURL(file);
            } else {
                preview.innerHTML = `
                    <div class="file-preview-text">
                        <div class="drag-text">拖拽文件到此处或点击上传</div>
                        <div class="file-types">支持 JPG, PNG 格式</div>
                    </div>
                `;
            }
        }

        // 实时表单验证功能
        const validationRules = {
            surname: {
                required: true,
                minLength: 1,
                pattern: /^[A-Za-z\s]+$/,
                message: '姓氏只能包含英文字母和空格'
            },
            given_name: {
                required: true,
                minLength: 1,
                pattern: /^[A-Za-z\s]+$/,
                message: '名字只能包含英文字母和空格'
            },
            chinese_name: {
                required: false,
                pattern: /^[\u4e00-\u9fa5]+$/,
                message: '中文名只能包含中文字符'
            },
            dob: {
                required: true,
                pattern: /^\d{2}\/\d{2}\/\d{4}$/,
                message: '请输入正确的日期格式 DD/MM/YYYY'
            },
            place_of_birth: {
                required: true,
                minLength: 2,
                message: '出生地至少需要2个字符'
            },
            passport_number: {
                required: true,
                pattern: /^[A-Z0-9]{6,12}$/,
                message: '护照号码应为6-12位字母和数字组合'
            },
            date_of_issue: {
                required: true,
                pattern: /^\d{2}\/\d{2}\/\d{4}$/,
                message: '请输入正确的日期格式 DD/MM/YYYY'
            },
            passport_expiry: {
                required: true,
                pattern: /^\d{2}\/\d{2}\/\d{4}$/,
                message: '请输入正确的日期格式 DD/MM/YYYY'
            },
            email: {
                required: true,
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: '请输入有效的邮箱地址'
            },
            telephone_number: {
                required: true,
                pattern: /^[\d\+\-\s\(\)]{8,20}$/,
                message: '请输入有效的电话号码'
            },
            visa_start_date: {
                required: true,
                pattern: /^\d{2}\/\d{2}\/\d{4}$/,
                message: '请输入正确的日期格式 DD/MM/YYYY'
            }
        };

        function validateField(fieldName, value) {
            const rules = validationRules[fieldName];
            if (!rules) return { isValid: true };

            // 检查必填字段
            if (rules.required && (!value || value.trim() === '')) {
                return { isValid: false, message: '此字段为必填项' };
            }

            // 如果字段为空且非必填，则跳过其他验证
            if (!value || value.trim() === '') {
                return { isValid: true };
            }

            // 检查最小长度
            if (rules.minLength && value.length < rules.minLength) {
                return { isValid: false, message: `至少需要${rules.minLength}个字符` };
            }

            // 检查正则表达式
            if (rules.pattern && !rules.pattern.test(value)) {
                return { isValid: false, message: rules.message };
            }

            // 特殊验证：护照有效期检查
            if (fieldName === 'passport_expiry' && rules.pattern.test(value)) {
                const expiryDate = parseDate(value);
                const today = new Date();
                const sixMonthsFromNow = new Date();
                sixMonthsFromNow.setMonth(today.getMonth() + 6);

                if (expiryDate <= sixMonthsFromNow) {
                    return { isValid: false, message: '护照有效期应至少还有6个月' };
                }
            }

            return { isValid: true, message: '验证通过' };
        }

        function parseDate(dateStr) {
            const parts = dateStr.split('/');
            return new Date(parts[2], parts[1] - 1, parts[0]);
        }

        function showValidation(input, validation) {
            const formGroup = input.closest('.form-group');
            let validationMsg = formGroup.querySelector('.validation-message');
            let validationIcon = formGroup.querySelector('.validation-icon');

            // 移除现有的验证消息和图标
            if (validationMsg) validationMsg.remove();
            if (validationIcon) validationIcon.remove();

            // 重置输入框样式
            input.classList.remove('valid', 'invalid');

            if (validation.isValid) {
                input.classList.add('valid');

                // 添加成功图标
                validationIcon = document.createElement('span');
                validationIcon.className = 'validation-icon success';
                validationIcon.textContent = '✓';
                input.parentNode.appendChild(validationIcon);

                if (validation.message && validation.message !== '验证通过') {
                    validationMsg = document.createElement('div');
                    validationMsg.className = 'validation-message success';
                    validationMsg.textContent = validation.message;
                    input.parentNode.appendChild(validationMsg);
                }
            } else {
                input.classList.add('invalid');

                // 添加错误图标
                validationIcon = document.createElement('span');
                validationIcon.className = 'validation-icon error';
                validationIcon.textContent = '✗';
                input.parentNode.appendChild(validationIcon);

                // 添加错误消息
                validationMsg = document.createElement('div');
                validationMsg.className = 'validation-message error';
                validationMsg.textContent = validation.message;
                input.parentNode.appendChild(validationMsg);
            }
        }

        function setupRealTimeValidation() {
            Object.keys(validationRules).forEach(fieldName => {
                const input = document.getElementById(fieldName);
                if (input) {
                    input.addEventListener('blur', function() {
                        const validation = validateField(fieldName, this.value);
                        showValidation(this, validation);
                        checkFormProgress();
                    });

                    input.addEventListener('input', function() {
                        // 延迟验证，避免用户输入时频繁提示
                        clearTimeout(this.validationTimeout);
                        this.validationTimeout = setTimeout(() => {
                            const validation = validateField(fieldName, this.value);
                            showValidation(this, validation);
                            checkFormProgress();
                        }, 500);
                    });
                }
            });
        }

        // 拖拽上传功能
        function setupDragAndDrop() {
            document.querySelectorAll('.file-preview').forEach(preview => {
                const targetId = preview.getAttribute('data-target');
                const fileInput = document.getElementById(targetId);

                if (!fileInput) return;

                // 点击预览区域触发文件选择
                preview.addEventListener('click', () => {
                    fileInput.click();
                });

                // 拖拽事件
                preview.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    preview.classList.add('drag-over');
                });

                preview.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    preview.classList.remove('drag-over');
                });

                preview.addEventListener('drop', (e) => {
                    e.preventDefault();
                    preview.classList.remove('drag-over');

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        const file = files[0];

                        // 检查文件类型
                        if (file.type.startsWith('image/')) {
                            // 创建新的FileList对象
                            const dt = new DataTransfer();
                            dt.items.add(file);
                            fileInput.files = dt.files;

                            // 触发change事件
                            fileInput.dispatchEvent(new Event('change', { bubbles: true }));
                        } else {
                            alert('请上传图片文件 (JPG, PNG)');
                        }
                    }
                });
            });
        }

        // 初始化文件上传按钮事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化用户信息
            initializeUserInfo();

            // 初始化拖拽上传功能
            setupDragAndDrop();

            // 初始化实时表单验证
            setupRealTimeValidation();

            // 添加表单监听器来自动检查进度
            const form = document.querySelector('form');
            form.addEventListener('input', checkFormProgress);
            form.addEventListener('change', checkFormProgress);

            // 处理上传按钮点击事件
            document.querySelectorAll('.upload-btn[data-target]').forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-target');
                    const fileInput = document.getElementById(targetId);
                    if (fileInput) {
                        fileInput.click();
                    }
                });
            });

            // 处理文件选择事件
            document.querySelectorAll('input[type="file"][data-preview]').forEach(input => {
                input.addEventListener('change', function() {
                    const previewId = this.getAttribute('data-preview');
                    previewFile(this, previewId);
                    // 检查进度
                    setTimeout(checkFormProgress, 100);
                });
            });
        });

        // 显示/隐藏上次访问越南的详细信息
        document.querySelectorAll('input[name="visited_vietnam_last_year"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const detailsDiv = document.getElementById('previous-visit-details');
                if (this.value === 'true') {
                    detailsDiv.style.display = 'block';
                } else {
                    detailsDiv.style.display = 'none';
                }
            });
        });

        // 自动OCR识别并填充表单
        document.getElementById('passport_scan').addEventListener('change', function() {
            const fileInput = this;
            const file = fileInput.files[0];
            if (!file) return;

            const formData = new FormData();
            formData.append('passport_scan', file);

            fetch('/ocr-passport/', {
                method: 'POST',
                body: formData
            })
            .then(res => res.json())
            .then(data => {
                if (data.surname) document.getElementById('surname').value = data.surname;
                if (data.given_name) document.getElementById('given_name').value = data.given_name;
                if (data.chinese_name) document.getElementById('chinese_name').value = data.chinese_name;
                if (data.sex) document.getElementById('sex-male').checked = data.sex === 'M';
                if (data.sex) document.getElementById('sex-female').checked = data.sex === 'F';
                if (data.dob) document.getElementById('dob').value = data.dob;
                if (data.place_of_birth) document.getElementById('place_of_birth').value = data.place_of_birth;
                if (data.nationality) document.getElementById('nationality').value = data.nationality;
                if (data.passport_number) document.getElementById('passport_number').value = data.passport_number;
                if (data.passport_type) document.getElementById('passport_type').value = data.passport_type;
                if (data.place_of_issue) document.getElementById('place_of_issue').value = data.place_of_issue;
                if (data.date_of_issue) document.getElementById('date_of_issue').value = data.date_of_issue;
                if (data.passport_expiry) document.getElementById('passport_expiry').value = data.passport_expiry;
                // 你可以继续补充其它字段
            })
            .catch(err => {
                alert('OCR识别失败，请手动填写信息');
                console.error(err);
            });
        });

        flatpickr('.date-input', {
            dateFormat: 'd/m/Y',
            allowInput: true,
            theme: 'light',
            locale: {
                firstDayOfWeek: 1, // Monday
                weekdays: {
                    shorthand: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
                    longhand: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
                },
                months: {
                    shorthand: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    longhand: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
                }
            }
        });
    </script>
</body>
</html>