<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Vietnam E-Visa Application Form - Apply for your electronic visa">
    <title>Vietnam E-Visa Application Form</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/common.css">
    <link rel="stylesheet" href="/static/css/form.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="top-navbar">
        <div class="navbar-brand">
            电子签证自动化系统
        </div>
        <div class="navbar-user">
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">U</div>
                <span id="username">用户</span>
            </div>
            <a href="/login" class="logout-btn" onclick="logout()">退出登录</a>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-content">
        <h1>Vietnam E-Visa Application Form</h1>
        
        <!-- 进度指示器 -->
        <div class="progress-container">
            <div class="progress-header">
                <h3>申请进度</h3>
            </div>
            <div class="progress-steps">
                <div class="progress-line">
                    <div class="progress-line-fill" id="progressLineFill"></div>
                </div>
                <div class="progress-step">
                    <div class="step-circle active" id="step1">1</div>
                    <div class="step-label active">文件上传</div>
                </div>
                <div class="progress-step">
                    <div class="step-circle" id="step2">2</div>
                    <div class="step-label">个人信息</div>
                </div>
                <div class="progress-step">
                    <div class="step-circle" id="step3">3</div>
                    <div class="step-label">护照信息</div>
                </div>
                <div class="progress-step">
                    <div class="step-circle" id="step4">4</div>
                    <div class="step-label">联系信息</div>
                </div>
                <div class="progress-step">
                    <div class="step-circle" id="step5">5</div>
                    <div class="step-label">签证详情</div>
                </div>
                <div class="progress-step">
                    <div class="step-circle" id="step6">✓</div>
                    <div class="step-label">提交完成</div>
                </div>
            </div>
            <div class="progress-info" id="progressInfo">
                请上传所需文件开始申请流程
            </div>
        </div>

        <div class="form-container">
        <div class="form-header">
            <h2>Apply for Vietnam E-Visa</h2>
            <p>Please fill out this form to apply for your Vietnam electronic visa.</p>
        </div>

        <form action="/apply-visa-form/" method="post" enctype="multipart/form-data">
            <!-- 文件上传区域 -->
            <div class="form-section">
                <h3>Document Uploads</h3>
                <div class="file-uploads">
                    <div class="file-upload">
                        <label for="portrait_photo">Portrait Photo 上传照片</label>
                        <button class="upload-btn" type="button" data-target="portrait_photo">Upload Portrait Photo</button>
                        <input type="file" id="portrait_photo" name="portrait_photo" accept="image/jpeg,image/png" data-preview="portrait-preview" required>
                        <div id="portrait-preview" class="file-preview" data-target="portrait_photo">
                            <div class="file-preview-text">
                                <div class="drag-text">拖拽文件到此处或点击上传</div>
                                <div class="file-types">支持 JPG, PNG 格式</div>
                            </div>
                        </div>
                    </div>
                    <div class="file-upload">
                        <label for="passport_scan">Passport Scan 上传护照</label>
                        <button class="upload-btn" type="button" data-target="passport_scan">Upload Passport Scan</button>
                        <input type="file" id="passport_scan" name="passport_scan" accept="image/jpeg,image/png" data-preview="passport-preview" required>
                        <div id="passport-preview" class="file-preview" data-target="passport_scan">
                            <div class="file-preview-text">
                                <div class="drag-text">拖拽文件到此处或点击上传</div>
                                <div class="file-types">支持 JPG, PNG 格式</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人基本信息 -->
            <div class="form-section">
                <h3>Personal Information 个人基本信息</h3>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="surname">Surname 姓 <span class="required">*</span></label>
                        <input type="text" id="surname" name="surname" required>
                    </div>
                    <div class="form-group">
                        <label for="given_name">Given Name 名 <span class="required">*</span></label>
                        <input type="text" id="given_name" name="given_name" required>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="chinese_name">Chinese Name 中文名 <span class="optional">(Optional)</span></label>
                        <input type="text" id="chinese_name" name="chinese_name">
                    </div>
                    <div class="form-group">
                        <label for="sex">Sex 性别 <span class="required">*</span></label>
                        <div class="radio-group">
                            <div class="radio-option">
                                <input type="radio" id="sex-male" name="sex" value="M" required>
                                <label for="sex-male">男</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="sex-female" name="sex" value="F" required>
                                <label for="sex-female">女</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="dob">Date of Birth 出生日期 <span class="required">*</span></label>
                        <input type="text" id="dob" name="dob" class="date-input" placeholder="DD/MM/YYYY" required>
                        <p class="note">Format: DD/MM/YYYY</p>
                    </div>
                    <div class="form-group">
                        <label for="place_of_birth">Place of Birth 出生地 <span class="required">*</span></label>
                        <input type="text" id="place_of_birth" name="place_of_birth" required>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="nationality">Nationality 国籍 <span class="required">*</span></label>
                        <input type="text" id="nationality" name="nationality" value="CHINA" required>
                    </div>
                    <div class="form-group">
                        <label for="religion">Religion 宗教 <span class="required">*</span></label>
                        <input type="text" id="religion" name="religion" value="NO" required>
                    </div>
                </div>
            </div>

            <!-- 护照信息 -->
            <div class="form-section">
                <h3>Passport Information 护照信息</h3>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="passport_number">Passport Number 护照号码 <span class="required">*</span></label>
                        <input type="text" id="passport_number" name="passport_number" required>
                    </div>
                    <div class="form-group">
                        <label for="passport_type">Passport Type 护照类型 <span class="required">*</span></label>
                        <input type="text" id="passport_type" name="passport_type" value="Ordinary passport" required>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="place_of_issue">Place of Issue 签发地 <span class="required">*</span></label>
                        <input type="text" id="place_of_issue" name="place_of_issue" required>
                    </div>
                    <div class="form-group">
                        <label for="date_of_issue">Date of Issue 签发日期 <span class="required">*</span></label>
                        <input type="text" id="date_of_issue" name="date_of_issue" class="date-input" placeholder="DD/MM/YYYY" required>
                        <p class="note">Format: DD/MM/YYYY</p>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="passport_expiry">Passport Expiry 护照有效期 <span class="required">*</span></label>
                        <input type="text" id="passport_expiry" name="passport_expiry" class="date-input" placeholder="DD/MM/YYYY" required>
                        <p class="note">Format: DD/MM/YYYY</p>
                    </div>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-section">
                <h3>Contact Information 联系信息</h3>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="email">Email 邮箱 <span class="required">*</span></label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="telephone_number">Telephone Number 电话号码 <span class="required">*</span></label>
                        <input type="text" id="telephone_number" name="telephone_number" required>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="permanent_address">Permanent Address 永久地址 <span class="optional">(Optional)</span></label>
                        <input type="text" id="permanent_address" name="permanent_address">
                    </div>
                    <div class="form-group">
                        <label for="contact_address">Contact Address 联系地址 <span class="optional">(Optional)</span></label>
                        <input type="text" id="contact_address" name="contact_address">
                    </div>
                </div>
            </div>

            <!-- 紧急联系人 -->
            <div class="form-section">
                <h3>Emergency Contact 紧急联系人</h3>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="emergency_contact_name">Emergency Contact Name 紧急联系人 <span class="optional">(Optional)</span></label>
                        <input type="text" id="emergency_contact_name" name="emergency_contact_name">
                    </div>
                    <div class="form-group">
                        <label for="emergency_address">Emergency Address 紧急地址 <span class="optional">(Optional)</span></label>
                        <input type="text" id="emergency_address" name="emergency_address">
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="emergency_contact_phone">Emergency Contact Phone 紧急联系人电话 <span class="optional">(Optional)</span></label>
                        <input type="text" id="emergency_contact_phone" name="emergency_contact_phone">
                    </div>
                </div>
            </div>

            <!-- 签证申请详情 -->
            <div class="form-section">
                <h3>Visa Request Details 签证申请详情</h3>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="visa_entry_type">Visa Entry Type 签证类型 <span class="required">*</span></label>
                        <div class="radio-group">
                            <div class="radio-option">
                                <input type="radio" id="visa-entry-single" name="visa_entry_type" value="Single-entry" required>
                                <label for="visa-entry-single">单次</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="visa-entry-multiple" name="visa_entry_type" value="Multiple-entry" required>
                                <label for="visa-entry-multiple">多次</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="visa_validity_duration">Visa Validity Duration 签证有效期 <span class="required">*</span></label>
                        <div class="radio-group" style="flex-direction: column;">
                            <div class="radio-option">
                                <input type="radio" id="visa-duration-30" name="visa_validity_duration" value="30天" required>
                                <label for="visa-duration-30">30天</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="visa-duration-90" name="visa_validity_duration" value="90天" required>
                                <label for="visa-duration-90">90天</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group inline">
                    <div class="form-group">
                        <label for="visa_start_date">Visa Start Date 签证生效日期 <span class="required">*</span></label>
                        <input type="text" id="visa_start_date" name="visa_start_date" class="date-input" placeholder="DD/MM/YYYY" required>
                        <p class="note">Format: DD/MM/YYYY</p>
                    </div>
                    <div class="form-group">
                        <label for="intended_entry_gate">Intended Entry Gate 入境口岸 <span class="required">*</span></label>
                        <select id="intended_entry_gate" name="intended_entry_gate" required>
                            <option value="">Select Entry Gate</option>
                            <option value="Tan Son Nhat Int Airport (Ho Chi Minh City)" selected>胡志明</option>
                            <option value="Noi Bai Int Airport">河内</option>
                            <option value="Da Nang International Airport">岘港</option>
                            <option value="Cam Ranh Int Airport (Khanh Hoa)">芽庄</option>
                            <option value="Mong Cai Landport">芒街</option>
                            <option value="Huu Nghi Landport">友谊</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="form-actions">
                <button class="submit-btn" type="submit">提交申请</button>
            </div>
        </form>
        </div>
    </div>

    <!-- 提交状态弹窗 -->
    <div id="submitOverlay" class="submit-overlay">
        <div class="submit-modal">
            <div id="submitContent">
                <!-- 动态内容将在这里显示 -->
            </div>
        </div>
    </div>

    <!-- OCR 确认弹窗 -->
    <div id="ocrConfirmation" class="ocr-confirmation">
        <div class="ocr-modal">
            <div class="ocr-header">
                <h3>护照信息识别结果</h3>
                <p>请确认以下识别结果是否正确，您可以直接修改错误的信息</p>
            </div>
            <div id="ocrFields" class="ocr-fields">
                <!-- OCR 字段将在这里动态生成 -->
            </div>
            <div class="ocr-actions">
                <button type="button" class="btn-secondary" onclick="cancelOCR()">取消</button>
                <button type="button" class="btn-primary" onclick="confirmOCR()">确认并填入表单</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/form.js"></script>
</body>
</html>
