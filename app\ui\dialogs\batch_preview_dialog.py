from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QAbstractItemView
)
from PyQt6.QtCore import Qt, pyqtSignal
from typing import List
from app.data.models import Applicant
from app.utils.logger_config import get_logger
from pathlib import Path
import re

logger = get_logger()


# 签证类型映射（英文 → 中文）
visa_type_map = {"Single-entry": "单次", "Multiple-entry": "多次"}


# 入境口岸映射（英文 → 中文）
ENTRY_GATE_MAP = {
    "Tan Son Nhat Int Airport (Ho Chi Minh City)": "胡志明",
    "Noi Bai Int Airport": "河内",
    "Cam Ranh Int Airport (Khanh Hoa)": "芽庄",
    "Da Nang International Airport": "岘港",
    "Mong Cai Landport": "东兴",
    "Huu Nghi Landport": "友谊"
}


class BatchPreviewDialog(QDialog):
    applicant_selected = pyqtSignal(Applicant)

    def __init__(self, applicants: List[Applicant], parent=None):
        super().__init__(parent)
        self.setWindowTitle("批量导入预览列表")
        self.resize(1300, 520)
        self.applicants = applicants
        


        self.headers = [
            "序号", "中文姓名", "姓", "名", "护照号码", "性别", "出生日期",
            "签证类型", "签证有效期", "签证生效日期", "入境口岸", "手机号", "邮箱"
        ]

        layout = QVBoxLayout()
        self.table = QTableWidget()
        self.table.setColumnCount(len(self.headers))
        self.table.setHorizontalHeaderLabels(self.headers)
        self.table.setEditTriggers(QAbstractItemView.EditTrigger.DoubleClicked)  # 允许双击编辑

        self.table.setRowCount(len(applicants))
        for i, a in enumerate(applicants):
            photo_number = Path(str(a.portrait_photo_path)).stem  # ✅ 从照片命名提取序号
            self.table.setItem(i, 0, QTableWidgetItem(photo_number))  # ✅ 展示在序号列
            self.table.setItem(i, 1, QTableWidgetItem(a.chinese_name or ""))
            #self.table.setItem(i, 2, QTableWidgetItem(f"{a.surname} {a.given_name}".strip()))
            self.table.setItem(i, 2, QTableWidgetItem(a.surname or ""))     
            self.table.setItem(i, 3, QTableWidgetItem(a.given_name or ""))
            self.table.setItem(i, 4, QTableWidgetItem(a.passport_number or ""))
            self.table.setItem(i, 5, QTableWidgetItem(a.sex or ""))
            self.table.setItem(i, 6, QTableWidgetItem(a.dob or ""))
            self.table.setItem(i, 7, QTableWidgetItem(visa_type_map.get(a.visa_entry_type, a.visa_entry_type)))
            self.table.setItem(i, 8, QTableWidgetItem(str(a.visa_validity_duration or "")))
            self.table.setItem(i, 9, QTableWidgetItem(a.visa_start_date or ""))
            self.table.setItem(i, 10, QTableWidgetItem(ENTRY_GATE_MAP.get(a.intended_entry_gate, a.intended_entry_gate)))
            self.table.setItem(i, 11, QTableWidgetItem(a.telephone_number or ""))
            self.table.setItem(i, 12, QTableWidgetItem(a.email or ""))


        self.table.itemChanged.connect(self._on_item_changed)  # 连接编辑事件,双击编辑修改表格内容

        self.table.resizeColumnsToContents()
        #self.table.itemDoubleClicked.connect(self._on_row_double_clicked)  #移除双击关闭逻辑
        layout.addWidget(self.table)

        btn_close = QPushButton("关闭")
        #btn_close.clicked.connect(self.accept)
        btn_close.clicked.connect(self._on_close_clicked)
        layout.addWidget(btn_close)

        self.setLayout(layout)

    def _on_row_double_clicked(self, item):
        row = item.row()
        if 0 <= row < len(self.applicants):
            selected = self.applicants[row]
            self.applicant_selected.emit(selected)
            self.accept()


    #修改批量导入表格内容
    def _on_item_changed(self, item: QTableWidgetItem):     
        row = item.row()
        col = item.column()
        new_value = item.text().strip()

        if row >= len(self.applicants):
            return

        applicant = self.applicants[row]
        header = self.headers[col]

        # ✅ 中文列名 → Applicant 字段名 映射表

        header_to_field_map = {
            "中文姓名": "chinese_name",
            "姓": "surname",
            "名": "given_name",
            "护照号码": "passport_number",
            "性别": "sex",
            "出生日期": "dob",
            "签证类型": "visa_entry_type",
            "签证有效期": "visa_validity_duration",
            "签证生效日期": "visa_start_date",
            "入境口岸": "intended_entry_gate",
            "手机号": "telephone_number",
            "邮箱": "email"
        }

        field = header_to_field_map.get(header)
        if not field:
            return   # 跳过无法映射的字段

        # ✅ 中文 → 英文反向映射
        if field == "visa_entry_type":
            reverse_map = {"单次": "Single-entry", "多次": "Multiple-entry"}
            new_value = reverse_map.get(new_value, new_value)

        if field == "intended_entry_gate":
            reverse_map = {
                "胡志明": "Tan Son Nhat Int Airport (Ho Chi Minh City)",
                "河内": "Noi Bai Int Airport",
                "芽庄": "Cam Ranh Int Airport (Khanh Hoa)",
                "岘港": "Da Nang International Airport",
                "东兴": "Mong Cai Landport",  # 添加新的东兴口岸
                "友谊": "Huu Nghi Landport"   # 添加新的友谊口岸
            }
            new_value = reverse_map.get(new_value, new_value)

        if field == "visa_validity_duration":
            match = re.search(r"\d+", str(new_value))
            if match:
                new_value = int(match.group(0))
            else:
                new_value = 0

        setattr(applicant, field, new_value)
        #logger.info(f"✅ 已更新 Applicant[{row}].{field} = {new_value}")
        logger.info(f"✅ 第{row+1}行{applicant.chinese_name}.{field} 已更新为：{getattr(applicant, field)}")
    def _on_close_clicked(self):
        """点击关闭按钮后打印所有 applicant 的当前数据"""
        for i, a in enumerate(self.applicants):
            logger.info(f"第 {i+1} 位 Applicant 数据: {a.__dict__}")
        self.accept()



       

