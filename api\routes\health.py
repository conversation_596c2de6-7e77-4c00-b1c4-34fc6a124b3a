# api/routes/health.py
from fastapi import APIRouter, Depends
from datetime import datetime, timedelta
import psutil
import os
from pathlib import Path

from ..models.responses import HealthResponse
from ..config.settings import settings
from ..database.factory import check_database_health

router = APIRouter(prefix="/health", tags=["Health Check"])

# 应用启动时间
app_start_time = datetime.now()


def get_uptime() -> str:
    """获取应用运行时间"""
    uptime_delta = datetime.now() - app_start_time
    days = uptime_delta.days
    hours, remainder = divmod(uptime_delta.seconds, 3600)
    minutes, _ = divmod(remainder, 60)

    if days > 0:
        return f"{days} days, {hours} hours, {minutes} minutes"
    elif hours > 0:
        return f"{hours} hours, {minutes} minutes"
    else:
        return f"{minutes} minutes"


async def check_database_connection() -> str:
    """检查数据库连接"""
    try:
        db_health = await check_database_health()
        return db_health.get("database", "error")
    except Exception:
        return "error"


def check_ocr_service() -> str:
    """检查OCR服务"""
    try:
        # 这里应该实际检查OCR服务可用性
        # 暂时检查环境变量是否配置
        if os.getenv("ALIYUN_APPCODE"):
            return "available"
        else:
            return "not_configured"
    except Exception:
        return "error"


def check_email_service() -> str:
    """检查邮件服务"""
    try:
        # 这里应该实际检查邮件服务状态
        # 暂时检查环境变量是否配置
        if os.getenv("EMAIL_HOST_1"):
            return "running"
        else:
            return "not_configured"
    except Exception:
        return "error"


def get_system_info() -> dict:
    """获取系统信息"""
    try:
        return {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent,
            "process_count": len(psutil.pids())
        }
    except Exception:
        return {}


@router.get("/", response_model=HealthResponse)
async def health_check():
    """
    基础健康检查

    - **返回**: 服务健康状态
    """
    dependencies = {
        "database": await check_database_connection(),
        "ocr_service": check_ocr_service(),
        "email_service": check_email_service()
    }

    # 判断整体健康状态
    all_healthy = all(status in ["connected", "available", "running"] for status in dependencies.values())
    status = "healthy" if all_healthy else "degraded"

    return HealthResponse(
        success=True,
        message="健康检查完成",
        status=status,
        version=settings.app_version,
        uptime=get_uptime(),
        dependencies=dependencies
    )


@router.get("/detailed")
async def detailed_health_check():
    """
    详细健康检查

    - **返回**: 详细的系统状态信息
    """
    dependencies = {
        "database": check_database_connection(),
        "ocr_service": check_ocr_service(),
        "email_service": check_email_service()
    }

    system_info = get_system_info()

    # 判断整体健康状态
    all_healthy = all(status in ["connected", "available", "running"] for status in dependencies.values())
    status = "healthy" if all_healthy else "degraded"

    return {
        "success": True,
        "message": "详细健康检查完成",
        "status": status,
        "version": settings.app_version,
        "uptime": get_uptime(),
        "dependencies": dependencies,
        "system": system_info,
        "configuration": {
            "debug_mode": settings.debug,
            "max_file_size_mb": settings.max_file_size / (1024 * 1024),
            "allowed_file_types": settings.allowed_file_types,
            "log_level": settings.log_level
        },
        "timestamp": datetime.now().isoformat()
    }


@router.get("/ready")
async def readiness_check():
    """
    就绪检查（用于Kubernetes等容器编排）

    - **返回**: 服务是否就绪
    """
    dependencies = {
        "database": check_database_connection(),
        "ocr_service": check_ocr_service()
    }

    # 关键服务必须可用
    critical_services_ready = (
        dependencies["database"] == "connected" and
        dependencies["ocr_service"] in ["available", "not_configured"]  # OCR可以不配置
    )

    if critical_services_ready:
        return {
            "success": True,
            "message": "服务就绪",
            "ready": True,
            "timestamp": datetime.now().isoformat()
        }
    else:
        return {
            "success": False,
            "message": "服务未就绪",
            "ready": False,
            "dependencies": dependencies,
            "timestamp": datetime.now().isoformat()
        }


@router.get("/live")
async def liveness_check():
    """
    存活检查（用于Kubernetes等容器编排）

    - **返回**: 服务是否存活
    """
    return {
        "success": True,
        "message": "服务存活",
        "alive": True,
        "timestamp": datetime.now().isoformat()
    }
