# 文件：app/ui/widgets/image_drop_box.py
from PyQt6.QtWidgets import QLabel, QFileDialog
from PyQt6.QtWidgets import QWidget
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap, QDragEnterEvent, QDropEvent
from PyQt6.QtWidgets import QVBoxLayout
from PyQt6.QtCore import QSize


class ImageDropBox(QWidget):
    imageChanged = pyqtSignal(str)  # 发出图像路径

    def __init__(self, label_text: str, file_type: str, parent=None):
        super().__init__(parent)
        #self.label = QLabel(label_text)
        #self.image_label = QLabel()
        self.setAcceptDrops(True)
        self.file_type = file_type  # 'passport' or 'portrait'

        
        #self.label = QLabel(label_text)
        #self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        #self.label.setStyleSheet("color: gray; font-size: 12px;")

        # 创建图像标签
        self.image_label = QLabel()
        #self.image_label.setFixedHeight(50)
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        #self.image_label.setStyleSheet("border: 2px dashed gray;")

        #self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        #self.setStyleSheet("border: 2px dashed gray; padding: 10px; font-size: 14px;")
        #layout = QVBoxLayout()
        #layout.addWidget(self.label)
        #layout.addWidget(self.image_label)
        #self.setLayout(layout)
        #self.setMaximumHeight(600) # 设置最大高度为 600 像素

         # 设置更美观的样式
        self.image_label.setStyleSheet("""
            border: 2px dashed #AAAAAA;
            border-radius: 8px;
            background-color: #F8F8F8;
            color: #888888;
            font-size: 13px;
        """)
        
        # 将提示文本直接设置到图像标签
        self.image_label.setText(label_text)
        
        # 创建布局，只添加图像标签
        layout = QVBoxLayout()
        layout.addWidget(self.image_label)
        layout.setContentsMargins(0, 0, 0, 0)  # 移除内边距
        layout.setSpacing(0)  # 移除间距
        self.setLayout(layout)

        # 移除 widget 自身的边距和样式
        self.setContentsMargins(0, 0, 0, 0)
        self.setStyleSheet("background: transparent; border: none; padding: 0px;")

    def mousePressEvent(self, event):
        file_filter = "图片文件 (*.jpg *.jpeg *.png)"
        file_name, _ = QFileDialog.getOpenFileName(self, f"选择{self.file_type}图像", "", file_filter)
        if file_name:
            self.load_image(file_name)

    def dragEnterEvent(self, event: QDragEnterEvent):
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event: QDropEvent):
        url = event.mimeData().urls()[0]
        file_path = url.toLocalFile()
        if file_path.lower().endswith((".jpg", ".jpeg", ".png")):
            self.load_image(file_path)

    def load_image(self, path: str):
        # ✅ 修改为（可根据类型动态调整）：
        if self.file_type == "passport":
            target_size = QSize(230, 220)
        else:
            target_size = QSize(140, 180)
        pixmap = QPixmap(path).scaled(target_size, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
        self.image_label.setPixmap(pixmap)
        self.imageChanged.emit(path)
