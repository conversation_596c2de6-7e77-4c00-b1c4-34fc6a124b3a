# initialize_db.py 初始化数据库

import sqlite3
from pathlib import Path

DB_PATH = Path("results/visa_results.db")
SCHEMA_PATH = Path("db/schema.sql")

# 创建 results 目录（如果不存在）
DB_PATH.parent.mkdir(parents=True, exist_ok=True)

with sqlite3.connect(DB_PATH) as conn:
    with open(SCHEMA_PATH, "r", encoding="utf-8") as f:
        sql = f.read()
        conn.executescript(sql)

print("✅ 数据库初始化完成：visa_results.db")
