#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
越南酒店地址数据
包含河内、胡志明市、岘港和芽庄4个城市的酒店地址
每个城市10个酒店地址，共40个地址
"""

# 越南酒店地址字典
hotel_addresses = {
    "Hanoi": [
        "11 Hoang Dieu, Hoan Kiem District, Hanoi, Vietnam",
        "114 <PERSON><PERSON>u, Long Bien, Hanoi, Vietnam",
        "179 Quan Thanh, Hanoi, Vietnam",
        "102 Hai Ba Trung, Cau Giay, Vietnam",
        "No. 40, <PERSON><PERSON>, Cau Giay, Hanoi, Vietnam",
        "231 Quang Trung, Hai Ba Trung, Hanoi, Vietnam",
        "53 <PERSON><PERSON>, <PERSON>y <PERSON>, Hanoi, Vietnam",
        "225 Dien Bien Phu, Cau Giay District, Hanoi, Vietnam",
        "210 Hoang Dieu, Tay Ho District, Hanoi, Vietnam",
        "29 Trang Tien St, Hoan <PERSON>em Dist, Vietnam"
    ],
    "Ho Chi Minh": [
        "82 Dien Bien Phu, District 4 District, Ho <PERSON> Minh, Vietnam",
        "195 Le Duan, District 4, <PERSON>, Vietnam",
        "275 Le Duan Street, District 5, Ho <PERSON>, Vietnam",
        "Nguyen Dinh Chieu Street 12a phan <PERSON>hiet, Ho <PERSON>, Vietnam",
        "23 Dong Khoi, District 1, Ho <PERSON>, Vietnam",
        "180 Nguyen Hue, District 1, Ho Chi Minh, Vietnam",
        "42 Le Loi, District 1, Ho Chi Minh, Vietnam",
        "135 Nam Ky Khoi Nghia, District 3, Ho Chi Minh, Vietnam",
        "257 Vo Van Kiet, District 6, Ho Chi Minh, Vietnam",
        "78 Nguyen Van Troi, Phu Nhuan, Ho Chi Minh, Vietnam"
    ],
    "Da Nang": [
        "120 Bach Dang, Ngu Hanh Son District, Da Nang, Vietnam",
        "182 Hung Vuong, Son Tra, Vietnam",
        "88 Hoang Dieu, Thanh Khe District, Da Nang, Vietnam",
        "105 Tran Phu, Hai Chau, Da Nang, Vietnam",
        "224 Le Duan, Thanh Khe, Da Nang, Vietnam",
        "75 Nguyen Van Linh, Hai Chau, Da Nang, Vietnam",
        "167 Hoang Dieu, Son Tra District, Da Nang, Vietnam",
        "58 Tran Hung Dao, Hai Chau, Da Nang, Vietnam",
        "139 Phan Chu Trinh, Thanh Khe, Da Nang, Vietnam",
        "210 Le Loi, Hai Chau District, Da Nang, Vietnam"
    ],
    "Nha Trang": [
        "19 Tran Phu, Loc Tho, Nha Trang, Vietnam",
        "102 Hung Vuong, Loc Tho, Nha Trang, Vietnam",
        "75 Nguyen Thien Thuat, Tan Lap, Nha Trang, Vietnam",
        "128 Hoang Hoa Tham, Loc Tho, Nha Trang, Vietnam",
        "256 Tran Phu, Van Thanh, Nha Trang, Vietnam",
        "38 Le Thanh Ton, Phuoc Tien, Nha Trang, Vietnam",
        "115 Nguyen Thien Thuat, Tan Lap, Nha Trang, Vietnam", 
        "45 Pham Van Dong, Tan Lap, Nha Trang, Vietnam",
        "178 Tran Phu Beach Street, Loc Tho, Nha Trang, Vietnam",
        "209 Hung Vuong, Van Thanh, Nha Trang, Vietnam"
    ]
}

# 如果这个文件被直接运行
if __name__ == "__main__":
    # 打印每个城市的地址数量
    print("越南酒店地址统计:")
    for city, addresses in hotel_addresses.items():
        print(f"{city}: {len(addresses)}个地址")
    
    # 打印总地址数量
    total_addresses = sum(len(addresses) for addresses in hotel_addresses.values())
    print(f"总计: {total_addresses}个地址") 