# nginx.conf

# 可选: 定义 Nginx worker 进程数，通常设置为 CPU 核心数或 "auto"
# worker_processes auto;

events {
    worker_connections 1024; # 每个 worker 进程可以处理的最大并发连接数
}

http {
    # 上游服务定义 (我们的 FastAPI 应用)
    # Docker Compose 会将服务名 'visa-automator' 解析为该服务下所有容器的 IP 地址
    # Nginx 会对这些 IP 地址进行轮询负载均衡
    upstream backend_fastapi {
        # server host.docker.internal:8000; # 如果是从 Nginx 容器访问宿主机上直接运行的服务
        server visa-automator:8000;   # Docker Compose 服务名和容器内监听的端口
                                     # Nginx 会自动轮询所有 visa-automator 的副本
    }

    # 可选: 如果你的 FastAPI 应用也提供静态文件，而你想让 Nginx 直接处理它们
    # location /static/ {
    #     alias /app/static/; # 假设静态文件在容器内的 /app/static/ 目录
    #     expires 30d;
    #     add_header Cache-Control "public";
    # }

    server {
        listen 8000; # Nginx 监听其容器内的 8000 端口
        server_name localhost; # 或者你的域名，如果适用

         # ---- 新增/修改的静态文件处理 ----
        location /static/ {
            alias /usr/share/nginx/html/static/; # 指向 Nginx 容器内静态文件所在的路径
                                                # 注意末尾的斜杠很重要
            expires 7d;                         # 告诉浏览器缓存7天
            add_header Cache-Control "public";  # 声明为公共缓存

            # 可选：启用 Gzip 压缩 (确保 Nginx 编译时包含 ngx_http_gzip_module)
            # gzip on;
            # gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript image/svg+xml;
            # gzip_proxied any;
            # gzip_vary on;
            # gzip_comp_level 6;
            # gzip_buffers 16 8k;
            # gzip_http_version 1.1;

            # 可选：如果文件不存在，可以尝试代理回 FastAPI（虽然通常不需要）
            # try_files $uri $uri/ @fallback_to_fastapi;
        }
            # location @fallback_to_fastapi { # 如果使用 try_files
            #     proxy_pass http://backend_fastapi;
            #     proxy_set_header Host $host;
            #     proxy_set_header X-Real-IP $remote_addr;
            #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            #     proxy_set_header X-Forwarded-Proto $scheme;
            # }
            # ---- 结束新增/修改的静态文件处理 ----

        location / {
            proxy_pass http://backend_fastapi; # 将所有请求代理到上游的 FastAPI 服务组
            # 设置一些常用的代理头部，以便 FastAPI 应用能获取到原始请求信息
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 可选: 增加超时设置
            proxy_connect_timeout 60s;   # 与上游服务器建立连接的超时时间
            proxy_send_timeout    60s;   #向上游服务器发送请求的超时时间
            proxy_read_timeout    180s;  # 从上游服务器读取响应的超时时间 (设置为大于2分钟，比如3分钟=180秒)
        }

        # 可选: 错误页面处理
        # error_page 500 502 503 504 /50x.html;
        # location = /50x.html {
        #     root /usr/share/nginx/html; # Nginx 默认的 HTML 目录
        # }
    }

    # 可选: 如果需要，可以添加其他 server 块或 http 配置
    # include /etc/nginx/conf.d/*.conf;
}