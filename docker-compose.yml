services:
  visa-automator:
    build: .
    #container_name: visa-automator  移除容器名称，便于创建多个容器副本
    #deploy:
      #replicas: 5
    volumes:
      - ./:/app
    env_file:
      - ./.env
    shm_size: 16gb  # 增加共享内存大小，对Playwright很重要
    restart: unless-stopped
    # ports: # 我们不再将 visa-automator 的端口直接暴露给主机，Nginx 会处理
      # - "8000" # 注释掉这一行，或移除
    
    command: ["uvicorn", "api_main:app", "--host", "0.0.0.0", "--port", "8000"]
    expose: # 可选：明确声明容器内部暴露的端口，对 Nginx 来说并非必需，但好习惯
      - "8000"
    networks: # 定义一个自定义网络，以便服务间可以通过服务名通信
      - app-network

  nginx-lb:
    image: nginx:latest # 或者 nginx:alpine 以获得更小的镜像
    container_name: visa_automator_nginx_lb # 给 Nginx 一个固定的容器名
    ports:
      - "8000:8000"       # Nginx 监听主机的 80 端口，并映射到其容器内的 80 端口
      # - "443:443"   # 如果你以后需要 HTTPS，可以取消注释并配置 SSL
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro # 将本地的 nginx.conf 挂载到 Nginx 容器内，ro 表示只读
      - ./static_files:/usr/share/nginx/html/static:ro # 新增：将本地 static_files 挂载到 Nginx 的一个可访问路径
      # - ./certs:/etc/nginx/certs:ro         # 如果有 SSL 证书，可以挂载证书目录
    depends_on:
      - visa-automator # 确保 visa-automator 服务先启动（或至少开始启动）
    restart: unless-stopped
    networks:
      - app-network

networks:
  app-network:
    driver: bridge # Docker 默认的 bridge 网络驱动

# 使用 scale 命令来启动多个 visa-automator 实例：
# docker-compose up -d --scale visa-automator=3 nginx-lb
# (这将启动 3 个 visa-automator 实例和 1 个 nginx-lb 实例)
# 或者只启动并扩展服务，Nginx 单独定义：
# docker-compose up -d --scale visa-automator=3
# docker-compose up -d nginx-lb # 需要确保 nginx-lb 依赖的 visa-automator 已启动  
