-- PostgreSQL初始化脚本
-- 基于SQLite schema.sql转换而来

-- 设置时区为北京时间
SET timezone = 'Asia/Shanghai';

-- Table: applicant
CREATE TABLE IF NOT EXISTS applicant (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    customer_source TEXT,
    chinese_name TEXT,
    surname TEXT,
    given_name TEXT,
    sex TEXT,
    dob TEXT,
    place_of_birth TEXT,
    passport_number TEXT UNIQUE,
    place_of_issue TEXT,
    date_of_issue TEXT,
    passport_expiry TEXT,
    nationality TEXT,
    email TEXT,
    telephone_number TEXT
);

-- Table: visa_task
CREATE TABLE IF NOT EXISTS visa_task (
    id SERIAL PRIMARY KEY,
    applicant_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    visa_type TEXT,
    visa_validity_days INTEGER,
    visa_start_date TEXT,
    entry_gate TEXT,
    application_number TEXT,
    submit_status TEXT,
    approval_status TEXT,
    is_downloaded INTEGER DEFAULT 0,
    pdf_path TEXT,
    download_time TIMESTAMP WITH TIME ZONE,
    screenshot_path TEXT,
    log_path TEXT,
    error_message TEXT,
    session_id TEXT,
    CONSTRAINT fk_visa_task_applicant FOREIGN KEY (applicant_id) REFERENCES applicant(id) ON DELETE CASCADE
);

-- Table: payment_record
CREATE TABLE IF NOT EXISTS payment_record (
    id SERIAL PRIMARY KEY,
    visa_task_id INTEGER NOT NULL,
    payment_time TIMESTAMP WITH TIME ZONE,
    payment_amount DECIMAL(10,2),
    card_number_tail TEXT,
    payment_method TEXT,
    payment_status TEXT,
    transaction_id TEXT,
    source_email TEXT,
    CONSTRAINT fk_payment_record_visa_task FOREIGN KEY (visa_task_id) REFERENCES visa_task(id) ON DELETE CASCADE
);

-- Table: email_notification
CREATE TABLE IF NOT EXISTS email_notification (
    id SERIAL PRIMARY KEY,
    visa_task_id INTEGER,  -- 可为空，允许未能匹配任务
    received_time TIMESTAMP WITH TIME ZONE,
    from_address TEXT,
    subject TEXT,
    content_excerpt TEXT,
    application_number TEXT,
    email_type TEXT,
    CONSTRAINT fk_email_notification_visa_task FOREIGN KEY (visa_task_id) REFERENCES visa_task(id) ON DELETE SET NULL
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_applicant_passport ON applicant(passport_number);
CREATE INDEX IF NOT EXISTS idx_applicant_email ON applicant(email);
CREATE INDEX IF NOT EXISTS idx_visa_task_applicant ON visa_task(applicant_id);
CREATE INDEX IF NOT EXISTS idx_visa_task_application ON visa_task(application_number);
CREATE INDEX IF NOT EXISTS idx_visa_task_status ON visa_task(submit_status);
CREATE INDEX IF NOT EXISTS idx_visa_task_approval ON visa_task(approval_status);
CREATE INDEX IF NOT EXISTS idx_visa_task_created ON visa_task(created_at);
CREATE INDEX IF NOT EXISTS idx_payment_task ON payment_record(visa_task_id);
CREATE INDEX IF NOT EXISTS idx_payment_status ON payment_record(payment_status);
CREATE INDEX IF NOT EXISTS idx_email_task ON email_notification(visa_task_id);
CREATE INDEX IF NOT EXISTS idx_email_application ON email_notification(application_number);
CREATE INDEX IF NOT EXISTS idx_email_type ON email_notification(email_type);

-- 创建视图以简化常用查询
CREATE OR REPLACE VIEW v_applicant_summary AS
SELECT 
    a.id,
    a.chinese_name,
    a.surname,
    a.given_name,
    a.passport_number,
    a.email,
    a.created_at,
    COUNT(vt.id) as total_tasks,
    COUNT(CASE WHEN vt.submit_status = 'SUCCESS' THEN 1 END) as successful_tasks,
    COUNT(CASE WHEN vt.is_downloaded = 1 THEN 1 END) as downloaded_tasks,
    MAX(vt.created_at) as last_task_date
FROM applicant a
LEFT JOIN visa_task vt ON a.id = vt.applicant_id
GROUP BY a.id, a.chinese_name, a.surname, a.given_name, a.passport_number, a.email, a.created_at;

-- 创建函数以获取北京时间
CREATE OR REPLACE FUNCTION get_beijing_time()
RETURNS TIMESTAMP WITH TIME ZONE AS $$
BEGIN
    RETURN NOW() AT TIME ZONE 'Asia/Shanghai';
END;
$$ LANGUAGE plpgsql;

-- 插入初始数据（如果需要）
-- INSERT INTO applicant (customer_source, chinese_name, surname, given_name, sex, dob, place_of_birth, passport_number, nationality, email, telephone_number)
-- VALUES ('SYSTEM', '测试用户', 'TEST', 'USER', 'M', '01/01/1990', 'Beijing', 'TEST123456', 'CHINA', '<EMAIL>', '+86 138 0000 0000')
-- ON CONFLICT (passport_number) DO NOTHING;

-- 显示初始化完成信息
DO $$
BEGIN
    RAISE NOTICE '✅ PostgreSQL数据库初始化完成';
    RAISE NOTICE '📊 数据库名称: %', current_database();
    RAISE NOTICE '🕐 当前时间: %', get_beijing_time();
END $$;
