from pathlib import Path
from report.report_tools import *

date = "2025-05-12"

all_today = query_tasks_by_date(date)
paid_today = query_paid_tasks_by_date(date)
unpaid_today = query_unpaid_tasks_by_date(date)
all_tasks = query_all_tasks()

export_tasks_to_csv(all_today, Path(f"exports/all_{date}.csv"))
export_tasks_to_csv(paid_today, Path(f"exports/paid_{date}.csv"))
export_tasks_to_csv(unpaid_today, Path(f"exports/unpaid_{date}.csv"))
export_tasks_to_csv(all_tasks, Path("exports/all_tasks.csv"))
