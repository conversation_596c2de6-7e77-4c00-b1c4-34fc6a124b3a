# 文件: app/data/models.py
from dataclasses import dataclass
from typing import Optional

@dataclass
class Applicant:
    passport_number: Optional[str] = None
    surname: Optional[str] = None
    given_name: Optional[str] = None
    sex: Optional[str] = None
    dob: Optional[str] = None
    place_of_birth: Optional[str] = None
    nationality: Optional[str] = None
    passport_type: Optional[str] = None
    passport_expiry: Optional[str] = None
    # V V V 确保这一行存在 V V V
    date_of_issue: Optional[str] = None # <-- 确保护照签发日期字段存在
    # ^ ^ ^ 确保这一行存在 ^ ^ ^
    email: Optional[str] = None
    telephone_number: Optional[str] = None
    permanent_address: Optional[str] = None
    contact_address: Optional[str] = None         # Added for UI
    emergency_address: Optional[str] = None
    emergency_contact_name: Optional[str] = None  # Added for UI (stores only name part)
    emergency_contact_phone: Optional[str] = None # Added for UI
    visa_entry_type: Optional[str] = None # <-- Add this field (e.g., "Single-entry" or "Multiple-entry")
    visa_validity_duration: Optional[str] = None # <-- Add: e.g., "30天" or "90天"
    visa_start_date: Optional[str] = None        # <-- Add: e.g., "DD/MM/YYYY"
    # === Add the missing field ===
    intended_entry_gate: Optional[str] = None # <-- Add This Line (Stores mapped airport name)
    # === ===
    # === 需要添加的是这几个 Python 字段定义 ===
    visited_vietnam_last_year: Optional[bool] = None  # True / False
    previous_entry_date: Optional[str] = None     # DD/MM/YYYY
    previous_exit_date: Optional[str] = None      # DD/MM/YYYY
    previous_purpose: Optional[str] = None        # 例如 "Travel"
    # === --- ===
    portrait_photo_path: Optional[str] = None
    passport_scan_path: Optional[str] = None
    place_of_issue: Optional[str] = None