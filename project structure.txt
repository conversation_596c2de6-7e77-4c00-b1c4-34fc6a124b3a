visa_automator/
├── venv/                     
├── config/
│   ├── settings.yaml          # 全局配置文件
│   ├── browser_config.py      # 浏览器配置管理
│   └── locators/
│       └── vietnam_evisa.yaml # 越南电子签证页面元素定位器
├── app/
│   ├── __init__.py           
│   ├── ui/
│   │   ├── __init__.py        
│   │   ├── main_window.py     # 主窗口实现
│   │   ├── styles/            # UI样式文件
│   │   │   └── modern_card.qss
│   │   └── tabs/
│   │       ├── __init__.py    
│   │       └── vietnam_tab.py # 越南签证申请标签页
│   ├── core/
│   │   ├── __init__.py        
│   │   ├── simple_engine.py   # 核心自动化引擎
│   │   └── user_prefs.py      # 用户偏好设置管理
│   ├── data/
│   │   ├── __init__.py        
│   │   └── models.py          # 数据模型定义
│   ├── fillers/
│   │   ├── __init__.py        
│   │   ├── interface.py       # 填表器接口定义
│   │   └── vietnam_filler.py  # 越南签证填表实现
│   ├── payment/
│   │   ├── __init__.py        
│   │   ├── payment_automation.py # 支付自动化实现
│   │   ├── payment_models.py  # 支付数据模型
│   │   ├── payment_cards.json # 信用卡配置(gitignore)
│   │   └── README.md          # 支付模块说明
│   ├── email/
│   │   ├── __init__.py        
│   │   ├── email_executor.py  # 邮件处理执行器
│   │   ├── email_trigger.py   # 邮件触发器
│   │   ├── handlers.py        # 邮件处理器
│   │   ├── result_handler.py  # 结果邮件处理
│   │   └── determine_email_type.py # 邮件类型判断
│   ├── downloader/
│   │   ├── __init__.py       
│   │   └── vietnam_evisa_downloader.py # 签证PDF下载器
│   ├── batch_processing/
│   │   ├── __init__.py        
│   │   └── contact_parser.py  # 批处理联系人解析
│   └── utils/
│       ├── __init__.py        
│       ├── logger_config.py   # 日志配置
│       └── retry_strategy.py  # 重试策略
├── db/
│   ├── __init__.py            
│   ├── db.py                  # 数据库操作
│   └── db_tools.py            # 数据库工具函数
├── logs/                      # 日志目录(gitignore)
├── screenshots/               # 截图目录(gitignore)
├── payment_screenshots/       # 支付截图目录(gitignore)
├── downloads/                 # 下载文件目录(gitignore)
├── .prefs/                    # 用户偏好设置目录(gitignore)
│   └── user_prefs.json
├── main.py                    # 主程序入口
├── run_batch_main.py          # 批处理入口
├── run_batch_main.bat         # Windows批处理启动脚本
├── uid_cache.json             # 邮件UID缓存(gitignore)
└── requirements.txt           # 项目依赖