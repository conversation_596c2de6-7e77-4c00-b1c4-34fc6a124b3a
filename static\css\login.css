/* 登录页专用样式 - Login Page Styles */

body {
    background:
        radial-gradient(ellipse at top, #0f1419 0%, #1a1a2e 30%, #16213e 60%, #0f0f23 85%, #000000 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

/* 星云效果 */
body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(75, 0, 130, 0.15) 0%, transparent 60%),
        radial-gradient(circle at 80% 70%, rgba(25, 25, 112, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 60% 20%, rgba(72, 61, 139, 0.08) 0%, transparent 70%),
        radial-gradient(circle at 30% 80%, rgba(138, 43, 226, 0.1) 0%, transparent 55%);
    background-size: 100% 100%;
    animation: nebula 50s ease-in-out infinite alternate;
    pointer-events: none;
}

/* 星空效果 - 第一层 */
body::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.9), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.7), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.5), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(200,200,255,0.6), transparent),
        radial-gradient(1px 1px at 300px 300px, rgba(255,255,255,0.4), transparent),
        radial-gradient(2px 2px at 350px 100px, rgba(255,255,255,0.6), transparent),
        radial-gradient(1px 1px at 250px 200px, rgba(255,255,255,0.7), transparent);
    background-repeat: repeat;
    background-size: 400px 300px;
    animation: sparkle 30s linear infinite;
    pointer-events: none;
}

/* 星云动画 */
@keyframes nebula {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.1) rotate(180deg);
        opacity: 0.9;
    }
    100% {
        transform: scale(1) rotate(360deg);
        opacity: 0.7;
    }
}

/* 星空闪烁动画 */
@keyframes sparkle {
    from { transform: translateX(0) translateY(0); }
    to { transform: translateX(-400px) translateY(-100px); }
}

/* 额外的星星层 */
.stars-layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(1px 1px at 50px 50px, rgba(255,255,255,0.3), transparent),
        radial-gradient(1px 1px at 100px 100px, rgba(255,255,255,0.25), transparent),
        radial-gradient(1px 1px at 150px 150px, rgba(255,255,255,0.4), transparent),
        radial-gradient(1px 1px at 200px 200px, rgba(255,255,255,0.2), transparent),
        radial-gradient(2px 2px at 250px 50px, rgba(255,255,255,0.5), transparent),
        radial-gradient(1px 1px at 320px 180px, rgba(173,216,230,0.4), transparent),
        radial-gradient(1px 1px at 80px 280px, rgba(255,255,255,0.3), transparent);
    background-repeat: repeat;
    background-size: 380px 380px;
    animation: sparkle 40s linear infinite reverse;
    pointer-events: none;
}

.login-container {
    background: rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(25px) saturate(180%);
    -webkit-backdrop-filter: blur(25px) saturate(180%);
    padding: 3.5rem 3rem;
    border-radius: 20px;
    box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.25),
        inset 0 0 20px rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: 100%;
    max-width: 520px;
    margin: 2rem;
    position: relative;
    z-index: 10;
}

/* 增强毛玻璃效果的内容区域 */
.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: -1;
}

.login-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.login-header h1 {
    color: #ffffff;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    letter-spacing: -0.025em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.login-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    font-weight: 400;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.form-group input {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.2s ease;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-group input:focus {
    outline: none;
    border-color: rgba(37, 99, 235, 0.8);
    background: rgba(255, 255, 255, 0.35);
    box-shadow:
        0 0 0 3px rgba(37, 99, 235, 0.3),
        inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.remember-me input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
    cursor: pointer;
    accent-color: var(--primary-color);
}

.remember-me label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.login-actions {
    margin-top: 2rem;
}

.login-btn {
    width: 100%;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

.secondary-actions {
    margin-top: 1.5rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.forgot-password,
.register-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
}

.forgot-password:hover,
.register-link:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

.error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #fca5a5;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    font-size: 0.9rem;
    text-align: center;
    backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (max-width: 640px) {
    .login-container {
        margin: 1rem;
        padding: 2.5rem 2rem;
    }

    .login-header h1 {
        font-size: 1.5rem;
    }
}
