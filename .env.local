# 本地开发环境配置 - 使用SQLite
# 复制到 .env 文件使用

# 数据库配置 - 本地使用SQLite
DATABASE_TYPE=sqlite
SQLITE_PATH=results/visa_results.db

# API配置
SECRET_KEY=your-very-secret-key-for-local-dev
DEBUG=true

# 用户认证
API_BASIC_USERS=admin,user1
API_BASIC_PASSWORDS=admin123,user123

# 文件上传配置
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png

# OCR配置
ALIYUN_APPCODE=your_aliyun_appcode_here
OCR_TEMP_DIR=temp/ocr

# 日志配置
LOG_LEVEL=DEBUG
LOG_DIR=logs/fastapi
LOG_FILENAME_PREFIX=vietnam_evisa_api

# 其他现有配置...
ANTI_CAPTCHA_API_KEY=your_anti_captcha_key_here
BROWSER=chromium
HEADLESS=true
SLOW_MO=0
VIETNAM_EVISA_URL=https://evisa.gov.vn/

# 邮箱配置
EMAIL_HOST_1=imap.gmail.com
EMAIL_PORT_1=993
EMAIL_USER_1=<EMAIL>
EMAIL_PASSWORD_1=your_app_password
