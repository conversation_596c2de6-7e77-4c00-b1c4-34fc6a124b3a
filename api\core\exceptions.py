# api/core/exceptions.py
from fastapi import HTTP<PERSON>x<PERSON>, Request
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import logging

logger = logging.getLogger(__name__)


class VisaApplicationError(Exception):
    """签证申请相关错误"""
    def __init__(self, message: str, error_code: str = "VISA_ERROR"):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class OCRProcessingError(Exception):
    """OCR处理错误"""
    def __init__(self, message: str, error_code: str = "OCR_ERROR"):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class FileProcessingError(Exception):
    """文件处理错误"""
    def __init__(self, message: str, error_code: str = "FILE_ERROR"):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class AuthenticationError(Exception):
    """认证错误"""
    def __init__(self, message: str, error_code: str = "AUTH_ERROR"):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


async def visa_application_exception_handler(request: Request, exc: VisaApplicationError):
    """签证申请异常处理器"""
    logger.error(f"Visa application error: {exc.message}")
    return JSONResponse(
        status_code=400,
        content={
            "success": False,
            "message": exc.message,
            "error_code": exc.error_code,
            "type": "visa_application_error"
        }
    )


async def ocr_processing_exception_handler(request: Request, exc: OCRProcessingError):
    """OCR处理异常处理器"""
    logger.error(f"OCR processing error: {exc.message}")
    return JSONResponse(
        status_code=400,
        content={
            "success": False,
            "message": exc.message,
            "error_code": exc.error_code,
            "type": "ocr_processing_error"
        }
    )


async def file_processing_exception_handler(request: Request, exc: FileProcessingError):
    """文件处理异常处理器"""
    logger.error(f"File processing error: {exc.message}")
    return JSONResponse(
        status_code=400,
        content={
            "success": False,
            "message": exc.message,
            "error_code": exc.error_code,
            "type": "file_processing_error"
        }
    )


async def authentication_exception_handler(request: Request, exc: AuthenticationError):
    """认证异常处理器"""
    logger.error(f"Authentication error: {exc.message}")
    return JSONResponse(
        status_code=401,
        content={
            "success": False,
            "message": exc.message,
            "error_code": exc.error_code,
            "type": "authentication_error"
        }
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    logger.error(f"Validation error: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": "请求数据验证失败",
            "errors": exc.errors(),
            "type": "validation_error"
        }
    )


async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """HTTP异常处理器"""
    logger.error(f"HTTP error {exc.status_code}: {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "status_code": exc.status_code,
            "type": "http_error"
        }
    )


async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"Unexpected error: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务器内部错误",
            "type": "internal_error"
        }
    )
