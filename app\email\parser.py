import re
from datetime import datetime, timedelta
from typing import Optional, Dict, <PERSON><PERSON>
from bs4 import BeautifulSoup
from app.utils.logger_config import get_logger
from app.email.determine_email_type import determine_email_type  # 导入函数
from app.utils.retry_strategy import email_parsing_retry



logger = get_logger()

@email_parsing_retry
def parse_email(email_obj: Dict) -> <PERSON><PERSON>[str, Optional[Dict]]:
    """
    解析邮件并返回邮件类型和解析结果
    
    参数:
        email_obj: 包含邮件信息的字典，至少包含 from, subject, body 字段
        
    返回:
        Tuple[str, Optional[Dict]]: 邮件类型和解析结果的元组
    """
    sender = email_obj.get("from", "")
    subject = email_obj.get("subject", "")
    body = email_obj.get("body", "")
    # 添加调试日志
    logger.debug(f"开始解析邮件 - 发件人: {sender}, 主题: {subject[:50]}...")
    
    # 使用导入的函数确定邮件类型
    email_type = determine_email_type(sender, subject, body)
    logger.debug(f"确定邮件类型: {email_type}")
    
    # 根据邮件类型选择相应的解析函数
    if email_type == "submission":
        logger.debug("调用提交确认邮件解析函数")
        result = parse_submission_email(body)
    elif email_type == "payment":
        logger.debug("调用付款确认邮件解析函数")
        result = parse_payment_email(body,email_obj.get("received_time"))
    elif email_type == "result":
        logger.debug("调用出签结果邮件解析函数")
        result = parse_result_email(body)
    else:
        result = None
        logger.debug("未知邮件类型，不进行解析")

    # 记录解析结果
    if result:
        logger.debug(f"邮件解析成功 - 类型: {email_type}, 结果: {result}")
    else:
        logger.debug(f"邮件解析失败或无结果 - 类型: {email_type}")


    return email_type, result

@email_parsing_retry
def parse_submission_email(content: str) -> Optional[dict]:
    """
    解析提交确认邮件，提取申请编号、姓名、出生日期
    
    参数:
        content: 邮件正文内容
        
    返回:
        Optional[dict]: 解析结果字典，包含 application_number, full_name, dob 字段
    """
    logger.debug("开始解析提交确认邮件")
    logger.debug(f"邮件内容前200字符: {content[:200]}...")

    try:
        # 使用BeautifulSoup提取纯文本和签证查询&下载链接
        soup = BeautifulSoup(content, "html.parser")
        text = soup.get_text(separator=" ", strip=True)  # 提取纯文本
        logger.debug(f"提取的纯文本前200字符: {text[:200]}...")
        logger.info(f"📨 ✅成功识别提取确认提交邮件")

        # 使用正则表达式提取所需信息
        application_number_match = re.search(r"(?i)application (?:id|ID) (?:is:)?\s*([A-Z0-9]+)", text)   # 提取申请编号
        if not application_number_match:
            application_number_match = re.search(r"(?i)your application ID is:\s*([A-Z0-9]+)", text)
            logger.debug("使用备选正则表达式匹配申请编号")  
         # 优先从 "Visa applicant:" 提取姓名，备选从 "Dear" 后提取
        name_match = re.search(r"Visa applicant:\s+(.*?)\s+\.", text)
        if not name_match:
            name_match = re.search(r"Dear\s+(.*?)\s+We have received", text)
            logger.debug("使用备选正则表达式匹配姓名")
        # 提取出生日期
        dob_match = re.search(r"Date of birth:\s+(.*?)\s+\.", text)


        # 记录正则表达式匹配结果
        logger.debug(f"🔍 申请编号匹配: {application_number_match.group(0) if application_number_match else '未匹配'}")
        logger.debug(f"🔍 姓名匹配: {name_match.group(1) if name_match else '未匹配'}")
        logger.debug(f"🔍 出生日期匹配: {dob_match.group(1) if dob_match else '未匹配'}")

         # 提取查询URL (从"here"超链接)
        query_url = None
        here_link = soup.find('a', text=re.compile(r'here', re.I))
        if here_link and 'href' in here_link.attrs:
            query_url = here_link['href']
            logger.debug(f"🔍 查询URL匹配: {query_url}")
        else:
            # 尝试查找包含evisa.gov.vn/e-visa/search的链接
            search_link = soup.find('a', href=re.compile(r'evisa\.gov\.vn/e-visa/search', re.I))
            if search_link:
                query_url = search_link['href']
                logger.debug(f"🔍 查询URL匹配 (备选方法): {query_url}")
                

        if not (application_number_match and name_match and dob_match):
            logger.warning("⚠️ 无法提取 application_number / name / dob")
            return None

        # 提取出生日期并格式化为 dd/mm/yyyy
        raw_dob = dob_match.group(1).strip()
        dt = datetime.strptime(raw_dob, "%a %d %b %Y")
        dob = dt.strftime("%d/%m/%Y")
        logger.debug(f"格式化后的出生日期: {dob}")

        result = {
            "application_number": application_number_match.group(1).strip(),
            "full_name": name_match.group(1).strip(),
            "dob": dob,
            "query_url": query_url
        }
        logger.debug(f"提交确认邮件解析结果: {result}")
        return result

    except Exception as e:
        logger.exception("❌ 解析提交确认邮件失败")
        logger.debug(f"异常详情: {str(e)}")
        raise  # 重新抛出异常，让重试装饰器捕获


def parse_dob(raw: str) -> Optional[str]:
    """将 `Tue 17 Dec 1996` 转为 `17/12/1996`"""
    try:
        dt = datetime.strptime(raw.strip(), "%a %d %b %Y")
        return dt.strftime("%d/%m/%Y")
    except:
        return None
    

def parse_payment_email(content: str) -> Optional[dict]:
    """
    从付款成功邮件中提取必要字段
    - name
    - application_number
    - payment_amount
    - payment_time
    - card_number_tail
    - payment_method
    
    Args:
        content: 邮件内容
        
    """

    # 打印完整邮件内容（仅用于调试）
    #logger.info(f"📨 完整付款邮件内容:\n{content}")

   
    try:
         # 使用BeautifulSoup提取纯文本
        soup = BeautifulSoup(content, "html.parser")
        text = soup.get_text(separator=" ", strip=True)  # 提取纯文本
        #logger.info(f"📨 ✅成功识别提取付款确认邮件纯文本:\n{text}")  #调试的时候启用
        logger.info(f"📨 ✅成功识别提取付款确认邮件")
        
        application_number = re.search(r"(?i)register code[:：]?\s*([A-Z0-9\.]+)", text)  # 提取申请编号 (从register code字段)
        name_match = re.search(r"(?i)Dear Mr/Ms\.\s+([A-Z\s]+)\s+We would like", text)  # 提取申请人姓名 (从Dear Mr/Ms.后面提取到We would like之前)
        amount_match = re.search(r"(?i)amount[:：]?\s*([\d,\.]+)\s*VND", text)           # 提取付款金额
        card_match = re.search(r"Card number:\s*[\dXx\.\*]+(\d{4})", text)  # 提取卡号后四位
        method_match = re.search(r"(?i)payment method[:：]?\s*([^:]+?)(?:Important|$)", text)  # 提取付款方式 (从payment method字段)
        payment_time_match = re.search(r"Trasaction date:\s*(\d{2}:\d{2})\s+(\d{2}/\d{2}/\d{4})", text)  # 提取付款时间 (从Trasaction date字段)

        # 记录正则表达式匹配结果
        logger.info(f"🔍 申请编号匹配: {application_number.group(0) if application_number else '未匹配'}")
        logger.info(f"🔍 姓名匹配: {name_match.group(0) if name_match else '未匹配'}")
        logger.info(f"🔍 金额匹配: {amount_match.group(0) if amount_match else '未匹配'}")
        logger.info(f"🔍 卡号匹配: {card_match.group(0) if card_match else '未匹配'}")
        logger.info(f"🔍 支付方式匹配: {method_match.group(0) if method_match else '未匹配'}")
        logger.info(f"🔍 付款时间: {payment_time_match.group(0) if payment_time_match else '未匹配'}")



         # 处理付款时间
        if not payment_time_match:
            logger.error("❌ 未匹配到付款时间")
            return None
        logger.info(f"🔍 付款时间匹配: {payment_time_match.group(0)}")

        vn_time_str = f"{payment_time_match.group(2)} {payment_time_match.group(1)}"  # "15/05/2025 15:31"
        vn_time = datetime.strptime(vn_time_str, "%d/%m/%Y %H:%M")
        bj_time = vn_time + timedelta(hours=1)


        # 验证必要字段
        if not (application_number and amount_match):
            logger.warning("⚠️ 无法提取 application_number / amount")
            return None

        #dt_str = f"{date_match.group(1)} {date_match.group(2)}"
        #dt = datetime.strptime(dt_str, "%H:%M %d/%m/%Y")

        
        result = {
            "application_number": application_number.group(1).strip(),
            "full_name": name_match.group(1).strip() if name_match else "",
            "payment_amount": float(amount_match.group(1).replace(",", "")),
            "payment_time": bj_time.strftime("%Y-%m-%d %H:%M:%S"),
            "card_number_tail": card_match.group(1).strip() if card_match else "",
            "payment_method": method_match.group(1).strip() if method_match else ""
        }
        
        logger.info(f"解析付款邮件结果: {result}")
        return result
        

    except Exception as e:
        logger.exception("❌ 解析付款邮件失败")
        return None
    


def parse_result_email(content: str) -> Optional[dict]:
    """解析出签成功通知邮件，提取申请编号、下载链接"""
    try:
        # 使用BeautifulSoup提取纯文本
        soup = BeautifulSoup(content, "html.parser")
        text = soup.get_text(separator=" ", strip=True)  # 提取纯文本
        #logger.info(f"📨 ✅成功识别提取出签通知邮件，原文:\n{text}") #调试的时候启用
        logger.info(f"📨 ✅成功识别提取出签通知邮件")

        applicantion_number_match = re.search(r"e-visa application number\s*([A-Z0-9]+)", text, re.IGNORECASE)
        download_url = re.search(r"https://evisa\.gov\.vn/e-visa/search\?[^\"'\s]+", content)  # 链接在原始HTML中查找
        # 记录正则表达式匹配结果
        logger.info(f"🔍 申请编号匹配: {applicantion_number_match.group(0) if applicantion_number_match else '未匹配'}")
        logger.info(f"🔍 下载链接匹配: {download_url.group(0) if download_url else '未匹配'}")

        if not applicantion_number_match or not download_url:
            logger.warning("⚠️ 无法提取 application_number / download_url")
            return None

        return {
            "application_number": applicantion_number_match.group(1),
            "download_url": download_url.group(0)
        }

    except Exception as e:
        logger.exception("❌ 解析出签通知邮件失败")
        return None
