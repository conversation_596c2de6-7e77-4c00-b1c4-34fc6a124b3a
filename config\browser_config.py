"""
浏览器配置模块 - 统一配置入口
"""
from pathlib import Path
import os
from typing import Dict, Any, Optional, Tuple
from playwright.sync_api import Page, <PERSON><PERSON><PERSON>, <PERSON>rowserContex<PERSON>, Playwright
from app.utils.logger_config import get_logger

logger = get_logger()

# 项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent

# 默认配置
DEFAULT_BROWSER_CONFIG = {
    "browser_type": "chromium",  # 使用Playwright内置的chromium
    "headless": True,            # 是否使用无头模式
    "slow_mo": 0,                # 减慢自动化操作的速度(毫秒)
    "viewport_width": 1920,      # 视口宽度
    "viewport_height": 1080,     # 视口高度
    "page_zoom": 0.8,            # 页面缩放比例
    "timeout_ms": 300000,        # 操作超时时间(毫秒)
    "screenshots_dir": "screenshots",  # 截图保存目录
}

# 全局配置实例
_browser_config = None

def get_browser_config() -> Dict[str, Any]:
    """
    获取浏览器配置 - 统一入口
    
    Returns:
        Dict[str, Any]: 浏览器配置
    """
    global _browser_config
    
    # 如果已经加载过配置，直接返回
    if _browser_config is not None:
        return _browser_config
    
    # 创建配置字典
    browser_config = DEFAULT_BROWSER_CONFIG.copy()
    
    # 从环境变量加载配置
    browser_config["browser_type"] = os.environ.get("BROWSER", browser_config["browser_type"])
    browser_config["headless"] = os.environ.get("HEADLESS", "true").lower() == "true"
    browser_config["slow_mo"] = int(os.environ.get("SLOW_MO", str(browser_config["slow_mo"])))
    browser_config["viewport_width"] = int(os.environ.get("VIEWPORT_WIDTH", str(browser_config["viewport_width"])))
    browser_config["viewport_height"] = int(os.environ.get("VIEWPORT_HEIGHT", str(browser_config["viewport_height"])))
    browser_config["page_zoom"] = float(os.environ.get("PAGE_ZOOM", str(browser_config["page_zoom"])))
    browser_config["timeout_ms"] = int(os.environ.get("TIMEOUT_MS", str(browser_config["timeout_ms"])))
    
    # 保存配置
    _browser_config = browser_config
    
    logger.info(f"已加载浏览器配置: 类型={browser_config['browser_type']}, 无头模式={browser_config['headless']}")
    
    # 返回配置
    return _browser_config

def launch_form_browser(playwright: Playwright, custom_config: Optional[Dict[str, Any]] = None) -> Tuple[Browser, BrowserContext, Page]:
    """
    启动浏览器
    
    Args:
        playwright: Playwright实例
        custom_config: 自定义配置，会覆盖全局配置
        
    Returns:
        Tuple[Browser, BrowserContext, Page]: 浏览器、上下文和页面实例
    """
    # 加载配置
    config = get_browser_config()
    
    # 应用自定义配置
    if custom_config:
        for key, value in custom_config.items():
            config[key] = value
    
    # 浏览器启动选项
    browser_options = {
        'headless': config['headless']
    }
    
    # 添加slow_mo
    if isinstance(config['slow_mo'], (int, float)) and config['slow_mo'] > 0:
        browser_options['slow_mo'] = config['slow_mo']
        logger.info(f"设置slow_mo: {config['slow_mo']}ms")
    
    # 启动浏览器 - 固定使用chromium
    logger.info(f"启动 chromium 浏览器")
    browser = playwright.chromium.launch(**browser_options)
    logger.info(f"已启动 chromium 浏览器")
    
    # 创建上下文选项
    context_options = {
        'viewport': {
            'width': config.get('viewport_width', 1920),
            'height': config.get('viewport_height', 1080)
        }
    }
    
    # 创建上下文和页面
    context = browser.new_context(**context_options)
    page = context.new_page()
    
    # 设置页面缩放（如果需要）
    if config.get('page_zoom'):
        page.evaluate(f"document.body.style.zoom = '{config['page_zoom']}'")
    
    logger.info("浏览器、上下文和页面已创建")
    return browser, context, page


def close_browser(browser: Browser, context: BrowserContext = None, page: Page = None):
    """
    关闭浏览器、上下文和页面
    
    Args:
        browser: 浏览器实例
        context: 上下文实例
        page: 页面实例
    """
    try:
        if page and not page.is_closed():
            page.close()
            logger.debug("页面已关闭")
        
        if context:
            context.close()
            logger.debug("上下文已关闭")
        
        if browser and browser.is_connected():
            browser.close()
            logger.debug("浏览器已关闭")
        
        logger.info("浏览器资源已清理")
    except Exception as e:
        logger.warning(f"关闭浏览器资源时发生错误: {e}")
