# api_main.py
import asyncio
import base64
import os
import tempfile
import uuid
import uvicorn
import time
from pathlib import Path
from typing import Optional, Dict, Any, List # 增加了 List
from dataclasses import dataclass
from fastapi import FastAPI, HTTPException, Body, Depends, File, Form, UploadFile,status,Request,Response
from pydantic import BaseModel, Field, EmailStr, validator # EmailStr 和 root_validator
from starlette.concurrency import run_in_threadpool
from dotenv import load_dotenv
from fastapi.staticfiles import StaticFiles
from fastapi.responses import RedirectResponse,JSONResponse,HTMLResponse
from contextlib import asynccontextmanager  # 用于定义异步上下文管理器
from fastapi.templating import Jinja2Templates
from fastapi_login import LoginManager
from passlib.context import CryptContext
from datetime import datetime, timedelta

# --- 全局变量和应用实例 ---
app = FastAPI(
    title="Vietnam E-Visa Automator API",
    description="API to automate Vietnam E-Visa application filling using <PERSON>wright.",
    version="1.0.0" # 定为1.0.0
)

# --- 初始化 ---
load_dotenv()

# 定义User模型
class User(BaseModel):
    username: str
    password: str
    locked_until: Optional[datetime] = None
    failed_attempts: int = 0

# 获取当前文件的绝对路径
current_dir = Path(__file__).resolve().parent
static_dir = os.path.join(current_dir, "static")

# 确保目录存在
os.makedirs(static_dir, exist_ok=True)

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory=static_dir), name="static")

# 配置Jinja2模板
templates = Jinja2Templates(directory="templates")

# 根路径路由
@app.get("/")
async def root(request: Request):
    """
    根路径重定向到登录或表单页面
    """
    if not request.cookies.get("auth_token"):
        return RedirectResponse(url="/login", status_code=302)
    return templates.TemplateResponse("test_form.html", {"request": request})

#配置基础设置
SECRET = os.getenv("SECRET_KEY", "your-very-secret-key")
manager = LoginManager(SECRET, token_url='/login', use_cookie=True)
manager.cookie_name = "auth_token"

# 定义User模型
class User(BaseModel):
    username: str
    password: str
    locked_until: Optional[datetime] = None
    failed_attempts: int = 0

# 注册 user_loader 回调函数
@manager.user_loader
def load_user(user_id: str) -> Optional[User]:
    user_data = users.get(user_id)
    if user_data:
        return User(
            username=user_id,
            password=user_data["password"],
            locked_until=user_data.get("locked_until"),
            failed_attempts=user_data.get("failed_attempts", 0)
        )
    return None

# 简单的密码验证
def verify_password(plain_password: str, stored_password: str) -> bool:
    # 直接进行字符串比较
    return plain_password == stored_password

# 从环境变量加载用户数据
users = {}

# 从环境变量获取用户列表和密码列表
api_users = os.getenv("API_BASIC_USERS", "").split(",")
api_passwords = os.getenv("API_BASIC_PASSWORDS", "").split(",")

# 确保用户和密码数量匹配
if len(api_users) != len(api_passwords):
    raise ValueError("Number of users and passwords must match")

# 初始化用户数据
for username, password in zip(api_users, api_passwords):
    username = username.strip()
    password = password.strip()
    if username and password:  # 只添加非空的用户
        users[username] = {
            "password": password,
            "failed_attempts": 0,
            "locked_until": None
        }
        print(f"Initialized user: {username}")



#异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    if exc.status_code == 401:
        return RedirectResponse(url="/login")
    elif exc.status_code == 403:
        return RedirectResponse(url="/login?error=Account locked due to too many failed attempts")
    return JSONResponse(status_code=exc.status_code, content={"detail": exc.detail})

# 移除重复的路由定义
def login_page(request: Request):
    error = request.query_params.get("error")
    return templates.TemplateResponse("login.html", {"request": request, "error": error})

# 登录处理路由（POST）
@app.post("/login")
async def login(response: Response, request: Request, username: str = Form(...), password: str = Form(...)):
    try:
        print(f"Login attempt for username: {username}")
        user = users.get(username)
        if not user:
            print(f"User {username} not found in database")
            return RedirectResponse(url="/login?error=Invalid%20credentials", status_code=302)

        print(f"Stored password: {user['password']}")
        if not verify_password(password, user["password"]):
            print(f"Password verification failed for user {username}")
            user["failed_attempts"] = user.get("failed_attempts", 0) + 1
            if user["failed_attempts"] >= 10:
                user["locked_until"] = datetime.now() + timedelta(hours=100000)
                return RedirectResponse(url="/login?error=Account%20locked", status_code=302)
            return RedirectResponse(url="/login?error=Invalid%20credentials", status_code=302)

        print(f"Login successful for user {username}")
        # 登录成功，重置失败次数
        user["failed_attempts"] = 0
        user["locked_until"] = None

        # 设置会话
        response = templates.TemplateResponse("test_form.html", {"request": request})
        response.set_cookie(
            key="auth_token",
            value=username,
            httponly=True,
            samesite="lax",
            max_age=3600,  # 1小时过期
            expires=3600   # 1小时过期
        )
        return response
    except Exception as e:
        print(f"Error in login handler: {str(e)}")
        raise

# --- 项目模块导入 ---
# 确保这些导入路径相对于你的项目结构和运行方式是正确的
try:
    from app.utils.logger_config import setup_logger, get_logger
    from app.data.models import Applicant as ProjectApplicant
    from app.core.simple_engine import SimpleEngine
    from db.db import init_db, DB_PATH, SCHEMA_PATH
    from app.utils.ocr_utils import run_aliyun_passport_ocr,normalize_ocr_result_for_applicant
    from app.email.email_runner import start_polling_scheduler as initialize_email_scheduler
    # 注意: main.py 中的 SimpleApplicant 用于邮件调度器初始化，这里导入它
    # 为了避免潜在的循环导入或模块名冲突，最好将 SimpleApplicant 移到更通用的位置
    # 但基于你现有的结构，我们暂时从 main 导入
    try:
        from main import SimpleApplicant
    except ImportError: # 如果 main.py 中没有或导入出错，提供备用
        logger_temp_init = get_logger() # 修改：移除参数
        logger_temp_init.warning("Could not import SimpleApplicant from main.py. Using a placeholder for email scheduler.")
        @dataclass # 需要 from dataclasses import dataclass
        class SimpleApplicant:
            email: str
            config: dict
            passport_number: Optional[str] = None # main.py 中的定义

    from app.utils.env_loader import load_email_accounts_from_env
except ImportError as e:
    print(f"CRITICAL: Failed to import project modules: {e}")
    print("Please ensure that api_main.py is in the project root or your PYTHONPATH is set correctly.")
    raise # 启动时关键依赖缺失，直接抛出异常


# 设置日志
try:
    # 你可以根据需要在你的 logger_config.py 中调整 setup_logger
    # 例如，让它能接收日志文件名
    setup_logger(
        log_dir="logs/fastapi",  # 例如，为FastAPI的日志创建一个子目录
        console_level="DEBUG",
        file_level="INFO",
        log_filename_prefix="vietnam_evisa_api" # 清晰的前缀
    )
    logger = get_logger()  # 获取配置好的 loguru logger
    logger.info("✅ Loguru logger (via setup_logger with specific prefix and dir) initialized for FastAPI")
except Exception as e:
    # 备用日志方案，确保即使日志配置失败，应用也能基本记录
    print(f"Error setting up logger via setup_logger: {e}. Using basic print for logging.")
    class PrintLogger:
        def info(self, msg): print(f"INFO [{__name__}]: {msg}")
        def debug(self, msg): print(f"DEBUG [{__name__}]: {msg}")
        def warning(self, msg): print(f"WARNING [{__name__}]: {msg}")
        def error(self, msg, exc_info=False):
            print(f"ERROR [{__name__}]: {msg}")
            if exc_info and isinstance(exc_info, BaseException): print(str(exc_info))
        def critical(self, msg, exc_info=False):
            print(f"CRITICAL [{__name__}]: {msg}")
            if exc_info and isinstance(exc_info, BaseException): print(str(exc_info))
    logger = PrintLogger()

logger.info("FastAPI application is initializing...")



# 实例化 SimpleEngine
# 它的构造函数应该会加载所有必要的配置 (settings.yaml, locators)
try:
    logger.info("Initializing SimpleEngine...")
    engine = SimpleEngine()
    logger.info("✅ SimpleEngine initialized successfully.")
except Exception as e:
    logger.critical(f"❌ CRITICAL: Failed to initialize SimpleEngine: {e}", exc_info=True)
    engine = None # 标记引擎初始化失败

# --- Pydantic 模型 ---
class BaseResponse(BaseModel):
    status: str
    message: str

class SuccessResponse(BaseResponse):
    status: str = "success"
    data: Optional[Dict[str, Any]] = None

class ErrorResponse(BaseResponse):
    status: str = "error"
    detail: Optional[str] = None

class VisaApplicationResponseData(BaseModel):
    tracking_id: str # 例如 passport_number
    evisa_application_number: Optional[str] = None # 越南官方的 E-XXXX 申请号
    submission_status: str # 例如 "AUTOMATION_INITIATED", "AUTOMATION_FAILED_PRE_SUBMIT"
    payment_status: Optional[str] = None
    message: str



# 新模型，只管文本字段
class VisaFormTextFields(BaseModel):
    customer_source: Optional[str] = Form(None, example="WebAppAPI")
    surname: Optional[str] = Form(None, example="DOE")
    given_name: Optional[str] = Form(None, example="JOHN")
    chinese_name: Optional[str] = Form(None, example="约翰多伊")
    sex: str = Form(example="MALE")     # 默认值
    dob: Optional[str] = Form(None, pattern=r"^(?:|\d{2}/\d{2}/\d{4})$", description="DD/MM/YYYY")
    place_of_birth: Optional[str] = Form(None, description="Pinyin, uppercase")
    nationality: Optional[str] = Form("CHINA") # 默认值
    religion: Optional[str] = Form("NO")

    passport_number: Optional[str] = Form(None)
    passport_type: Optional[str] = Form("Ordinary passport")
    place_of_issue: Optional[str] = Form(None)
    date_of_issue: Optional[str] = Form(None, pattern=r"^(?:|\d{2}/\d{2}/\d{4})$", description="DD/MM/YYYY")
    passport_expiry: Optional[str] = Form(None, pattern=r"^(?:|\d{2}/\d{2}/\d{4})$", description="DD/MM/YYYY")

    email: EmailStr = Form(description="Applicant's email address")
    telephone_number: str = Form(description="Applicant's telephone number")

    permanent_address: Optional[str] = Form(None)
    contact_address: Optional[str] = Form(None)
    emergency_contact_name: Optional[str] = Form(None)
    emergency_address: Optional[str] = Form(None)
    emergency_contact_phone: Optional[str] = Form(None)

    visa_entry_type: str = Form(description="Requested visa entry type")
    visa_validity_duration: str = Form(description="Requested visa validity")
    visa_start_date: str = Form(pattern=r"^\d{2}/\d{2}/\d{4}$", description="Requested visa start date DD/MM/YYYY")
    intended_entry_gate: str = Form(description="Intended port of entry")

    visited_vietnam_last_year: bool = Form(False)
    previous_entry_date: Optional[str] = Form(None, pattern=r"^(?:|\d{2}/\d{2}/\d{4})$")
    previous_exit_date: Optional[str] = Form(None, pattern=r"^(?:|\d{2}/\d{2}/\d{4})$") # 根据你的 HTML 添加
    previous_purpose: Optional[str] = Form(None)

    @validator('*', pre=True, always=True)
    def empty_str_as_none(cls, v: Any):
        if isinstance(v, str) and v.strip() == "": # 修改为检查strip()后的空字符串
            return None
        return v
    class Config:
        str_strip_whitespace = True

# --- 应用生命周期事件 ---
@app.on_event("startup")
async def startup_event_handler():
    logger.info("🚀 FastAPI application 'startup' event triggered.")

    if engine is None:
        logger.critical("❌ SimpleEngine failed to initialize during module load. API calls will likely fail.")
        # 应用将继续启动，但 /apply-visa/ 接口会返回 503
        return

    # 1. 初始化数据库 (确保父目录存在)
    logger.info("⚙️ Initializing database...")
    try:
        db_parent_dir = DB_PATH.parent
        if not db_parent_dir.exists():
            logger.info(f"Database directory {db_parent_dir} does not exist, creating it.")
            db_parent_dir.mkdir(parents=True, exist_ok=True)

        if not SCHEMA_PATH.exists():
             logger.error(f"❌ Database schema file not found at {SCHEMA_PATH}. Cannot initialize database tables if DB file is new.")
        # init_db() 会处理数据库文件是否存在以及表是否需要创建
        init_db()
        logger.info("✅ Database initialized (tables created if they didn't exist).")
    except Exception as e:
        logger.critical(f"❌ Failed to initialize database during startup: {e}", exc_info=True)
        # 此处失败可能导致后续操作也失败，根据严重性决定是否中止

    # 2. 初始化并启动邮件轮询调度器
    logger.info("⚙️ Initializing email polling scheduler...")
    try:
        loaded_env_config = load_email_accounts_from_env() # 应该只调用一次
        email_accounts_map = loaded_env_config.get("email_accounts", {})
        # logger.debug(f"Email accounts loaded from env for scheduler: {list(email_accounts_map.keys())}")

        applicants_for_scheduler: List[SimpleApplicant] = []
        if email_accounts_map:
            for email_user, config_dict in email_accounts_map.items():
                if config_dict and isinstance(config_dict, dict) and "user" in config_dict and config_dict["user"] == email_user:
                    applicant = SimpleApplicant(email=email_user, config=config_dict)
                    applicants_for_scheduler.append(applicant)
                else:
                    logger.warning(f"⚠️ Skipping invalid email config for scheduler: user='{email_user}', config='{config_dict}'")
        else:
            logger.warning("⚠️ No email accounts found in environment for scheduler.")

        if applicants_for_scheduler:
            logger.info(f"Preparing to initialize email scheduler for {len(applicants_for_scheduler)} accounts.")
            initialize_email_scheduler(applicants_for_scheduler) # from app.email.email_runner
            logger.info("✅ Email polling scheduler initialized and started.")
        else:
            logger.warning("⚠️ No valid email accounts to schedule. Email scheduler for predefined accounts will not run.")
    except Exception as e:
        logger.critical(f"❌ Failed to initialize email scheduler during startup: {e}", exc_info=True)

    logger.info("🎉 FastAPI application startup sequence complete.")

@app.on_event("shutdown")
async def shutdown_event_handler():
    logger.info("🔌 FastAPI application 'shutdown' event triggered.")
    # 可以在这里添加清理逻辑，例如确保APScheduler优雅关闭
    # (虽然 atexit 已经注册了，但显式关闭更保险)
    try:
        from app.email.email_runner import scheduler, email_thread_pool
        if scheduler.running:
            logger.info("Shutting down APScheduler...")
            scheduler.shutdown(wait=True) # wait=True 等待任务完成
            logger.info("APScheduler shut down.")
        logger.info("Shutting down email thread pool...")
        email_thread_pool.shutdown(wait=True) # wait=True 等待现有任务完成
        logger.info("Email thread pool shut down.")
    except Exception as e:
        logger.warning(f"⚠️ Error during scheduled shutdown of email components: {e}", exc_info=True)
    logger.info("👋 FastAPI application shutdown sequence complete.")

def _cleanup_temp_files(files: List[Optional[Path]]):
    cleaned_count = 0
    for f_path in files:
        if f_path and f_path.exists():
            try:
                f_path.unlink()
                logger.debug(f"🗑️ Cleaned up temporary file: {f_path}")
                cleaned_count += 1
            except Exception as e:
                logger.warning(f"⚠️ Failed to clean up temporary file {f_path}: {e}")
    if cleaned_count > 0:
        logger.info(f"Temporary file cleanup: {cleaned_count} files removed.")

# --- 新增API 端点 只做护照OCR识别，返回识别到的字段（JSON），不做填表。 ---
@app.post("/ocr-passport/", tags=["OCR"])
async def ocr_passport_endpoint(passport_scan: UploadFile = File(...)):

    import tempfile
    from pathlib import Path

    temp_passport_path = Path(tempfile.mktemp(prefix="passport_ocr_", suffix=".jpg"))
    try:
        with open(temp_passport_path, "wb") as temp_file:
            content = await passport_scan.read()
            temp_file.write(content)
        ocr_result = run_aliyun_passport_ocr(str(temp_passport_path))
        fields = normalize_ocr_result_for_applicant(ocr_result) if ocr_result else {}
        return JSONResponse(content=fields)
    finally:
        if temp_passport_path.exists():
            temp_passport_path.unlink()


# 登录页面路由
@app.get("/login")
async def login_page(request: Request):
    """
    登录页面路由
    """
    error = request.query_params.get("error", "")
    return templates.TemplateResponse("login.html", {"request": request, "error": error})

# --- API 端点 ---
@app.post(
    "/apply-visa-form/",
    response_model=SuccessResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Client Error (e.g., automation logic failure)"},
        422: {"model": ErrorResponse, "description": "Validation Error (input data invalid)"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"},
        503: {"model": ErrorResponse, "description": "Service Temporarily Unavailable"}
    },
    tags=["Visa Automation"],
    summary="Submit a new E-Visa application with file uploads using multipart/form-data."
)
async def submit_visa_application_form_endpoint(
    # --- File Uploads ---
    portrait_photo: UploadFile = File(..., description="Portrait photo (JPG/PNG)"),
    passport_scan: UploadFile = File(..., description="Passport scan (JPG/PNG)"),
     # --- Personal Information ---
    customer_source: Optional[str] = Form(None, example="WebAppAPI"),
    surname: Optional[str] = Form(None, example="DOE"),
    given_name: Optional[str] = Form(None, example="JOHN"),
    chinese_name: Optional[str] = Form(None, example="约翰多伊"),
    sex: str = Form(..., example="MALE"),
    dob: Optional[str] = Form(None, pattern=r"^(?:|\d{2}/\d{2}/\d{4})$", description="DD/MM/YYYY"), # pattern允许空或正确格式
    place_of_birth: Optional[str] = Form(None, description="Pinyin, uppercase"),
    nationality: Optional[str] = Form("CHINA"), # HTML有value="CHINA"，Form可以有default
    religion: Optional[str] = Form("NO"),      # HTML有value="NO"

    # --- Passport Information ---
    passport_number: Optional[str] = Form(None),
    passport_type: Optional[str] = Form("Ordinary passport"), # HTML有value
    place_of_issue: Optional[str] = Form(None),
    date_of_issue: Optional[str] = Form(None, pattern=r"^(\d{2}/\d{2}/\d{4}|\d{8})$", description="DD/MM/YYYY or YYYYMMDD"),
    passport_expiry: Optional[str] = Form(None, pattern=r"^(\d{2}/\d{2}/\d{4}|\d{8})$", description="DD/MM/YYYY or YYYYMMDD"),

    # --- Contact Information ---
    email: str = Form(..., example="<EMAIL>"),
    telephone_number: str = Form(..., example="13800138000"),
    permanent_address: Optional[str] = Form(None, example="123 Permament St, Permacity"),
    contact_address: Optional[str] = Form(None, example="456 Contact St, Contactville"),

    # --- Emergency Contact ---
    emergency_contact_name: Optional[str] = Form(None, example="Jane Doe"),
    emergency_address: Optional[str] = Form(None, example="789 Emergency Rd, Emergcity"),
    emergency_contact_phone: Optional[str] = Form(None, example="+15551234567"),

    # --- Visa Request Details ---
    visa_entry_type: str = Form(..., example="Single-entry"),
    visa_validity_duration: str = Form(..., example="30天"),
    visa_start_date: str = Form(..., example="01/01/2025", pattern=r"^\d{2}/\d{2}/\d{4}$", description="DD/MM/YYYY"),
    intended_entry_gate: str = Form(..., example="Tan Son Nhat Int Airport (Ho Chi Minh City)"),

    # --- Previous Visit Information ---
    visited_vietnam_last_year: bool = Form(False),
    previous_entry_date: Optional[str] = Form(None, example="10/02/2023", pattern=r"^(?:|\d{2}/\d{2}/\d{4})$"),
    previous_exit_date: Optional[str] = Form(None, example="20/02/2023", pattern=r"^(?:|\d{2}/\d{2}/\d{4})$"),
    previous_purpose: Optional[str] = Form(None, example="Tourism")

):
    """
    Receives E-Visa application data and files via multipart/form-data, validates it, and initiates the
    automated Playwright process in a background thread.
    """
    request_id = str(uuid.uuid4())
    logger.info(f"⚡ [{request_id}] Received /apply-visa-form/ request for passport: {passport_number},{surname},{given_name},{chinese_name}")

    if engine is None:
        logger.error(f"[{request_id}] SimpleEngine not initialized. Automation service unavailable.")
        raise HTTPException(status_code=503, detail="Automation engine is not ready. Please try again later.")

    temp_photo_path: Optional[Path] = None
    temp_passport_path: Optional[Path] = None
    created_temp_files: List[Optional[Path]] = []

    try:
        # 1. 处理上传的文件
        logger.debug(f"[{request_id}] Processing uploaded files...")

        # 处理头像照片
        if portrait_photo:
            temp_photo_path = Path(tempfile.mktemp(prefix="portrait_api_", suffix=".jpg"))
            with open(temp_photo_path, "wb") as temp_file:
                content = await portrait_photo.read()
                temp_file.write(content)
            if temp_photo_path:
                created_temp_files.append(temp_photo_path)
                logger.info(f"[{request_id}] Portrait photo saved to {temp_photo_path}")

        # 处理护照扫描
        if passport_scan:
            temp_passport_path = Path(tempfile.mktemp(prefix="passport_api_", suffix=".jpg"))
            with open(temp_passport_path, "wb") as temp_file:
                content = await passport_scan.read()
                temp_file.write(content)
            if temp_passport_path:
                created_temp_files.append(temp_passport_path)
                logger.info(f"[{request_id}] Passport scan saved to {temp_passport_path}")

            else: raise HTTPException(status_code=422, detail="Passport scan is required.")

        # OCR护照
        ocr_result = run_aliyun_passport_ocr(str(temp_passport_path))
        logger.info(f"[{request_id}] OCR result: {ocr_result}")



        # 3. 用OCR识别结果和表单字段组装Applicant
        ocr_fields = normalize_ocr_result_for_applicant(ocr_result) if ocr_result else {}

        logger.debug(f"[{request_id}] Creating ProjectApplicant instance...")
        applicant_args = {
            "customer_source": customer_source or ocr_fields.get("customer_source"),
            "surname": surname or ocr_fields.get("surname"),
            "given_name": given_name or ocr_fields.get("given_name"),
            "chinese_name": chinese_name or ocr_fields.get("chinese_name"),
            "sex": sex or ocr_fields.get("sex"),
            "dob": dob or ocr_fields.get("dob"),
            "place_of_birth": place_of_birth or ocr_fields.get("place_of_birth"),
            "nationality": nationality or ocr_fields.get("nationality"),
            "religion": religion,
            "passport_number": passport_number or ocr_fields.get("passport_number"),
            "passport_type": passport_type or ocr_fields.get("passport_type"),
            "place_of_issue": place_of_issue or ocr_fields.get("place_of_issue"),
            "date_of_issue": date_of_issue or ocr_fields.get("date_of_issue"),
            "passport_expiry": passport_expiry or ocr_fields.get("passport_expiry"),
            "email": email,
            "telephone_number": telephone_number,
            "permanent_address": permanent_address,
            "contact_address": contact_address,
            "emergency_contact_name": emergency_contact_name,
            "emergency_address": emergency_address,
            "emergency_contact_phone": emergency_contact_phone,
            "visa_entry_type": visa_entry_type,
            "visa_validity_duration": visa_validity_duration,
            "visa_start_date": visa_start_date,
            "intended_entry_gate": intended_entry_gate,
            "visited_vietnam_last_year": visited_vietnam_last_year,
            "previous_entry_date": previous_entry_date,
            "previous_exit_date": previous_exit_date,
            "previous_purpose": previous_purpose,
            "portrait_photo_path": temp_photo_path,
            "passport_scan_path": temp_passport_path
        }

        # 将所有空字符串（包括只包含空格的字符串）转为 None
        for k, v in applicant_args.items():
            if isinstance(v, str) and v.strip() == "":
                applicant_args[k] = None

        internal_applicant = ProjectApplicant(**applicant_args)

        # 3. 在线程池中异步执行核心自动化逻辑
        logger.info(f"[{request_id}] Submitting automation task for {internal_applicant.passport_number} to thread pool...")
        automation_succeeded: bool = await run_in_threadpool(
            engine.run_vietnam_evisa_step1, internal_applicant
        )
        logger.info(f"[{request_id}] Automation task for {internal_applicant.passport_number} completed. Overall success from engine: {automation_succeeded}")

        # 4. 根据自动化结果构造并返回 API 响应
        evisa_app_num_from_engine = getattr(internal_applicant, 'application_number', None)

        if automation_succeeded:
            logger.info(f"[{request_id}] ✅ Automation process SUCCEEDED for {internal_applicant.passport_number}. Official E-Visa App No (if extracted): {evisa_app_num_from_engine}")
            response_data_obj = VisaApplicationResponseData(
                tracking_id=internal_applicant.passport_number,
                submission_status="AUTOMATION_SUCCESSFUL_PAYMENT_INITIATED",
                evisa_application_number=evisa_app_num_from_engine,
                message="Application data submitted and automation process (fill & pay steps) initiated successfully. Monitor email for further updates."
            )
            return SuccessResponse(
                message="Visa application processed.",
                data=response_data_obj.dict(exclude_none=True)
            )
        else:
            # 自动化失败
            logger.error(f"[{request_id}] ❌ Automation process FAILED for {internal_applicant.passport_number}")
            error_detail_msg = "Automation process failed at fill/pay stage. Please check logs for details."
            raise HTTPException(
                status_code=400,
                detail=error_detail_msg
            )

    except HTTPException:
        logger.warning(f"[{request_id}] Re-raising known HTTPException.")
        raise
    except Exception as e:
        logger.critical(f"[{request_id}] 💥 UNHANDLED EXCEPTION in /apply-visa-form/ for passport {internal_applicant.passport_number}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An unexpected internal server error occurred. Request ID: {request_id}. Error: {str(e)}")
    finally:
        # 清理所有创建的临时文件
        logger.debug(f"[{request_id}] Cleaning up temporary files...")
        _cleanup_temp_files(created_temp_files)
        logger.info(f"[{request_id}] Request processing for /apply-visa-form/ finished.")

@app.get("/apply-visa-form/", tags=["Visa Automation"])
async def get_visa_form():
    """重定向到表单页面，而不是显示404错误"""
    return RedirectResponse(url="/form")

@app.get("/health", response_model=SuccessResponse, tags=["System"], summary="Health check for the API service.")
async def health_check_endpoint():
    """
    Provides a simple health check for the service.
    Indicates if the core components (like the automation engine) are initialized.
    """
    if engine is None:
        logger.warning("/health endpoint: Automation engine not initialized.")
        # return ErrorResponse(status="error", message="Service is unhealthy: Automation engine not ready.") # 如果希望非200
        raise HTTPException(status_code=503, detail="Service is unhealthy: Automation engine not ready.")
    # 你可以在这里添加更多检查，例如数据库连接状态，APScheduler 状态等
    logger.info("/health endpoint: Service is healthy.")
    return SuccessResponse(message="API service is operational and healthy.")

@app.get("/test", tags=["System"])
async def test_endpoint():
    """简单的测试端点"""
    return {"message": "API is working correctly"}

# --- Uvicorn 启动 (仅当此文件作为主脚本运行时) ---
if __name__ == "__main__":
    # 这是用于本地开发的启动方式
    # 生产环境通常使用 Gunicorn + Uvicorn workers, 或者其他 ASGI 服务器
    import uvicorn
    logger.info("Starting Uvicorn server for development via 'if __name__ == \"__main__\"':")
    logger.info("Access API docs at http://127.0.0.1:8000/docs or http://localhost:8000/docs")
    uvicorn.run(
        "api_main:app",   # 指向 FastAPI app 实例 (文件名:实例名)
        host="0.0.0.0",   # 监听所有可用网络接口
        port=8000,        # 监听端口
        reload=True,      # 开发模式：代码更改时自动重载
        log_level="info"  # Uvicorn 自身的日志级别
    )

