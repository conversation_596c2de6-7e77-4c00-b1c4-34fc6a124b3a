# 文件: app/data/models.py (Improved Readability)
from dataclasses import dataclass
from typing import Optional

@dataclass
class Applicant:
    """Represents the data required for the Vietnam E-Visa application."""

    # --- Personal Information ---
    customer_source: Optional[str] = None         #customer source, for record only
    surname: Optional[str] = None                 # Applicant's surname
    given_name: Optional[str] = None              # Applicant's given name(s)
    chinese_name: Optional[str] = None            # Chinese name (only for UI display)
    sex: Optional[str] = None                     # e.g., "MALE", "FEMALE" (as extracted/input)
    dob: Optional[str] = None                     # Date of Birth, format: "DD/MM/YYYY"
    place_of_birth: Optional[str] = None          # Province/Place name (e.g., "GUANGDONG") - Used for Pinyin conversion
    nationality: Optional[str] = None             # e.g., "China"
    religion: str = "NO"                          # Defaulted to "NO" as per filler logic

    # --- Passport Information ---
    passport_number: Optional[str] = None
    passport_type: str = "Ordinary passport"      # Set fixed default value
    place_of_issue: Optional[str] = None          # Province/Place name (e.g., "GUANGDONG")
    date_of_issue: Optional[str] = None           # Date of Issue, format: "DD/MM/YYYY"
    passport_expiry: Optional[str] = None         # Expiry Date, format: "DD/MM/YYYY"

    # --- Contact Information ---
    email: Optional[str] = None
    telephone_number: Optional[str] = None        # Include country code if possible, e.g., "+8613xxxxxxxxx"
    permanent_address: Optional[str] = None       # Can be auto-generated if missing
    contact_address: Optional[str] = None         # Can be auto-generated if missing

    # --- Emergency Contact ---
    emergency_contact_name: Optional[str] = None  # Full name of emergency contact
    emergency_address: Optional[str] = None       # Can be auto-generated (uses contact_address) if missing
    emergency_contact_phone: Optional[str] = None # Phone number of emergency contact
    # emergency_relationship is handled directly in filler ("Relative"), not stored here

    # --- Visa Request Details ---
    visa_entry_type: Optional[str] = None         # Expected: "Single-entry" or "Multiple-entry"
    visa_validity_duration: Optional[str] = None  # Expected: "30天" or "90天" (from UI)
    visa_start_date: Optional[str] = None         # Requested visa start date, format: "DD/MM/YYYY"
    intended_entry_gate: Optional[str] = None     # Mapped English airport name from ENTRY_GATE_MAP
    # purpose_of_entry handled directly in filler ("Tourist"), not stored here

    # --- Previous Visit Information ---
    visited_vietnam_last_year: Optional[bool] = None # True if visited in the last year, False otherwise
    previous_entry_date: Optional[str] = None     # Format: "DD/MM/YYYY" (Required if visited_vietnam_last_year is True)
    previous_exit_date: Optional[str] = None      # Format: "DD/MM/YYYY" (Required if visited_vietnam_last_year is True)
    previous_purpose: Optional[str] = None        # Expected English purpose, e.g., "Travel" (Required if visited_vietnam_last_year is True)

    # --- File Paths ---
    portrait_photo_path: Optional[str] = None     # Absolute path to the portrait photo file
    passport_scan_path: Optional[str] = None      # Absolute path to the passport scan file