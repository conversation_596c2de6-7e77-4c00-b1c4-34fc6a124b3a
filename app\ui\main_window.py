from app.utils.logger_config import get_logger
from PyQt6.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QTabWidget, QStatusBar, QApplication
from app.ui.tabs.vietnam_tab import VietnamVisaTab


# 导入其他 Tab 页的类（如果未来添加）

logger = get_logger()

class MainWindow(QMainWindow):
    """主程序窗口"""
    def __init__(self):
        super().__init__()

        # --- 基本窗口设置 ---
        self.setWindowTitle("自动化签证系统")
        # 获取屏幕大小，设置窗口为屏幕的 80% 左右
        screen = QApplication.primaryScreen().availableGeometry()
        self.setGeometry(int(screen.width() * 0.1), int(screen.height() * 0.1),
                         int(screen.width() * 0.5), int(screen.height() * 0.5))


        # --- 中心部件和布局 ---
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.layout = QVBoxLayout(self.central_widget)

        # --- 标签页容器 ---
        self.tab_widget = QTabWidget()
        self.layout.addWidget(self.tab_widget)

        # --- 添加越南标签页 ---
        # V1 直接添加，不检查配置是否存在，让 Tab 页内部处理
        self.vietnam_tab = VietnamVisaTab() # 创建越南 Tab 实例
        self.tab_widget.addTab(self.vietnam_tab, "越南电子签证 (Vietnam E-Visa)") # 添加一个国旗 emoji
        logger.info("越南电子签标签页已添加。")

        # --- 状态栏 ---
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("就绪。请在标签页中填写信息并开始。")

        self.setMinimumSize(500, 860)  # 宽, 高（单位：像素）
        self.setMaximumWidth(700)


        logger.info("主窗口初始化完成。")

    def closeEvent(self, event):
        """关闭窗口前的确认 (可选)"""
        # reply = QMessageBox.question(self, '确认退出',
        #                              "确定要退出程序吗?",
        #                              QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        #                              QMessageBox.StandardButton.No)
        # if reply == QMessageBox.StandardButton.Yes:
        #     logger.info("用户确认退出。")
        #     event.accept() # 接受关闭事件
        # else:
        #     logger.info("用户取消退出。")
        #     event.ignore() # 忽略关闭事件
        # 简单 V1，直接退出
        logger.info("主窗口正在关闭...")
        event.accept()