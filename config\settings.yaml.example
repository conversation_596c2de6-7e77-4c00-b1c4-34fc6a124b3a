# 全局应用设置
browser: chromium   # 固定使用chromium
headless: true      # 设为 false 可以在运行时看到浏览器窗口
slow_mo: 0          # 减慢自动化操作的速度 (毫秒)，方便观察，调试完成后可以设为 0 或删除
vietnam_evisa_url: "https://evisa.gov.vn/" # 请务必检查并更新为最新的官方 URL！
anti_captcha_api_key: "your_api_key_here"  # 替换为你的Anti-Captcha API密钥

# 浏览器设置 - 简化版
browser_config:
  browser_type: chromium   # 固定使用chromium
  headless: true           # 设为 false 可以在运行时看到浏览器窗口
  slow_mo: 0               # 减慢自动化操作的速度 (毫秒)
  viewport_width: 1920     # 视口宽度
  viewport_height: 1080    # 视口高度
  page_zoom: 0.8           # 页面缩放比例
  timeout_ms: 300000       # 操作超时时间(毫秒)
  screenshots_dir: "screenshots"  # 截图保存目录

# 配置监听邮箱，检测提交，付款以及出签状态;可以添加多个邮箱
# 下面的邮箱配置仅供参考，实际使用时请替换为你自己的邮箱和授权码
email_accounts:
  - host: imap.example.com
    port: 993
    user: <EMAIL>
    password: "your_app_password_here" # 授权码，不是密码
    allowed_senders:             # 只有来自这些发件人的邮件才会被处理
      - "<EMAIL>"
      - "<EMAIL>"
