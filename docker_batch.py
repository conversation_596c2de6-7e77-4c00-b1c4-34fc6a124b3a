import sys, os
sys.path.append(os.path.abspath("."))
from app.utils.logger_config import setup_logger, get_logger
import yaml
from pathlib import Path
from db.db_tools import show_db_stats, verify_recent_records
from app.batch_processing.batch_applicant_importer import import_applicants_from_folder
from app.batch_processing.batch_runner import run_batch
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 设置日志级别
setup_logger(console_level="DEBUG")

# 设置控制台输出编码为 UTF-8
try:
    sys.stdout.reconfigure(encoding='utf-8')
except Exception:
    pass  # 兼容旧版本 Python

if __name__ == "__main__":
    logger = get_logger()
    logger.info("批处理程序启动...")
    
    # 显示数据库统计信息
    show_db_stats()
    verify_recent_records(5)
    
    # 从文件夹导入申请人
    folder = r"/app/test_data"
    logger.info(f"开始从文件夹导入申请人: {folder}")
    applicants = import_applicants_from_folder(folder)
    logger.info(f"✅ 共导入 {len(applicants)} 位申请人")
    
    # 运行批处理
    run_batch(applicants, max_concurrent=4, launch_window=30)
    logger.info("✅批处理任务全部完成,处理结果请查看日志文件")
