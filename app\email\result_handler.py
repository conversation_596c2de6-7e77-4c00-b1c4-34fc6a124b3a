import re
from app.utils.logger_config import get_logger
from datetime import datetime
from bs4 import BeautifulSoup
from db.db import (
    insert_email_notification,
    find_task_by_application_number,
    update_pdf_download_status
)
from app.downloader.vietnam_evisa_downloader import download_pdf_for_application
from db.db import get_applicant_by_application_number

logger = get_logger()

def extract_application_info(raw_email) -> tuple[str, str] | tuple[None, None]:
    """
    从 HTML 邮件中提取签证编号和跳转链接
    """
    logger.debug("开始从原始邮件中提取申请信息")
    try:
        content = None
        for part in raw_email.walk():
            if part.get_content_type() == "text/html":
                content = part.get_payload(decode=True).decode(errors="ignore")
                logger.debug("找到HTML内容部分")
                break

        if not content:
            logger.warning("邮件中没有找到HTML内容")
            return None, None

        soup = BeautifulSoup(content, "html.parser")
        logger.debug("成功解析HTML内容")

        # 提取跳转链接
        link_tag = soup.find("a", href=re.compile(r"evisa\.gov\.vn/.+search", re.I))
        download_url = link_tag["href"] if link_tag else None
        logger.debug(f"提取的下载URL: {download_url}")

        # 提取编号（从正文文本中搜索）
        match = re.search(r"[A-Z0-9]{20,}", soup.get_text())
        application_number = match.group(0) if match else None
        logger.debug(f"提取的申请编号: {application_number}")

        return application_number, download_url

    except Exception as e:
        logger.exception("❌ 提取出签信息失败")
        logger.debug(f"异常详情: {str(e)}")
        return None, None


def process_result_email(email_obj: dict, raw_body: str):
    """解析签证结果通知邮件，触发 PDF 下载"""
    logger.debug("开始处理出签结果邮件")
    logger.debug(f"邮件主题: {email_obj.get('subject', '无主题')}")

    app_number, download_url = extract_application_info(email_obj["raw"])
    logger.debug(f"提取的申请编号: {app_number}, 下载URL: {download_url}")

    if not app_number or not download_url:
        logger.warning("⚠️ 无法从出签通知邮件中提取编号或下载链接")
        return

    # 写入邮件记录
    insert_email_notification(
        task_id=None,
        data={
            "received_time": datetime.now(),
            "from_address": email_obj["from"],
            "subject": email_obj["subject"],
            "content_excerpt": raw_body[:200],
            "application_number": app_number,
            "email_type": "RESULT_NOTICE"
        }
    )

    # 匹配任务记录
    logger.debug(f"查找申请编号 {app_number} 对应的任务记录")
    task_id = find_task_by_application_number(app_number)
    
    if not task_id:
        logger.warning(f"⚠️ 未找到签证编号 {app_number} 对应的任务记录,但仍将尝试下载签证PDF")
        # 使用应用编号作为任务ID的替代
        task_id = app_number
    
    # 获取申请人对象并删除对应任务
    logger.debug(f"获取申请编号 {app_number} 对应的申请人信息")
    applicant = get_applicant_by_application_number(app_number)
    if applicant:
        # 使用延迟导入打破循环
        from app.email.email_runner import scheduler
        logger.debug(f"找到申请人信息: {applicant}")
        try:
            scheduler.remove_job(f"poll_email_{applicant['passport_number']}")
            logger.info(f"✅ 已完成签证出签，自动移除任务: poll_email_{applicant['passport_number']}")
        except Exception as e:
            logger.warning(f"⚠️ 删除任务失败: {str(e)}")

    # 执行下载逻辑（Playwright 调用）
    logger.debug(f"开始下载PDF文件 - 申请编号: {app_number}, URL: {download_url}")
    save_path = download_pdf_for_application(task_id, download_url)

    if save_path:
        logger.debug(f"PDF下载成功，保存路径: {save_path}")
        # 只有当task_id是整数时才更新数据库
        if isinstance(task_id, int):
            update_pdf_download_status(task_id, save_path)
            logger.info(f"✅ PDF 下载完成并记录 → task_id={task_id}")
        else:
            logger.info(f"✅ PDF 下载完成 → 路径={save_path}，但未更新数据库（无有效任务ID）")
    else:
        logger.error("❌ PDF 下载失败")
        logger.debug("下载失败，可能原因: URL无效、网络问题或服务器拒绝访问")    
