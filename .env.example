# API密钥
ANTI_CAPTCHA_API_KEY=your_anti_captcha_key_here
ALIYUN_APPCODE=your_aliyun_appcode_here

# 邮箱配置
EMAIL_HOST_1=imap.example.com
EMAIL_PORT_1=993
EMAIL_USER_1=<EMAIL>
EMAIL_PASSWORD_1=your_app_password_here
EMAIL_ALLOWED_SENDERS_1=<EMAIL>,<EMAIL>

EMAIL_HOST_2=imap.another.com
EMAIL_PORT_2=993
EMAIL_USER_2=<EMAIL>
EMAIL_PASSWORD_2=another_app_password_here
EMAIL_ALLOWED_SENDERS_2=<EMAIL>,<EMAIL>

# 浏览器配置
BROWSER=chromium
HEADLESS=true
SLOW_MO=0
VIEWPORT_WIDTH=1920
VIEWPORT_HEIGHT=1080
PAGE_ZOOM=0.8
TIMEOUT_MS=300000

# 越南电子签证URL
VIETNAM_EVISA_URL=https://evisa.gov.vn/

# 信用卡信息 - 完全匹配原始格式
CREDIT_CARD_1_TYPE=VISA
CREDIT_CARD_1_NUMBER=****************
CREDIT_CARD_1_FIRST_NAME=EXAMPLE
CREDIT_CARD_1_LAST_NAME=USER
CREDIT_CARD_1_BILLING_ADDRESS=123 Test Street
CREDIT_CARD_1_CITY=Beijing
CREDIT_CARD_1_COUNTRY=China
CREDIT_CARD_1_EXP_MONTH=12
CREDIT_CARD_1_EXP_YEAR=2030
CREDIT_CARD_1_CVV=123
CREDIT_CARD_1_NOTE=测试卡

CREDIT_CARD_2_TYPE=MASTERCARD
CREDIT_CARD_2_NUMBER=****************
CREDIT_CARD_2_FIRST_NAME=NDH
CREDIT_CARD_2_LAST_NAME=MCJ
CREDIT_CARD_2_BILLING_ADDRESS=JSUH
CREDIT_CARD_2_CITY=HWYS
CREDIT_CARD_2_COUNTRY=China
CREDIT_CARD_2_EXP_MONTH=3
CREDIT_CARD_2_EXP_YEAR=2033
CREDIT_CARD_2_CVV=636
CREDIT_CARD_2_NOTE=备用万事达卡

CREDIT_CARD_3_TYPE=JCB
CREDIT_CARD_3_NUMBER=3530111333300000
CREDIT_CARD_3_FIRST_NAME=Zhang
CREDIT_CARD_3_LAST_NAME=Wei
CREDIT_CARD_3_BILLING_ADDRESS=789 Example Avenue
CREDIT_CARD_3_CITY=Guangzhou
CREDIT_CARD_3_COUNTRY=China
CREDIT_CARD_3_EXP_MONTH=9
CREDIT_CARD_3_EXP_YEAR=2027
CREDIT_CARD_3_CVV=789
CREDIT_CARD_3_NOTE=JCB卡-备用

