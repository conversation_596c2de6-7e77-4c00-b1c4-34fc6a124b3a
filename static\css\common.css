/* 通用样式 - Common Styles */
:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --background-color: #f8fafc;
    --text-color: #1f2937;
    --border-radius: 12px;
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-input: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-input-focus: 0 0 0 4px rgba(37, 99, 235, 0.15);
    --spacing-unit: 1rem;
    --section-padding: 1.5rem;
    --form-spacing: 1.25rem;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, Oxygen, Ubu<PERSON><PERSON>, Cantarell, sans-serif;
    color: var(--text-color);
    line-height: 1.6;
}

/* 通用按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: none;
    font-size: 1rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--secondary-color);
    transform: translateY(-1px);
}

.btn-secondary {
    background: #f3f4f6;
    color: var(--text-color);
    border: 1px solid #e5e7eb;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

/* 通用输入框样式 */
input, select, textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    background: white;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-input);
    font-family: inherit;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-input-focus);
}

/* 验证状态样式 */
input.valid, select.valid {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

input.invalid, select.invalid {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* 加载动画 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 状态图标 */
.status-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    font-weight: bold;
}

.status-icon.success {
    background: #10b981;
    color: white;
}

.status-icon.error {
    background: #ef4444;
    color: white;
}

.status-icon.warning {
    background: #f59e0b;
    color: white;
}

/* 弹窗基础样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.9rem;
    }
    
    input, select, textarea {
        padding: 0.625rem 0.875rem;
    }
}
