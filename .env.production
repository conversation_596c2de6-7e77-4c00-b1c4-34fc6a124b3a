# 生产环境配置 - 使用PostgreSQL
# 复制到 .env 文件使用

# 数据库配置 - 生产使用PostgreSQL
DATABASE_TYPE=postgresql
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=visa_automator
POSTGRES_USER=visa_user
POSTGRES_PASSWORD=visa_password_2024

# API配置
SECRET_KEY=your-very-secure-production-key
DEBUG=false

# 用户认证
API_BASIC_USERS=admin,operator1,operator2
API_BASIC_PASSWORDS=secure_admin_pass,secure_op1_pass,secure_op2_pass

# 文件上传配置
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png

# OCR配置
ALIYUN_APPCODE=your_production_aliyun_appcode
OCR_TEMP_DIR=temp/ocr

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs/fastapi
LOG_FILENAME_PREFIX=vietnam_evisa_api

# 其他生产配置...
ANTI_CAPTCHA_API_KEY=your_production_anti_captcha_key
BROWSER=chromium
HEADLESS=true
SLOW_MO=0
VIETNAM_EVISA_URL=https://evisa.gov.vn/

# 邮箱配置
EMAIL_HOST_1=imap.gmail.com
EMAIL_PORT_1=993
EMAIL_USER_1=<EMAIL>
EMAIL_PASSWORD_1=your_production_app_password
