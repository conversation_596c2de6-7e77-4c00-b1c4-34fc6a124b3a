# api/routes/visa.py
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from fastapi.concurrency import run_in_threadpool
from typing import Dict, Any

from ..models.requests import VisaFormTextFields
from ..models.responses import VisaApplicationResponse
from ..core.dependencies import get_simple_engine, require_auth
from ..core.exceptions import VisaApplicationError
from app.data.models import Applicant
from app.data.applicant_mapper import map_form_to_applicant
from app.email.email_runner import start_polling_scheduler

router = APIRouter(prefix="/visa", tags=["Visa Application"])


@router.post("/apply", response_model=VisaApplicationResponse)
async def apply_visa(
    form_data: VisaFormTextFields,
    background_tasks: BackgroundTasks,
    engine=Depends(get_simple_engine),
    user=Depends(require_auth)
):
    """
    提交签证申请

    - **form_data**: 签证申请表单数据
    - **返回**: 申请结果和跟踪信息
    """
    try:
        # 将表单数据映射为内部申请人对象
        form_dict = form_data.model_dump() if hasattr(form_data, 'model_dump') else form_data.dict()
        internal_applicant: Applicant = map_form_to_applicant(form_dict)

        # 在线程池中执行自动化任务（避免阻塞）
        automation_succeeded: bool = await run_in_threadpool(
            engine.run_vietnam_evisa_step1, internal_applicant
        )

        if automation_succeeded:
            # 启动邮件监控（后台任务）
            background_tasks.add_task(
                start_polling_scheduler,
                [internal_applicant]
            )

            return VisaApplicationResponse(
                success=True,
                message="签证申请提交成功，请等待处理结果",
                application_id=internal_applicant.application_id,
                status="submitted",
                tracking_info={
                    "submission_time": internal_applicant.created_at.isoformat(),
                    "expected_processing_days": "3-5",
                    "email": internal_applicant.email
                }
            )
        else:
            raise VisaApplicationError(
                "自动化处理失败，请稍后重试或联系客服",
                "AUTOMATION_FAILED"
            )

    except Exception as e:
        if isinstance(e, VisaApplicationError):
            raise e
        raise VisaApplicationError(
            f"签证申请处理失败: {str(e)}",
            "PROCESSING_ERROR"
        )


@router.get("/status/{application_id}")
async def get_visa_status(
    application_id: str,
    user=Depends(require_auth)
):
    """
    查询签证申请状态

    - **application_id**: 申请ID
    - **返回**: 申请状态信息
    """
    try:
        # 这里应该从数据库查询申请状态
        # 暂时返回模拟数据
        return {
            "success": True,
            "message": "状态查询成功",
            "application_id": application_id,
            "status": "processing",
            "last_updated": "2025-01-01T12:00:00",
            "steps": [
                {"step": "submitted", "status": "completed", "time": "2025-01-01T10:00:00"},
                {"step": "processing", "status": "in_progress", "time": "2025-01-01T11:00:00"},
                {"step": "payment", "status": "pending", "time": None},
                {"step": "approval", "status": "pending", "time": None}
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"状态查询失败: {str(e)}")


@router.get("/applications")
async def list_applications(
    page: int = 1,
    page_size: int = 10,
    status: str = None,
    user=Depends(require_auth)
):
    """
    获取申请列表

    - **page**: 页码
    - **page_size**: 每页大小
    - **status**: 状态筛选
    - **返回**: 申请列表
    """
    try:
        # 这里应该从数据库查询申请列表
        # 暂时返回模拟数据
        applications = [
            {
                "application_id": f"E2025012300{i}",
                "applicant_name": f"申请人{i}",
                "status": "processing" if i % 2 == 0 else "completed",
                "submission_time": "2025-01-01T10:00:00",
                "last_updated": "2025-01-01T12:00:00"
            }
            for i in range(1, 6)
        ]

        # 状态筛选
        if status:
            applications = [app for app in applications if app["status"] == status]

        # 分页
        start = (page - 1) * page_size
        end = start + page_size
        paginated_apps = applications[start:end]

        return {
            "success": True,
            "message": "申请列表获取成功",
            "data": paginated_apps,
            "total": len(applications),
            "page": page,
            "page_size": page_size
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取申请列表失败: {str(e)}")


@router.delete("/applications/{application_id}")
async def cancel_application(
    application_id: str,
    user=Depends(require_auth)
):
    """
    取消申请

    - **application_id**: 申请ID
    - **返回**: 取消结果
    """
    try:
        # 这里应该实现取消申请的逻辑
        # 检查申请状态，只有特定状态才能取消

        return {
            "success": True,
            "message": f"申请 {application_id} 已成功取消",
            "application_id": application_id,
            "cancelled_at": "2025-01-01T12:00:00"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消申请失败: {str(e)}")
