venv/ 
__pycache__/ 
*.pyc 
*.db 
*.db-journal 
*.log 
.prefs/ 
dist/ 
build/ 
*.spec 
screenshots/
payment_screenshots/
test_payment.py
payment_cards.json
app/payment/payment_cards.json
.vscode/
.specstory/
test/
error_ward_select_not_loaded.png
# SpecStory explanation file
.specstory/.what-is-this.md
app/ui/tabs/backup/
app/payment_example.py
app/email/credentials.json
app/email/token.json
app/email/gmail_watcher.py
**/credentials*.json
**/token*.json
downloads/
error*.png
reorder_applicant_table.py
logs/
*.log
测试姓名90天多次岘港20052025.pdf
uid_cache.json
# 敏感信息文件
.env
*_secret*.json
*_secrets*.json
*_credential*.json
*_credentials*.json
*_key*.json
*_keys*.json
*_card*.json
*_cards*.json
uploads/

# Docker相关
docker-compose.override.yml  # 保留此行，通常包含本地特定设置

#测试数据
test_data/

check_env.py

.idea/

uid_cache.json
