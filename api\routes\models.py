# api/routes/models.py
from datetime import date
from typing import Optional, Any
from pydantic import BaseModel, validator


class VisaFormTextFields(BaseModel):
    """签证表单文本字段"""
    surname: str
    given_name: str
    chinese_name: Optional[str] = None
    sex: str
    dob: str
    place_of_birth: str
    nationality: str = "CHINA"
    religion: str = "NO"
    passport_number: str
    passport_type: str = "Ordinary passport"
    place_of_issue: str
    date_of_issue: str
    passport_expiry: str
    email: str
    telephone_number: str
    permanent_address: Optional[str] = None
    contact_address: Optional[str] = None
    emergency_contact_name: Optional[str] = None
    emergency_address: Optional[str] = None
    emergency_contact_phone: Optional[str] = None
    visa_entry_type: str
    visa_validity_duration: str
    visa_start_date: str
    intended_entry_gate: str

    @validator('*', pre=True, always=True)
    def empty_str_as_none(cls, v: Any):
        if isinstance(v, str) and v.strip() == '':
            return None
        return v


class VisaApplicationResponse(BaseModel):
    """签证申请响应"""
    success: bool
    message: str
    application_id: Optional[str] = None
    redirect_url: Optional[str] = None


class OCRResponse(BaseModel):
    """OCR识别响应"""
    success: bool
    message: str
    fields: Optional[dict] = None


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str
    timestamp: str
    version: str
    uptime: Optional[str] = None
