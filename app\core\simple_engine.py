# 文件: app/core/simple_engine.py
from app.utils.logger_config import get_logger
import yaml
import uuid
from pathlib import Path
from time import sleep # 保留 sleep 导入
from playwright.sync_api import sync_playwright, Error as PlaywrightError
from app.fillers.vietnam_filler import VietnamFiller
from app.data.models import Applicant
from app.payment.payment_automation import process_payment, process_payment_with_existing_page
from concurrent.futures import ThreadPoolExecutor
from db.db import get_or_create_applicant, insert_visa_task
from app.email.email_trigger import confirm_payment_arrived
# 导入统一的浏览器配置模块
from config.browser_config import launch_form_browser, get_browser_config


logger = get_logger()

BASE_DIR = Path(__file__).resolve().parent.parent.parent # 项目根目录

class SimpleEngine:
    """
    一个简化的自动化引擎，用于 V1。
    处理配置加载和 Playwright 启动/关闭。
    不再强制设置视口大小，使用浏览器默认大小或Playwright默认大小。
    """
    def __init__(self):
        self.locators = {}
        self.settings = {}
        self._load_config()

    def _load_config(self):
        """加载元素定位器locators/*.yaml和环境变量配置"""
        # 加载定位器
        locators_dir = BASE_DIR / 'config/locators'
        logger.debug(f"Attempting to load locators from directory: {locators_dir}")
        if locators_dir.is_dir():
            for file_path in locators_dir.glob('*.yaml'):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        country_key = file_path.stem
                        self.locators[country_key] = yaml.safe_load(f) or {}
                    logger.info(f"✅ Successfully loaded locators: {file_path}")
                except Exception as e:
                    logger.error(f"Failed to load locator file {file_path}: {e}", exc_info=True)
        else:
            logger.warning(f"Locators directory not found: {locators_dir}")
        
        # 从环境变量加载设置
        from app.utils.env_loader import load_env_var
        self.settings = {
            "vietnam_evisa_url": load_env_var("VIETNAM_EVISA_URL", "https://evisa.gov.vn/"),
            "browser": load_env_var("BROWSER", "chromium"),
            "headless": load_env_var("HEADLESS", "true").lower() == "true",
            "slow_mo": int(load_env_var("SLOW_MO", "0")),
            "anti_captcha_api_key": load_env_var("ANTI_CAPTCHA_API_KEY", "")
        }
        logger.info("✅ Successfully loaded settings from environment variables")


    def run_vietnam_evisa_step1(self, applicant: Applicant) -> bool: # 明确返回类型为 bool
        """
        执行越南电子签第一步填充和付款的核心函数。
        包含启动浏览器、导航、调用 Filler、执行付款和关闭浏览器。
        返回 True 表示整个流程（填表+付款）成功，否则返回 False。
        添加全流程自动重试机制，最大重试次数为3次(含第一次)
        """
        # 为整个流程创建一个会话ID
        session_id = str(uuid.uuid4())
        logger.info(f"创建新的自动化会话: {session_id}, 申请人: {applicant.passport_number}")
        
        # 添加防止重复付款的检查
        from db.db import check_payment_exists
        if check_payment_exists(applicant.passport_number):
            logger.warning(f"⚠️ 检测到申请人 {applicant.passport_number} 已有付款记录，跳过自动化流程,结束任务")
            return True  # 已有付款记录视为成功


        max_retries = 3 # 最大重试次数
        for attempt in range(1, max_retries + 1):
            logger.info(f"==== 第 {attempt} 次自动化流程尝试，会话ID: {session_id} ====")
            try:
                logger.info(f"引擎开始执行越南电子签流程（填表+付款），会话ID: {session_id}")
                page = None
                browser = None
                p_context = None
                fill_success = False # 初始化填表状态
                payment_success = False # 初始化付款状态
                overall_success = False # 初始化整体流程状态

                filler = VietnamFiller()
                # 将会话ID传递给filler
                filler.session_id = session_id
                try:
                    logger.debug(f"准备加载Filler配置,会话ID: {session_id}")
                    filler.prepare(self.locators, self.settings)
                    logger.debug(f"✅Filler配置加载成功,会话ID: {session_id}")
                except ValueError as e:
                    logger.error(f"Filler 准备失败: {e} 会话ID: {session_id}")
                    # 不直接返回，而是设置标志变量，允许重试循环继续
                    fill_success = False
                    continue  # 跳到下一次重试循环

                # 从统一的浏览器配置模块获取配置
                browser_config = get_browser_config()
                browser_type = browser_config.get('browser_type', 'chromium')
                headless = browser_config.get('headless', True)
                slow_mo = browser_config.get('slow_mo', 0)
                logger.debug(f"浏览器配置: 类型={browser_type}, 无头模式={headless}, 慢动作={slow_mo}ms")

                # Playwright 上下文管理
                try:
                    logger.info(f"启动 Playwright (浏览器: {browser_type}, Headless: {headless})...")
                    p_context = sync_playwright().start()
                    logger.debug("✅ Playwright上下文创建成功")

                    # 使用统一的浏览器启动函数
                    browser, context, page = launch_form_browser(p_context)
                    logger.debug("✅ 浏览器、上下文和页面已创建")

                    # 导航到目标URL并实现一次自动刷新
                    logger.info(f"导航至: {filler.base_url}")
                    if not filler.base_url:
                        logger.error("配置中缺少越南电子签 URL！")
                        raise ValueError("配置中缺少越南电子签 URL")  # 抛出异常而不是返回False
                    try:
                        # 第一次尝试加载页面，30秒超时
                        logger.info("开始加载页面...")
                        page.goto(filler.base_url, timeout=30000)  # 30秒超时
                        logger.debug("✅ 页面加载成功")
                    except Exception as e:
                        logger.warning(f"❌页面加载超时 (30秒)，尝试刷新: {e},再等30秒...")
                        try:
                            # 刷新页面，再等30秒
                            page.reload(timeout=30000)  # 再给30秒尝试刷新
                            logger.info("✅ 页面刷新加载成功")
                        except Exception as refresh_e:
                            # 如果刷新后仍然失败，则触发外层重试
                            logger.error(f"❌页面刷新后仍然加载失败: {refresh_e}")
                            raise  # 重新抛出异常，触发外层重试

                    # --- 步骤 1: 执行填表 ---
                    logger.info("页面加载完成，开始调用 filler 填充...")
                    fill_success = filler.fill_step1_personal_info(page, applicant)
                    logger.debug(f"填表结果: {fill_success}")

                    payment_success = False
                    if fill_success:
                        logger.info("✅ Filler 主要填充流程成功完成。")
                        
                        # --- 步骤 2: 执行付款 ---
                        try:
                            # 使用现有页面执行付款流程
                            logger.info("使用现有页面执行付款流程...")

                             # 二次检查是否已付款
                            
                            if check_payment_exists(applicant.passport_number):
                                logger.warning(f"⚠️ 付款前二次检查：申请人 {applicant.passport_number} 已有付款记录，跳过付款")
                                payment_success = True  # 已有付款记录视为成功
                            else:
                                # 从统一的浏览器配置模块获取付款配置
                                payment_settings = {
                                    "payment_timeout_ms": browser_config.get("timeout_ms", 300000),
                                    "screenshots_dir": browser_config.get("screenshots_dir", "screenshots")
                                }
                                
                                # 直接在当前线程中执行，不使用ThreadPoolExecutor
                                payment_result = process_payment_with_existing_page(page, settings=payment_settings)
                                
                                if payment_result is True:
                                    logger.info("✅ 支付流程成功完成")
                                    payment_success = True
                                elif payment_result is False:
                                    logger.warning("❌ 支付流程失败")
                                    payment_success = False
                                elif payment_result is None or payment_result == "unknown":
                                    logger.info("已成功执行支付自动化流程，但付款状态不明确，请客户检查邮箱确认付款状态。")
                                    payment_success = True  # 将付款状态不明确视为成功
                        except Exception as payment_e:
                            logger.error(f"❌ 在执行付款流程时发生异常: {payment_e}", exc_info=True)
                            logger.debug(f"❌ 付款异常详情: {str(payment_e)}")
                            payment_success = False
                            # 移除这里的 return False，让代码继续执行到 finally 块进行资源清理
                    else: 
                        logger.error("❌ Filler 报告在填充过程中遇到错误。")
                        logger.debug("❌ 填表失败，检查 Filler 日志以获取详细错误信息")
                        if not headless:
                            logger.info("❌ 流程出错（填表失败），浏览器将保持打开 10 秒钟以便检查错误...")
                            logger.debug("非无头模式下保持浏览器打开10秒以便检查")
                            sleep(10)
                        fill_success = False
                        #return False  # 填表失败也返回失败状态

                except PlaywrightError as pe:
                    logger.error(f"Playwright 运行时发生错误: {pe}", exc_info=True)
                    logger.debug(f"Playwright错误详情: {str(pe)}")
                    raise  # 重新抛出异常，让外层catch捕获并触发重试
                except Exception as e:
                    logger.critical(f"❌ 自动化引擎运行时发生未预料的严重错误: {e}", exc_info=True)
                    logger.debug(f"❌ 未预期异常详情: {e!r},{str(e)}")
                    raise  # 重新抛出异常，让外层catch捕获并触发重试
                finally:
                    # --- 清理填表流程的资源 ---
                    logger.info("执行填表流程清理操作...")
                    try:
                        if page and not page.is_closed():
                            try:
                                logger.debug("关闭页面")
                                page.close()
                                logger.info("页面已关闭。")
                            except Exception as page_close_e:
                                logger.warning(f"❌ 关闭页面时发生错误（可忽略）: {page_close_e}")
                                logger.debug(f"❌ 关闭页面错误详情: {str(page_close_e)}")
                        
                        if browser and browser.is_connected():
                            try:
                                logger.debug("关闭浏览器")
                                browser.close()
                                logger.info("浏览器已关闭。")
                            except Exception as browser_close_e:
                                logger.warning(f"❌ 关闭浏览器时发生错误（可忽略）: {browser_close_e}")
                                logger.debug(f"❌ 关闭浏览器错误详情: {str(browser_close_e)}")
                        
                        if p_context:
                            try:
                                logger.debug("停止Playwright上下文")
                                p_context.stop()
                                logger.info("Playwright 上下文已停止。")
                            except Exception as playwright_stop_e:
                                logger.warning(f"❌ 停止 Playwright 时发生错误（可忽略）: {playwright_stop_e}")
                                logger.debug(f"❌ 停止Playwright错误详情: {str(playwright_stop_e)}")
                        
                        logger.info("填表流程清理完成。")
                    except Exception as e:
                        logger.critical(f"❌ 在执行清理操作时发生严重错误: {e}", exc_info=True)
                        logger.debug(f"❌ 清理操作严重错误详情: {str(e)}")
                        # 确保即使在清理过程中出现问题，也能继续执行后续代码

                
                # --- 步骤 3: 确定整体流程结果 ---
                overall_success = fill_success and payment_success # 必须填表和付款都成功
                logger.debug(f"整体流程结果: 填表={fill_success}, 付款={payment_success}, 整体={overall_success}")
                if overall_success:
                    logger.info("✅✅ 整个签证申请流程（填表+付款）成功完成。")
                    logger.debug("开始保存申请人和任务记录到数据库")

                    try:
                        logger.debug(f"获取或创建申请人记录: {applicant.chinese_name}, {applicant.passport_number}")
                        applicant_id = get_or_create_applicant(vars(applicant))
                        logger.debug(f"申请人ID: {applicant.chinese_name}, {applicant_id}")
                        # 插入签证任务记录
                        logger.debug("准备签证任务数据")
                        visa_task_id = insert_visa_task(applicant_id, {
                            "visa_type": applicant.visa_entry_type,
                            "visa_validity_days": int(applicant.visa_validity_duration.replace("天", "")) if applicant.visa_validity_duration else None,
                            "visa_start_date": applicant.visa_start_date,
                            "entry_gate": applicant.intended_entry_gate,
                            "submit_status": "SUCCESS",
                            "approval_status": "WAITING",
                            "pdf_path": None,
                            "download_time": None,
                            "screenshot_path": None,  # 如有错误截图，可补填
                            "log_path": None,         # 如有独立日志路径，可补填
                            "error_message": None,
                            "application_number": getattr(applicant, "application_number", None),
                            "session_id": session_id  # 使用引擎级别的会话ID
                        })

                        logger.debug(f"准备插入签证任务数据: {visa_task_id}, 会话ID: {session_id}")

                        logger.debug(f"触发付款确认邮件检查: {applicant.passport_number}")

                        confirm_payment_arrived(applicant)  # ✅ 立即触发事件型邮箱轮询

                        logger.info(f"✅ 成功触发付款确认邮件，任务ID: {visa_task_id},会话ID: {session_id}")


                        logger.info(f"✅ 成功保存签证任务记录，任务ID: {visa_task_id}, 会话ID: {session_id}")
                    except Exception as persist_error:
                        logger.warning(f"⚠️ 签证和付款流程成功，但保存任务记录失败: {persist_error}", exc_info=True)
                        logger.debug(f"❌ 保存任务记录失败详情: {str(persist_error)}")

                        # 尝试触发基于事件（付款后）的邮件轮询，即使数据库操作失败
                        try:
                            logger.debug(f"尝试在数据库插入错误的情况下触发基于事件（付款后）的邮件轮询: {applicant.passport_number}")
                            confirm_payment_arrived(applicant)
                            logger.info(f"✅ 在错误处理中成功触发基于事件（付款后）的邮件轮询")
                        except Exception as email_error:
                            logger.error(f"❌ 触发付款确认邮件失败: {email_error}")  

                    return True # 返回整体流程成功
                else:
                    logger.warning("⚠️⚠️ 整个签证申请流程未能成功完成（填表或付款失败）。")
                    logger.debug(f"流程失败原因: 填表状态={fill_success}, 付款状态={payment_success}")

                logger.info("准备关闭浏览器...")

                #return overall_success # 返回整体流程的最终结果
            
                

                # --- 添加垃圾回收 ---
                try:
                    import gc
                    logger.debug("执行垃圾回收")
                    gc.collect()
                    logger.info("✅ 垃圾回收已完成。")
                except Exception as gc_error:
                    logger.warning(f"垃圾回收时发生错误（可忽略）: {gc_error}")
                    logger.debug(f"垃圾回收错误详情: {str(gc_error)}")


            # ====== 原有全部流程代码结束 ======
            except Exception as e:
                logger.error(f"第 {attempt} 次流程发生未捕获异常: {e}", exc_info=True)
                logger.debug(f"未捕获异常详情: {str(e)}")
                logger.debug(f"未捕获异常详情 (e_attempt): {e!r}", exc_info=True)
            if attempt < max_retries:
                logger.info(f"准备进行第 {attempt+1} 次全流程重试...")
        logger.error("已达到最大重试次数，流程执行失败。")
        logger.debug(f"最终结果: 失败，尝试次数: {attempt}/{max_retries}")
        return False

           
