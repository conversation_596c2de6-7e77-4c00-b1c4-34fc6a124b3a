# 文件: app/ui/tabs/vietnam_tab.py (真正完整版 V7 - 添加签证类型下拉框 - 绝对无省略)
from app.utils.logger_config import get_logger
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QFormLayout, QLabel,
                             QLineEdit, QPushButton, QMessageBox, QTextEdit,
                             QFileDialog, QApplication, QHBoxLayout, QComboBox,
                             QCheckBox,QDateEdit,QRadioButton, QButtonGroup,QSizePolicy)
from PyQt6.QtCore import Qt, QDir, QDate # <--- Import QDate
from pathlib import Path
# Import datetime for default start date calculation
from datetime import datetime, timedelta
from app.data.models import Applicant
from app.core.simple_engine import SimpleEngine
from app.core.user_prefs import load_preference, save_preference, clear_preference
from app.utils.address_tools import generate_random_address
from app.utils.ocr_utils import run_aliyun_passport_ocr
from app.ui.widgets.image_drop_box import ImageDropBox
from app.ui.dialogs.cropper_dialog import CropperDialog
from app.utils.date_utils import calculate_working_days_from_today, VIETNAM_HOLIDAYS_2025
from app.batch_processing.batch_applicant_importer import import_applicants_from_folder
from app.ui.dialogs.batch_preview_dialog import BatchPreviewDialog
from app.batch_processing.batch_runner import run_batch
import logging


logger = get_logger()

# --- Constants ---
LAST_DATA_KEY = "last_used_applicant_data"
REMEMBER_EMAIL_KEY = "remember_email_flag"; REMEMBERED_EMAIL_VALUE_KEY = "remembered_email_value"
REMEMBER_PHONE_KEY = "remember_phone_flag"; REMEMBERED_PHONE_VALUE_KEY = "remembered_phone_value"
VISA_TYPE_MAP = {"单次入境 (Single-entry)": "Single-entry", "多次入境 (Multiple-entry)": "Multiple-entry"}
REVERSE_VISA_TYPE_MAP = {v: k for k, v in VISA_TYPE_MAP.items()}
VISA_VALIDITY_OPTIONS = ["30天", "90天"]
# Corrected Map in app/ui/tabs/vietnam_tab.py V12
ENTRY_GATE_MAP = {
    "胡志明 ": "Tan Son Nhat Int Airport (Ho Chi Minh City)", # Double Check This Exact Text from F12
    "河内": "Noi Bai Int Airport",                        # Double Check This Exact Text from F12
    "岘港": "Da Nang International Airport",             # Double Check This Exact Text from F12
    "芽庄": "Cam Ranh Int Airport (Khanh Hoa)",      # Confirmed from HTML
    "东兴": "Mong Cai Landport",  # 添加新的东兴口岸
    "友谊": "Huu Nghi Landport"   # 添加新的友谊口岸
    
    # Add others if needed, using EXACT text from the dropdown list:
    # "芹苴 (Can Tho)": "Can Tho International Airport",
    # "海防 (Cat Bi)": "Cat Bi Int Airport (Hai Phong)",
    # ... etc
}
REVERSE_ENTRY_GATE_MAP = {v: k for k, v in ENTRY_GATE_MAP.items()}
# === ===

# --- QtLogHandler Definition ---
class QtLogHandler(logging.Handler):
    def __init__(self, text_widget: QTextEdit):
        super().__init__()
        self.widget = text_widget
        self.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', '%H:%M:%S'))
        
    def emit(self, record):
        try:
            msg = self.format(record)
            self.widget.append(msg)
        except Exception:
            self.handleError(record)
# --- ---

class VietnamVisaTab(QWidget):
    def __init__(self, parent=None):
        # 1. 调用父类的 __init__()
        super().__init__(parent)  # 这行必须放在最前面

        # 2. 初始化 applicant，防止 AttributeError
        if not hasattr(self, 'applicant'):
            self.applicant = Applicant()  # 假设 Applicant 是一个类，包含了相关字段

        # 其他初始化代码
        self.selected_portrait_path = None
        self.selected_passport_path = None
        # OCR 是否已完成（用于防止用户太快点击自动填表按钮）
        self.ocr_finished = True  # 初始设为 True，表示没有 OCR 阻塞

        # 初始化 UI 和其他必要操作
        self.init_ui()  # 初始化 UI
        self.load_last_used_data()  # 加载上次使用的数据
        #self._setup_log_handler()  # 设置日志处理器
            
    

    def init_ui(self):
        main_layout = QVBoxLayout(self)
        self.form_layout = QFormLayout()

        # ✅ 护照和照片上传框
        upload_layout = QHBoxLayout()
        upload_layout.setContentsMargins(0, 0, 0, 0)  # 移除内边距
        upload_layout.setSpacing(10)  # 设置组件之间的间距
    
        # 创建上传框
        self.passport_box = ImageDropBox("上传护照", "passport")
        self.passport_box.imageChanged.connect(self.on_passport_selected)
        self.passport_box.setFixedSize(280, 200)
        

        self.portrait_box = ImageDropBox("上传照片", "portrait")
        self.portrait_box.imageChanged.connect(self.on_portrait_selected)
        self.portrait_box.setFixedSize(200, 200)
        
        # 添加到布局，并添加伸缩项使其左对齐
        upload_layout.addWidget(self.passport_box)
        upload_layout.addWidget(self.portrait_box)
        upload_layout.addStretch(1)  # 添加伸缩项，使组件左对齐

        # 添加到表单布局，使用标签
        self.form_layout.addRow("", upload_layout)

        # Create Input Widgets
        self.customer_source_edit = QLineEdit() # Customer source (for record only)
        self.chinese_name_edit = QLineEdit()  # Chinese name (only for UI display)
        #self.chinese_name_edit.setReadOnly(True)  # cannot be edited by user
        self.passport_no_edit = QLineEdit()
        self.surname_edit = QLineEdit()
        self.given_name_edit = QLineEdit()
        self.sex_edit = QLineEdit()
        self.dob_edit = QLineEdit(); self.dob_edit.setPlaceholderText("DD/MM/YYYY")
        self.nationality_edit = QLineEdit("China")
        #self.visa_type_combo = QComboBox() # Create ComboBox
        #self.visa_type_combo.addItems(VISA_TYPE_MAP.keys())
        #self.visa_type_combo.setCurrentText("单次入境 (Single-entry)")
        #self.visa_validity_combo = QComboBox(); self.visa_validity_combo.addItems(VISA_VALIDITY_OPTIONS); self.visa_validity_combo.setCurrentText("90天")

        # === 将签证类型从下拉框改为单选按钮 ===
        self.visa_type_group = QButtonGroup(self)
        self.visa_type_layout = QHBoxLayout()
        
        # 创建单次入境单选按钮
        self.radio_single_entry = QRadioButton("单次入境 (Single-entry)")
        #self.radio_single_entry.setChecked(True)  # 默认选中
        self.visa_type_group.addButton(self.radio_single_entry)
        self.visa_type_layout.addWidget(self.radio_single_entry)
        
        # 创建多次入境单选按钮
        self.radio_multiple_entry = QRadioButton("多次入境 (Multiple-entry)")
        self.radio_multiple_entry.setChecked(True)  # 默认选择多次入境
        self.visa_type_group.addButton(self.radio_multiple_entry)
        self.visa_type_layout.addWidget(self.radio_multiple_entry)
        
        # 添加伸缩项，使按钮靠左对齐
        self.visa_type_layout.addStretch()
        
        # === 将签证有效期从下拉框改为单选按钮 ===
        self.visa_validity_group = QButtonGroup(self)
        self.visa_validity_layout = QHBoxLayout()
        # 设置布局的间距为0或较小的值
        #self.visa_validity_layout.setSpacing(10)  # 设置控件之间的间距为10像素
        #self.visa_validity_layout.setContentsMargins(0, 0, 0, 0)  # 设置布局的边距为0
        
        # 创建30天单选按钮
        self.radio_30_days = QRadioButton("30天")
        self.visa_validity_group.addButton(self.radio_30_days)
        self.visa_validity_layout.addWidget(self.radio_30_days)
        
        # 创建90天单选按钮
        self.radio_90_days = QRadioButton("90天")
        self.radio_90_days.setChecked(True)  # 默认选中
        self.visa_validity_group.addButton(self.radio_90_days)
        self.visa_validity_layout.addWidget(self.radio_90_days)

        # 添加伸缩项，使按钮靠左对齐
        self.visa_validity_layout.addStretch()
        
        

        # 创建生效日期行的水平布局
        visa_start_date_layout = QHBoxLayout()

        # === Use QDateEdit instead of QLineEdit for Start Date ===
        self.visa_start_date_edit = QDateEdit()
        self.visa_start_date_edit.setCalendarPopup(True) # Enable calendar popup
        self.visa_start_date_edit.setDisplayFormat("dd/MM/yyyy") # Set display format
        # Set a default date, e.g., tomorrow
        self.visa_start_date_edit.setDate(QDate.currentDate().addDays(1))
        visa_start_date_layout.addWidget(self.visa_start_date_edit)

        # 添加一些间距
        visa_start_date_layout.addSpacing(20)

        # 创建"出签生效"复选框
        self.check_expedited = QCheckBox("出签生效(4工作日)")
        visa_start_date_layout.addWidget(self.check_expedited)

        # 减小这里的间距，从10改为5或更小
        visa_start_date_layout.addSpacing(1)# 将间距从10减小到5

        # 创建只读日期显示框（用于显示计算后的出签生效日期）
        self.expedited_date_display = QLineEdit()
        self.expedited_date_display.setReadOnly(False)
        self.expedited_date_display.setFixedWidth(100)

        # 计算并显示4个工作日后的日期
        expedited_date = calculate_working_days_from_today(4, VIETNAM_HOLIDAYS_2025)
        self.expedited_date_display.setText(expedited_date.strftime("%d/%m/%Y"))
        visa_start_date_layout.addWidget(self.expedited_date_display)

        # === New Entry Gate ComboBox ===
        self.entry_gate_combo = QComboBox()
        self.entry_gate_combo.addItems(ENTRY_GATE_MAP.keys()) # Add Chinese options
        # Optionally set a default, e.g., Ho Chi Minh
        if "胡志明 (Tan Son Nhat)" in ENTRY_GATE_MAP:
            self.entry_gate_combo.setCurrentText("胡志明 (Tan Son Nhat)")
        # === ===

        # 添加弹性空间，使控件靠左对齐
        visa_start_date_layout.addStretch()

        # 连接复选框的信号
        self.check_expedited.stateChanged.connect(self.update_visa_start_date)

                
        # Optional: Set minimum date if needed
        # self.visa_start_date_edit.setMinimumDate(QDate.currentDate())
        # === New Contact/Emergency Widgets ===
        
        self.emergency_name_edit = QLineEdit()  # New (User enters name only)
        self.emergency_phone_edit = QLineEdit() # New
        # === ===
        self.email_edit = QLineEdit(); self.email_edit.setPlaceholderText("输入邮箱地址")
        self.phone_edit = QLineEdit(); self.phone_edit.setPlaceholderText("输入手机号码")
        self.religion_edit = QLineEdit("NO") # Still created, not added to layout
            # --- 创建 Previous Visit 的控件 ---
        self.radio_group_visited = QButtonGroup(self)
        self.radio_yes_visited = QRadioButton("是 (Yes)")
        self.radio_no_visited = QRadioButton("否 (No)")
        
        self.radio_group_visited.addButton(self.radio_yes_visited); self.radio_group_visited.addButton(self.radio_no_visited)
        
        # === 创建专门用于条件字段的容器QWidget和布局 ===
        # 使用 QWidget 作为容器，方便一起显隐
        self.previous_visit_container = QWidget()
        self.previous_visit_container.setVisible(False)
        # 为容器创建一个布局 (这里用 QFormLayout 保持对齐)
        previous_visit_inner_layout = QFormLayout(self.previous_visit_container) # 把布局设置给容器
        previous_visit_inner_layout.setContentsMargins(0, 5, 0, 5) # 调整内边距 (可选)
        # 创建条件的 Label 和 Edit
        self.prev_entry_label = QLabel("上次入境日期:") # (不需要指定 self 作为 parent 了, 因为会加入布局)
        self.prev_entry_date_edit = QDateEdit()
        self.prev_entry_date_edit.setCalendarPopup(True); self.prev_entry_date_edit.setDisplayFormat("dd/MM/yyyy")
        self.prev_exit_label = QLabel("上次离开日期:")
        self.prev_exit_date_edit = QDateEdit()
        self.prev_exit_date_edit.setCalendarPopup(True); self.prev_exit_date_edit.setDisplayFormat("dd/MM/yyyy")
        self.prev_purpose_label = QLabel("上次旅行目的:")
        self.prev_purpose_edit = QLineEdit("旅游")
        # 将条件控件添加到 *内部* 布局
        previous_visit_inner_layout.addRow(self.prev_entry_label, self.prev_entry_date_edit)
        previous_visit_inner_layout.addRow(self.prev_exit_label, self.prev_exit_date_edit)
        previous_visit_inner_layout.addRow(self.prev_purpose_label, self.prev_purpose_edit)
        # self.previous_visit_container.setLayout(previous_visit_inner_layout) # 如果上面没设置parent，这里设置
        # === 容器和内部布局创建完毕 ===
        

        # Add Widgets to Layout
        self.form_layout.addRow("客户来源:", self.customer_source_edit)  # add customer source field
        self.form_layout.addRow("中文姓名:", self.chinese_name_edit)  # add Chinese name field
        self.form_layout.addRow("姓 (Surname):", self.surname_edit)
        self.form_layout.addRow("名 (Given Name):", self.given_name_edit)
        self.form_layout.addRow("护照号码:", self.passport_no_edit)
        self.form_layout.addRow("性别:", self.sex_edit)
        self.form_layout.addRow("出生日期:", self.dob_edit)
        self.form_layout.addRow("国籍:", self.nationality_edit)
        self.form_layout.addRow("签证类型:", self.visa_type_layout) # used radio buttons layout
        self.form_layout.addRow("签证有效期:", self.visa_validity_layout) # used radio buttons layout
        self.form_layout.addRow("生效日期:", visa_start_date_layout)
        #orm_layout.addRow(visa_start_date_layout)
        self.form_layout.addRow("入境口岸:", self.entry_gate_combo)
        
        
        
       
        # === Add New Rows for Contact/Emergency ===
        
        self.form_layout.addRow("紧急联系人姓名:", self.emergency_name_edit)     # <-- Added
        self.form_layout.addRow("紧急联系人电话:", self.emergency_phone_edit)   # <-- Added
        # === ===

        # Email Row
        email_layout = QHBoxLayout(); email_layout.addWidget(self.email_edit, 1)
        self.remember_email_checkbox = QCheckBox("记住"); self.remember_email_checkbox.setToolTip("...")
        self.remember_email_checkbox.stateChanged.connect(self.email_remember_state_changed)
        email_layout.addWidget(self.remember_email_checkbox); self.form_layout.addRow("邮箱 (Email):", email_layout)

        # Phone Row
        phone_layout = QHBoxLayout(); phone_layout.addWidget(self.phone_edit, 1)
        self.remember_phone_checkbox = QCheckBox("记住"); self.remember_phone_checkbox.setToolTip("...")
        self.remember_phone_checkbox.stateChanged.connect(self.phone_remember_state_changed)
        phone_layout.addWidget(self.remember_phone_checkbox); self.form_layout.addRow("手机号:", phone_layout)

        # --- 添加 Previous Visit Radio 行 ---
        prev_visit_layout = QHBoxLayout()
        prev_visit_layout.addWidget(self.radio_no_visited)
        prev_visit_layout.addWidget(self.radio_yes_visited)
        prev_visit_layout.addStretch()
        self.form_layout.addRow("最近1年是否去过越南？:", prev_visit_layout)

        # --- 添加包含条件控件的 *容器* 到布局 ---
        # QFormLayout 可以直接添加 QWidget 作为一行的内容（会占据两列）
        self.form_layout.addRow(self.previous_visit_container) # 将整个容器Widget添加到布局
        # === ----------------------------- ===

        

        # File Rows
        #portrait_layout = QHBoxLayout(); self.portrait_button = QPushButton("选择证件照片...")
        #self.portrait_button.clicked.connect(self.select_portrait_photo)
        #self.portrait_label = QLabel("未选择文件"); self.portrait_label.setStyleSheet("color: gray;")
        #portrait_layout.addWidget(self.portrait_button); portrait_layout.addWidget(self.portrait_label, 1); self.form_layout.addRow("证件照片:", portrait_layout)
        #passport_layout = QHBoxLayout(); self.passport_button = QPushButton("选择护照扫描件...")
        #self.passport_button.clicked.connect(self.select_passport_scan)
        #self.passport_label = QLabel("未选择文件"); self.passport_label.setStyleSheet("color: gray;")
        #passport_layout.addWidget(self.passport_button); passport_layout.addWidget(self.passport_label, 1); self.form_layout.addRow("护照扫描件:", passport_layout)

        # --- 填充条件控件列表 (放在它们都被添加到布局之后) ---
        self.previous_visit_widgets = [
            self.prev_entry_label, self.prev_entry_date_edit,
            self.prev_exit_label, self.prev_exit_date_edit,
            self.prev_purpose_label, self.prev_purpose_edit
        ]
        logger.info("条件控件列表已填充。")
        # --- 连接信号 (确保方法已定义在类中) ---
        if hasattr(self, 'toggle_previous_visit_fields'): # 检查方法是否存在
            self.radio_yes_visited.toggled.connect(self.toggle_previous_visit_fields)
            # ✅ 主动触发一次以隐藏初始状态
            self.toggle_previous_visit_fields(self.radio_yes_visited.isChecked())

            logger.info("连接'是否去过' Yes 按钮的 toggled 信号")
        else:
            logger.error("！！！无法连接信号，toggle_previous_visit_fields 方法未定义！")
        # --- ---

        # Start Button, Status, Log
        #self.start_button = QPushButton("开始自动填充"); self.start_button.setStyleSheet("background-color: lightgreen; padding: 10px; font-weight: bold;")
        #self.start_button.clicked.connect(self.run_automation_blocking)
        self.status_label = QLabel("状态: 就绪") 
        #self.log_display = QTextEdit(); self.log_display.setReadOnly(True); self.log_display.setMinimumHeight(150)

        # === 优化后的按钮布局 ===
        button_row_layout = QHBoxLayout()

        self.run_button = QPushButton("开始自动填充")
        self.batch_import_button = QPushButton("批量导入")
        self.batch_submit_button = QPushButton("批量提交")

        # ✅ 设置统一高度与最小宽度
        button_height = 40
        button_width = 150

        for btn in [self.run_button, self.batch_import_button, self.batch_submit_button]:
            btn.setFixedHeight(button_height)
            btn.setMinimumWidth(button_width)

        # ✅ 设置主按钮为绿色高亮
        #self.run_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        #self.batch_import_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        #self.batch_submit_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")

        #发光效果主按钮
        glow_style = """
        QPushButton {
            background-color: #4CAF50;
            color: white;
            font-weight: bold;
            border-radius: 4px;
        }
        QPushButton:hover {
            background-color: #66BB6A;
            border: 2px solid white;
        }
        """

        self.run_button.setStyleSheet(glow_style)
        self.batch_import_button.setStyleSheet(glow_style)
        self.batch_submit_button.setStyleSheet(glow_style)

        # ✅ 添加按钮 + 间距（居中）
        button_row_layout.addStretch()
        button_row_layout.addWidget(self.run_button) # 主按钮
        button_row_layout.addSpacing(70) # 间距
        button_row_layout.addWidget(self.batch_import_button)   # 批量导入
        button_row_layout.addSpacing(70) # 间距
        button_row_layout.addWidget(self.batch_submit_button)   # 批量提交
        button_row_layout.addStretch()

        # ✅ 信号连接
        self.run_button.clicked.connect(self.run_automation_blocking)
        self.batch_import_button.clicked.connect(self.on_batch_import_clicked)
        self.batch_submit_button.clicked.connect(self.on_batch_submit_clicked)

        # ✅ 添加按钮行到表单布局底部
        self.form_layout.addRow(button_row_layout)

        

        # Combine Layouts
        main_layout.addLayout(self.form_layout)
        #main_layout.addWidget(self.log_display);self.setLayout(main_layout)
        
    def on_batch_import_clicked(self):
        folder = QFileDialog.getExistingDirectory(self, "选择包含护照图像和照片的文件夹")
        if not folder:
            return  # 用户取消选择

        try:
            applicants = import_applicants_from_folder(folder)
            if not applicants:
                QMessageBox.warning(self, "导入失败", "未能导入任何申请人数据，请检查文件夹内容")
                return
            self.batch_applicants = applicants  # ✅ 存入批量数据独立变量

            dialog = BatchPreviewDialog(applicants, parent=self)
            dialog.applicant_selected.connect(self._load_applicant_to_form)  # 可选支持双击加载
            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "导入异常", f"批量导入失败：{str(e)}")

    def _load_applicant_to_form(self, applicant):
        self.update_form_from_applicant(applicant)

    def update_form_from_applicant(self, applicant):
        """将 Applicant 实例字段自动填充到 UI 表单各组件"""
        self.chinese_name_edit.setText(applicant.chinese_name or "")
        self.surname_edit.setText(applicant.surname or "")
        self.given_name_edit.setText(applicant.given_name or "")
        self.passport_no_edit.setText(applicant.passport_number or "")
        self.sex_edit.setText(applicant.sex or "")
        self.dob_edit.setText(applicant.dob or "")
        self.nationality_edit.setText(applicant.nationality or "")

        self.place_of_birth = applicant.place_of_birth or ""
        self.place_of_issue = applicant.place_of_issue or ""
        self.date_of_issue = applicant.date_of_issue or ""
        self.passport_expiry = applicant.passport_expiry or ""

        self.phone_edit.setText(applicant.telephone_number or "")
        self.email_edit.setText(applicant.email or "")

        self.entry_gate_combo.setCurrentText(applicant.intended_entry_gate or "")
        self.radio_30_days.setChecked(applicant.visa_validity_duration == 30)
        self.radio_90_days.setChecked(applicant.visa_validity_duration == 90)
        self.radio_single_entry.setChecked(applicant.visa_entry_type == "Single-entry")
        self.radio_multiple_entry.setChecked(applicant.visa_entry_type == "Multiple-entry")

        if applicant.visa_start_date:
            self.visa_start_date_edit.setDate(QDate.fromString(applicant.visa_start_date, "dd/MM/yyyy"))


    def on_batch_submit_clicked(self):
        if not hasattr(self, "batch_applicants") or not self.batch_applicants:
            QMessageBox.warning(self, "无数据", "请先批量导入申请人数据。")
            return

        run_batch(self.batch_applicants, max_concurrent=5, launch_window=30)

    
    def toggle_previous_visit_fields(self, checked: bool):
        """
        根据“是否去过越南”选择，切换显示/隐藏上次访问字段组。
        """
        self.previous_visit_container.setVisible(checked)
        logger.info(f"[切换越南访问记录] 当前状态: {'显示' if checked else '隐藏'}")



    def autofill_address_fields(self, address: str):
        """
        自动填充地址（永久地址、紧急联系人地址、联系地址）。
        注意：这些字段不展示在 UI 中，仅供自动化使用。
        """
        self.generated_address = address  # 保存随机地址副本

        if hasattr(self, "applicant"):
            self.applicant.permanent_address = address
            self.applicant.emergency_address = address
            self.applicant.contact_address = address

        

    def load_last_used_data(self):
        logger.info("加载上次数据及记住的Email/Phone...")
        last_data = load_preference(LAST_DATA_KEY, default={})

        # 确保地址字段存在，防止报错
        for field in ['permanent_address', 'emergency_address', 'contact_address']:
            if not hasattr(self.applicant, field):
                setattr(self.applicant, field, None)
        
        # ✅ 从上次数据中加载出生地
        self.applicant.place_of_birth = last_data.get("place_of_birth", "")

        # 确保出生地是大写
        if self.applicant.place_of_birth:
            self.applicant.place_of_birth = self.applicant.place_of_birth.upper()
        else:
            self.applicant.place_of_birth = "GUANGDONG"
            logger.info("上次记载的出生地格式错误，使用默认值: GUANGDONG")


        #  如果上次数据没有出生地，则设置为默认值
        if not self.applicant.place_of_birth:
            self.applicant.place_of_issue = "GUANGDONG"
            logger.info("上次记载的出生地为空，使用默认值: GUANGDONG")
        
    # ✅ 随机地址生成（出生地决定）✅
        random_address = generate_random_address(self.applicant.place_of_birth)
        logger.info(f"Generated random address: {random_address}")

        self.applicant.contact_address = random_address
        self.applicant.permanent_address = random_address
        self.applicant.emergency_address = random_address

        logger.info(f"Assigned contact_address: {self.applicant.contact_address}")
        logger.info(f"Assigned permanent_address: {self.applicant.permanent_address}")
        logger.info(f"Assigned emergency_address: {self.applicant.emergency_address}")

        self.autofill_address_fields(random_address)

        
        if isinstance(last_data, dict):
            logger.info("正在填充界面...")
            self.customer_source_edit.setText(last_data.get("customer_source", ""))
            self.chinese_name_edit.setText(last_data.get("chinese_name", "")) 
            self.passport_no_edit.setText(last_data.get("passport_number", ""))
            self.surname_edit.setText(last_data.get("surname", ""))
            self.given_name_edit.setText(last_data.get("given_name", ""))
            self.sex_edit.setText(last_data.get("sex", ""))
            self.dob_edit.setText(last_data.get("dob", ""))
            if last_data.get("nationality"): self.nationality_edit.setText(last_data["nationality"])
            self.applicant.date_of_issue = last_data.get("date_of_issue", "")
            self.applicant.passport_expiry = last_data.get("passport_expiry", "")
            self.applicant.place_of_issue = last_data.get("place_of_issue", "")
            
           

            # Load Visa Type radio buttons
            saved_visa_type_value = last_data.get("visa_entry_type")
            if saved_visa_type_value == "Multiple-entry":
                self.radio_multiple_entry.setChecked(True)
            else:
                self.radio_single_entry.setChecked(True)

            # Load Visa Validity radio buttons
            saved_validity = last_data.get("visa_validity_duration", "90天")
            if saved_validity == "30天":
                self.radio_30_days.setChecked(True)
            else:
                self.radio_90_days.setChecked(True)

             # === Load Visa Start Date (handle string to QDate) ===
            saved_start_date_str = last_data.get("visa_start_date", "")
            if saved_start_date_str:
                try:
                    # Attempt to parse the saved string into a QDate object
                    q_date = QDate.fromString(saved_start_date_str, "dd/MM/yyyy")
                    if q_date.isValid(): # Check if parsing was successful
                        self.visa_start_date_edit.setDate(q_date)
                        logger.info(f"已加载生效日期: {saved_start_date_str}")
                    else:
                        logger.warning(f"加载的生效日期格式无效: '{saved_start_date_str}', 使用默认日期。")
                        # Keep the default date set in init_ui if parsing fails
                        # self.visa_start_date_edit.setDate(QDate.currentDate().addDays(1))
                except Exception as e:
                    logger.error(f"解析保存的生效日期时出错: {e}", exc_info=True)
                    # Keep default date on error
            # else: Keep default date if nothing was saved
            # === ===
            # === Load New UI Fields ===
            
            self.emergency_name_edit.setText(last_data.get("emergency_contact_name", "")) # Loads name part only
            self.emergency_phone_edit.setText(last_data.get("emergency_contact_phone", ""))
            # === ===

             # === Load Entry Gate ===
            saved_entry_gate_value = last_data.get("intended_entry_gate") # Get saved English airport name
            ui_entry_gate_text = REVERSE_ENTRY_GATE_MAP.get(saved_entry_gate_value) # Convert to Chinese UI text
            if ui_entry_gate_text:
                self.entry_gate_combo.setCurrentText(ui_entry_gate_text)
                logger.info(f"已加载入境口岸: {ui_entry_gate_text}")
            # else: keep default set in init_ui
            # === ===

        # Load file hints
        last_portrait_path = last_data.get("portrait_photo_path")
        if last_portrait_path and Path(last_portrait_path).exists():
            self.portrait_box.load_image(last_portrait_path)
            self.selected_portrait_path = last_portrait_path

        # else: # If no last path, label is already "未选择文件" from init_ui
        #     self.portrait_label.setText("未选择文件")
        #     self.portrait_label.setStyleSheet("color: gray;")

        last_passport_path = last_data.get("passport_scan_path")
        if last_passport_path and Path(last_passport_path).exists():
            try:
                self.passport_box.imageChanged.disconnect(self.on_passport_selected)
            except TypeError:
                pass  # 防止首次运行时未连接导致报错
            self.passport_box.load_image(last_passport_path)
            self.passport_box.imageChanged.connect(self.on_passport_selected)
            self.selected_passport_path = last_passport_path
                            
            logger.info("基本填充完成。")
        else: logger.warning("未找到上次有效数据。")

        # === 加载 Previous Visit 数据 ===
        # 默认设为 False，只有当保存的值明确是 True 时才认为是 True
        visited_last_year = last_data.get("visited_vietnam_last_year") == True
        logger.info(f"加载 visited_last_year: {visited_last_year}") # 打印加载的状态
        if visited_last_year:
            self.radio_yes_visited.setChecked(True) # 设置 UI 状态
            logger.info("加载上次访问记录...")
            # 加载并设置上次日期和目的
            prev_entry_str = last_data.get("previous_entry_date", "")
            if prev_entry_str:
                try:
                    q_date = QDate.fromString(prev_entry_str, "dd/MM/yyyy")
                    if q_date.isValid():
                        self.prev_entry_date_edit.setDate(q_date)
                        logger.info(f"  已加载上次入境日期: {prev_entry_str}")
                    else:
                        logger.warning(f"  加载的上次入境日期格式无效: '{prev_entry_str}', 使用默认。")
                        # self.prev_entry_date_edit.setDate(QDate.currentDate()) # 或者不设置，让它保留默认
                except Exception as e:
                    logger.error(f"  解析上次入境日期时出错: {e}", exc_info=True)
            # else: logger.info("  未找到上次入境日期。") # 可选

            prev_exit_str = last_data.get("previous_exit_date", "")
            if prev_exit_str:
                try:
                    q_date = QDate.fromString(prev_exit_str, "dd/MM/yyyy")
                    if q_date.isValid():
                        self.prev_exit_date_edit.setDate(q_date)
                        logger.info(f"  已加载上次离开日期: {prev_exit_str}")
                    else:
                        logger.warning(f"  加载的上次离开日期格式无效: '{prev_exit_str}', 使用默认。")
                        # self.prev_exit_date_edit.setDate(QDate.currentDate())
                except Exception as e:
                    logger.error(f"  解析上次离开日期时出错: {e}", exc_info=True)
            self.prev_purpose_edit.setText(last_data.get("previous_purpose", "旅游")) # 从上次数据加载，默认为"旅游"
        else:
            self.radio_no_visited.setChecked(True) # 明确设置 "No"
        # === --- ===

        # Load remembered Email
        remember_email_flag = load_preference(REMEMBER_EMAIL_KEY, default=False)
        if remember_email_flag:
            remembered_email = load_preference(REMEMBERED_EMAIL_VALUE_KEY)
            if remembered_email: self.email_edit.setText(remembered_email); self.remember_email_checkbox.setChecked(True); logger.info("已加载记住的邮箱。")
            else: self.remember_email_checkbox.setChecked(False); clear_preference(REMEMBER_EMAIL_KEY); clear_preference(REMEMBERED_EMAIL_VALUE_KEY); logger.warning("清除无效邮箱标记。")
        else:
            if isinstance(last_data, dict) and last_data.get("email"): self.email_edit.setText(last_data.get("email", "")); logger.info("填充上次邮箱（未记）。")
            self.remember_email_checkbox.setChecked(False)

        # Load remembered Phone
        remember_phone_flag = load_preference(REMEMBER_PHONE_KEY, default=False)
        if remember_phone_flag:
            remembered_phone = load_preference(REMEMBERED_PHONE_VALUE_KEY)
            if remembered_phone: self.phone_edit.setText(remembered_phone); self.remember_phone_checkbox.setChecked(True); logger.info("已加载记住的手机。")
            else: self.remember_phone_checkbox.setChecked(False); clear_preference(REMEMBER_PHONE_KEY); clear_preference(REMEMBERED_PHONE_VALUE_KEY); logger.warning("清除无效手机标记。")
        else:
            if isinstance(last_data, dict) and last_data.get("telephone_number"): self.phone_edit.setText(last_data.get("telephone_number", "")); logger.info("填充上次手机（未记）。")
            self.remember_phone_checkbox.setChecked(False)
           
            self.status_label.setText("状态: 数据加载完成。")


    def email_remember_state_changed(self, state):
        """槽函数：当“记住邮箱”勾选框状态改变时调用"""
        email_to_process = self.email_edit.text().strip()
        is_checked = (state == Qt.CheckState.Checked.value)
        if is_checked:
            if email_to_process:
                if save_preference(REMEMBER_EMAIL_KEY, True) and save_preference(REMEMBERED_EMAIL_VALUE_KEY, email_to_process): logger.info(f"已记住邮箱: {email_to_process}")
                else: QMessageBox.warning(self, "错误", "无法保存记住的邮箱。"); self.remember_email_checkbox.setChecked(False)
            else: QMessageBox.warning(self, "提示", "邮箱为空，无法记住。"); self.remember_email_checkbox.setChecked(False)
        else:
            if clear_preference(REMEMBER_EMAIL_KEY) and clear_preference(REMEMBERED_EMAIL_VALUE_KEY): logger.info("已取消记住邮箱。")
            else: logger.warning("未能清除记住的邮箱偏好。")

    def phone_remember_state_changed(self, state):
        """槽函数：当“记住手机”勾选框状态改变时调用"""
        phone_to_process = self.phone_edit.text().strip()
        is_checked = (state == Qt.CheckState.Checked.value)
        if is_checked:
            if phone_to_process:
                if save_preference(REMEMBER_PHONE_KEY, True) and save_preference(REMEMBERED_PHONE_VALUE_KEY, phone_to_process): logger.info(f"已记住手机号: {phone_to_process}")
                else: QMessageBox.warning(self, "错误", "无法保存记住的手机号。"); self.remember_phone_checkbox.setChecked(False)
            else: QMessageBox.warning(self, "提示", "手机号为空，无法记住。"); self.remember_phone_checkbox.setChecked(False)
        else:
            if clear_preference(REMEMBER_PHONE_KEY) and clear_preference(REMEMBERED_PHONE_VALUE_KEY): logger.info("已取消记住手机号。")
            else: logger.warning("未能清除记住的手机号偏好。")

    def update_visa_start_date(self, state):
        """根据复选框状态更新签证生效日期显示"""
        if state == Qt.CheckState.Checked:
            # 选中了"出签生效"
            # 计算4个工作日后的日期
            expedited_date = calculate_working_days_from_today(4, VIETNAM_HOLIDAYS_2025)
            # 更新显示框的值
            self.expedited_date_display.setText(expedited_date.strftime("%d/%m/%Y"))
            # 可选：更新日期选择器的值（视觉反馈）
            self.visa_start_date_edit.setDate(QDate(expedited_date.year, expedited_date.month, expedited_date.day))
        else:
            # 未选中"出签生效"
            # 这里可以不做任何事情，或者根据需要添加其他逻辑
            pass
            

    def on_passport_selected(self, path: str):
        '''
        dialog = CropperDialog(path)
        if dialog.exec():
            cropped = dialog.get_cropped_pixmap()  # 下一阶段会实现
            # 暂时使用原图测试显示
            self.passport_box.image_label.setPixmap(cropped.scaled(230, 220, Qt.AspectRatioMode.KeepAspectRatio))
            self.selected_passport_path = path  # 后续应是裁剪结果保存路径
            self.run_passport_ocr(path)  # ✅ 保留 OCR 流程
        else:
            logger.info("用户取消了裁剪，未触发 OCR")
        '''
        #暂时移除护照裁剪功能，直接上传护照
        self.selected_passport_path = path
        logger.info(f"护照图像已选择：{path}")
        self.run_passport_ocr(path)  # ✅ 保留 OCR 流程
        

    def on_portrait_selected(self, path: str):
        self.selected_portrait_path = path
        logger.info(f"证件照图像已选择：{path}")


    def run_passport_ocr(self, image_path: str):
        """调用阿里云 OCR 识别护照图片，并更新 applicant 模型字段和 UI 界面输入框"""
        logger.info("正在调用阿里云 OCR 识别护照图片...")

        try:
            self.ocr_finished = False  # 标记 OCR 开始执行

            # ✅ 第一步：调用 OCR 工具函数
            # 使用 run_aliyun_passport_ocr 函数，它会内部获取 ALIYUN_APPCODE
            ocr_result = run_aliyun_passport_ocr(image_path)

            # ⛔ 如果结果为空，停止处理
            if not ocr_result:
                logger.warning("❗OCR 未能识别出任何信息。")
                self.ocr_finished = True  # 标记 OCR 已完成
                return

            logger.info(f"OCR 提取结果: {ocr_result}")

            # ✅ 第二步：写入 applicant 数据模型字段
            # ✅ 拆分姓和名（根据 OCR 返回的 name 字段）
            name_raw = ocr_result.get("name", "")  # "PU,HUAN"
            if "," in name_raw:
                surname, given_name = name_raw.split(",", 1)
            else:
                surname = name_raw
                given_name = ""
            self.applicant.chinese_name = ocr_result.get("name_cn", "") or ocr_result.get("name_cn_raw", "") # 中文姓名  
            self.applicant.surname = surname                                         #姓
            self.applicant.given_name = given_name                                   #名
            self.applicant.sex = ocr_result.get("sex", "")                           # 性别
            self.applicant.nationality = ocr_result.get("country", "")               # 国籍
            self.applicant.dob = ocr_result.get("birth_date", "")                    # 出生日期
            #self.applicant.place_of_birth = ocr_result.get("birth_place_raw", "")     # 出生地点 格式：中文/大写拼音
            self.applicant.passport_number = ocr_result.get("passport_no", "")       # 护照号码
            self.applicant.place_of_issue = ocr_result.get("issue_place", "")        # 护照签发地
            self.applicant.date_of_issue = ocr_result.get("issue_date", "")          # 护照签发日期 
            self.applicant.passport_expiry = ocr_result.get("expiry_date", "")       # 护照有效期

            # 处理出生地，从"河南 /HENAN"格式中提取"HENAN"
            birth_place_raw = ocr_result.get("birth_place_raw", "")
            if birth_place_raw and "/" in birth_place_raw:
                # 分割并获取拼音部分
                parts = birth_place_raw.split("/")
                if len(parts) > 1:
                    birth_place_pinyin = parts[1].strip().upper()  # 提取并大写
                    self.applicant.place_of_birth = birth_place_pinyin
                    logger.info(f"从'{birth_place_raw}'中提取出生地拼音: '{birth_place_pinyin}'")
                else:
                    self.applicant.place_of_birth = "GUANGDONG"  # 默认值
                    logger.warning(f"出生地格式异常，使用默认值GUANGDONG: '{birth_place_raw}'")
            elif birth_place_raw and birth_place_raw.isascii():
                # 如果是纯ASCII，假设已经是拼音
                self.applicant.place_of_birth = birth_place_raw.strip().upper()
                logger.info(f"使用ASCII出生地: '{self.applicant.place_of_birth}'")
            else:
                # 如果为空或非ASCII且不包含/，使用默认值
                self.applicant.place_of_birth = "GUANGDONG"
                logger.warning(f"无法识别出生地，使用默认值GUANGDONG: '{birth_place_raw}'")
            
            # ✅ 第三步：更新界面 UI 输入框（属于 UI 输入字段 QLineEdit）
            self.chinese_name_edit.setText(self.applicant.chinese_name)
            self.surname_edit.setText(self.applicant.surname)
            self.given_name_edit.setText(self.applicant.given_name)
            self.sex_edit.setText(self.applicant.sex)
            self.nationality_edit.setText(self.applicant.nationality)
            self.dob_edit.setText(self.applicant.dob)
            self.passport_no_edit.setText(self.applicant.passport_number)
            
            
            logger.info("✅ OCR 提取成功，已写入 applicant 并更新界面。")
            self.ocr_finished = True  # ✅ 标记为已完成

            # ✅ 护照有效期提醒
            expiry_str = self.applicant.passport_expiry
            visa_qdate = self.visa_start_date_edit.date()
            visa_start_str = visa_qdate.toString("dd/MM/yyyy")  # 获取签证生效日期字符串

            if expiry_str and visa_start_str:
                try:
                    expiry = datetime.strptime(expiry_str, "%Y%m%d")  # 解析护照有效期字符串
                    visa_start = datetime.strptime(visa_start_str, "%d/%m/%Y")
                    days = (expiry - visa_start).days
                    if days < 183:
                        box = QMessageBox(self)
                        box.setWindowTitle("护照有效期提醒")
                        box.setIcon(QMessageBox.Icon.Warning)
                        box.setText("护照有效期不足6个月，请确定是否继续办理？")
                        box.addButton("确定", QMessageBox.ButtonRole.AcceptRole)
                        box.addButton("取消", QMessageBox.ButtonRole.RejectRole)
                        box.exec()  # PyQt6 用 exec()
                except Exception as e:
                    logger.warning(f"⚠️ 护照有效期提醒处理失败: {e}")

           
        except Exception as e:
            logger.error(f"❌ OCR识别失败: {e}")

    def run_automation_blocking(self):
        """启动自动化填充，并在成功开始前保存当前所有数据"""
        #logger.info(f"➡️ Passing to engine: applicant.intended_entry_gate = '{self.applicant.intended_entry_gate}'") # 旧的错误的日志
         # ✅ 防呆机制，避免未识别完成就操作表单
        if not self.ocr_finished:
            QMessageBox.warning(self, "请稍等", "护照识别尚未完成，请稍候再点击“自动填表”。")
            return
        self.status_label.setText("状态: 准备运行...")
        QApplication.processEvents()

        # 1. Collect data
        # 获取签证类型值
        visa_type_value = "Single-entry" if self.radio_single_entry.isChecked() else "Multiple-entry"
        
        # 获取签证有效期值
        selected_validity_ui = "30天" if self.radio_30_days.isChecked() else "90天"

        # 根据"出签生效"复选框状态决定使用哪个日期值
        if self.check_expedited.isChecked():
            # 使用计算的出签生效日期
            expedited_date = calculate_working_days_from_today(4, VIETNAM_HOLIDAYS_2025)
            start_date_str = expedited_date.strftime("%d/%m/%Y")
            logger.info(f"使用出签生效日期: {start_date_str}")
        else:
            # 使用日期选择器中的日期
            selected_start_qdate = self.visa_start_date_edit.date()
            start_date_str = selected_start_qdate.toString("dd/MM/yyyy")
            logger.info(f"使用自定义日期: {start_date_str}")


         # === Get date from QDateEdit and format it ===
        #selected_start_qdate = self.visa_start_date_edit.date() # Get QDate object
        #start_date_str = selected_start_qdate.toString("dd/MM/yyyy") # Format to strin
        # === Get Entry Gate Mapped Value ===
        selected_entry_gate_ui = self.entry_gate_combo.currentText()
        entry_gate_value = ENTRY_GATE_MAP.get(selected_entry_gate_ui) # Map Chinese UI text to English airport name
        logger.info(f"🔍 UI Entry Gate: Selected Text='{selected_entry_gate_ui}', Mapped Value='{entry_gate_value}'") # <--- 看这里
        self.applicant.intended_entry_gate = entry_gate_value
        if not entry_gate_value: # Handle potential error if map fails
             logger.error(f"无法映射入境口岸: {selected_entry_gate_ui}"); entry_gate_value = "" # Use empty or default?
        # === ===
        # === 收集 Previous Visit 数据 ===
        visited_last_year = self.radio_yes_visited.isChecked() # 获取 Yes 按钮状态 (True/False)
        # 只有当 "Yes" 被选中时才获取日期和目的，否则为 None 或默认值
        prev_entry_date_str = self.prev_entry_date_edit.date().toString("dd/MM/yyyy") if visited_last_year else None
        prev_exit_date_str = self.prev_exit_date_edit.date().toString("dd/MM/yyyy") if visited_last_year else None
        prev_purpose_ui = self.prev_purpose_edit.text().strip() if visited_last_year else "旅游" # 如果选否或为空，默认按"旅游"
        prev_purpose_value = "Travel" if prev_purpose_ui == "旅游" else prev_purpose_ui # 映射 "旅游" 为 "Travel"
        # === ===
        current_data = {
            "customer_source": self.customer_source_edit.text().strip(),  # save customer source
            "chinese_name": self.chinese_name_edit.text().strip(),  # save Chinese name
            "passport_number": self.passport_no_edit.text().strip(),
            "surname": self.surname_edit.text().strip(),
            "given_name": self.given_name_edit.text().strip(),
            "sex": self.sex_edit.text().strip(),
            "dob": self.dob_edit.text().strip(),
            "nationality": self.nationality_edit.text().strip(),
            "visa_entry_type": visa_type_value, # <-- Included visa type value
            "visa_validity_duration": selected_validity_ui,
            "visa_start_date": start_date_str, # <-- Use formatted string here
            "intended_entry_gate": entry_gate_value, # <-- Add mapped airport name
            "email": self.email_edit.text().strip(),
            "telephone_number": self.phone_edit.text().strip(),
            # === Collect new UI fields ===
            "place_of_birth": self.applicant.place_of_birth,
            "passport_type": self.applicant.passport_type,
            "date_of_issue": self.applicant.date_of_issue,
            "passport_expiry": self.applicant.passport_expiry,
           
           
           
            
            "emergency_contact_name": self.emergency_name_edit.text().strip(), # Collects name only
            "emergency_contact_phone": self.emergency_phone_edit.text().strip(),
            # === ===
            # === 添加新字段到字典 ===
            "visited_vietnam_last_year": visited_last_year,
            "previous_entry_date": prev_entry_date_str,
            "previous_exit_date": prev_exit_date_str, # 存储收集到的值
            "previous_purpose": prev_purpose_value,    # 存储映射或获取的值
            # === ===
            "portrait_photo_path": self.selected_portrait_path,
            "passport_scan_path": self.selected_passport_path,
        }
        try: 
            # 这里 applicant_data 才是最终传递给引擎的对象
             logger.info(f"➡️ Passing to engine: applicant_data.intended_entry_gate = '{current_data.get('intended_entry_gate')}'") # <-- *** 新的、正确位置的日志 ***
             applicant_data = Applicant(**current_data)
        except TypeError as e:
            logger.critical(f"无法创建 Applicant 对象: {e}", exc_info=True)
            QMessageBox.critical(self, "代码错误", f"数据模型与UI不匹配:\n{e}\n请联系开发者。")
            self.status_label.setText("状态: 运行失败 - 代码错误")
            return

        # 2. Validate
        required_fields = { "护照号码": applicant_data.passport_number,"姓": applicant_data.surname,"名": applicant_data.given_name, "性别": applicant_data.sex, "出生日期": applicant_data.dob, "国籍": applicant_data.nationality, "邮箱": applicant_data.email, "证件照片": applicant_data.portrait_photo_path, "护照扫描件": applicant_data.passport_scan_path, "生效日期": start_date_str, "入境口岸": applicant_data.intended_entry_gate} # Simplified field names
         # === 条件性添加过往记录的校验 ===
        if visited_last_year: # 使用前面获取的布尔值
            logger.info("用户选择了'去过越南'，检查过往记录字段。")
            required_fields.update({
                 "上次入境日期": applicant_data.previous_entry_date,
                 # "上次离开日期": applicant_data.previous_exit_date, # 离开日期通常需要，添加校验
                 "上次旅行目的": applicant_data.previous_purpose
            })
            # 也可以在这里添加对 prev_entry_date_str 的格式二次校验 (虽然 QDateEdit 通常能保证)
        
       

        missing = [name for name, value in required_fields.items() if not value]
        if missing:
            message = f"请填写或选择以下必填项：\n- {', '.join(missing)}"
            if QApplication.instance():
                QMessageBox.warning(self.window() if hasattr(self, "window") else None, "缺少信息", message)
            else:
                logger.warning(f"⚠️ 缺少信息（非UI环境）：{message}")
            self.status_label.setText("状态: 运行失败 - 缺少信息")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            return

        if '@' not in applicant_data.email:
            if QApplication.instance():
                QMessageBox.warning(self.window() if hasattr(self, "window") else None, "格式错误", "请输入有效的邮箱地址。")
            else:
                logger.warning("⚠️ 格式错误（非UI环境）：请输入有效的邮箱地址。")
            self.status_label.setText("状态: 运行失败 - 邮箱格式无效")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            return


        # 3. Save Data
        logger.info("数据校验通过，保存当前数据...")
        if not save_preference(LAST_DATA_KEY, current_data): logger.warning("未能保存本次数据。")

        # 4. Run Automation
        #self.log_display.clear(); self.status_label.setText("状态: 正在运行自动化..."); self.status_label.setStyleSheet("color: orange; font-weight: bold;"); QApplication.processEvents()
        try:
            engine = SimpleEngine()
            success = engine.run_vietnam_evisa_step1(applicant_data)
            logger.info(f"自动化执行结束，结果: {'成功' if success else '失败'}")
        except Exception as e: logger.critical(f"执行自动化时发生未捕获错误: {e}", exc_info=True); success = False

        # 5. Update Status
        if success:
            self.status_label.setText("状态: 自动化尝试完成。"); self.status_label.setStyleSheet("color: green; font-weight: bold;"); QMessageBox.information(None, "完成", "自动化尝试已执行。\n请检查浏览器和日志。")
        else:
            self.status_label.setText("状态: 运行失败，请查看日志。"); self.status_label.setStyleSheet("color: red; font-weight: bold;"); QMessageBox.critical(None, "失败", "自动化执行过程中发生错误。\n请检查日志区域。")
        QApplication.processEvents()
