import sqlite3
from pathlib import Path
from typing import Optional
from datetime import datetime, timedelta, timezone,date
from app.utils.logger_config import get_logger

logger = get_logger()

# 路径配置
DB_PATH = Path("results/visa_results.db")
SCHEMA_PATH = Path("db/schema.sql")

def get_beijing_time():
    """获取北京时间，可在任何数据库中使用"""
    beijing_tz = timezone(timedelta(hours=8))
    return datetime.now(beijing_tz).strftime("%Y-%m-%d %H:%M:%S")


def init_db():
    """初始化数据库结构"""
    logger.debug("开始初始化数据库结构")
    if not SCHEMA_PATH.exists():
        logger.error(f"缺少建表文件: {SCHEMA_PATH}")
        raise FileNotFoundError(f"缺少建表文件: {SCHEMA_PATH}")
    with sqlite3.connect(DB_PATH) as conn:
        logger.debug(f"✅ 成功连接到数据库: {DB_PATH}")
        with open(SCHEMA_PATH, "r", encoding="utf-8") as f:
            sql_script = f.read()
            logger.debug(f"读取模式文件成功，准备执行SQL脚本")
            conn.executescript(sql_script)
            logger.debug(f"✅ 数据库:结构初始化完成")


def insert_applicant(data: dict) -> int:
    """插入新申请人，返回申请人ID"""
    logger.debug(f"准备插入申请人信息: {data.get('chinese_name')} ({data.get('passport_number')})")
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.execute("""
            INSERT INTO applicant (
                created_at, customer_source, chinese_name, surname, given_name,
                sex, dob, place_of_birth, passport_number, place_of_issue,
                date_of_issue, passport_expiry, nationality, email, telephone_number
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            data.get("created_at") or get_beijing_time(),
            data.get("customer_source"),
            data.get("chinese_name"),
            data.get("surname"),
            data.get("given_name"),
            data.get("sex"),
            data.get("dob"),
            data.get("place_of_birth"),
            data.get("passport_number"),
            data.get("place_of_issue"),
            data.get("date_of_issue"),
            data.get("passport_expiry"),
            data.get("nationality"),
            data.get("email"),
            data.get("telephone_number")
        ))
        logger.debug(f"✅ 数据库:申请人信息插入成功，ID: {cursor.lastrowid}")
        return cursor.lastrowid


def get_or_create_applicant(data: dict) -> int:
    """获取已存在的申请人，或新建"""
    logger.debug(f"数据库：查找或创建申请人: {data.get('passport_number')}")
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.execute(
            "SELECT id FROM applicant WHERE passport_number = ?",
            (data["passport_number"],)
        )
        row = cursor.fetchone()
        if row:
            logger.debug(f"✅ 数据库:找到已存在申请人，ID: {row[0]}")
            return row[0]
    logger.debug(f"数据库：未找到申请人，准备创建新记录: {data.get('passport_number')}")
    return insert_applicant(data)


def insert_visa_task(applicant_id: int, data: dict) -> int:
    """插入签证任务 返回任务ID"""
    logger.debug(f"数据库：准备插入签证任务记录，申请人ID: {applicant_id}, 签证类型: {data.get('visa_type')}")
    
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.execute("""
            INSERT INTO visa_task (
                applicant_id, visa_type, visa_validity_days, visa_start_date, entry_gate,
                submit_status, approval_status, is_downloaded, pdf_path, download_time,
                screenshot_path, log_path, error_message, application_number, created_at,
                session_id
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            applicant_id,
            data.get("visa_type"),
            data.get("visa_validity_days"),
            data.get("visa_start_date"),
            data.get("entry_gate"),
            data.get("submit_status", "FAILED"),
            data.get("approval_status", "WAITING"),
            data.get("is_downloaded", 0),
            data.get("pdf_path"),
            data.get("download_time"),
            data.get("screenshot_path"),
            data.get("log_path"),
            data.get("error_message"), 
            data.get("application_number"),
            get_beijing_time(),  # 获取当前北京时间
            data.get("session_id")  # 新增 会话ID
        ))

        task_id = cursor.lastrowid
        logger.debug(f"✅ 数据库: 成功插入签证任务记录，ID: {task_id}, 申请编号: {data.get('application_number')}")
        
        return cursor.lastrowid


def update_task_submission(task_id: int, application_number: str):
    """提交成功后更新签证编号与状态"""
    logger.debug(f"数据库：更新任务提交状态，任务ID: {task_id}, 申请编号: {application_number}")
    with sqlite3.connect(DB_PATH) as conn:
        conn.execute("""
            UPDATE visa_task
            SET submit_status = 'SUCCESS',
                application_number = ?
            WHERE id = ?
        """, (application_number, task_id))
        logger.debug(f"✅ 数据库: 任务提交状态更新成功")


def find_task_by_application_number(app_number: str) -> Optional[int]:
    logger.debug(f"数据库：通过申请编号查找任务: {app_number}")
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.execute("""
            SELECT id FROM visa_task
            WHERE application_number = ?
            ORDER BY created_at DESC
            LIMIT 1
        """, (app_number,))
        row = cursor.fetchone()
        logger.debug(f"✅ 数据库:查找结果: {'找到任务ID: ' + str(row[0]) if row else '未找到匹配任务'}")
        return row[0] if row else None
    

def get_applicant_by_application_number(app_number: str) -> Optional[dict]:
    """通过 application_number 查询申请人信息"""
    logger.debug(f"数据库：通过申请编号查找申请人信息: {app_number}")
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.execute("""
            SELECT a.*
            FROM applicant a
            JOIN visa_task v ON v.applicant_id = a.id
            WHERE v.application_number = ?
            ORDER BY v.created_at DESC
            LIMIT 1
        """, (app_number,))
        row = cursor.fetchone()
        if row:
            columns = [desc[0] for desc in cursor.description]
            logger.debug(f"✅ 数据库:查找结果: {'找到申请人信息: ' + str(row) if row else '未找到匹配申请人信息'}")
            return dict(zip(columns, row))
        logger.debug(f"数据库:未找到匹配申请人信息")
        return None



def update_payment_status(task_id: int):
    """更新任务的支付状态为已支付"""
    logger.debug(f"数据库：更新支付状态为已支付，任务ID: {task_id}")
    with sqlite3.connect(DB_PATH) as conn:
        conn.execute("""
            UPDATE payment_record
            SET payment_status = 'PAID'
            WHERE visa_task_id = ?
        """, (task_id,))
        logger.debug(f"✅ 数据库:支付状态更新成功")



def update_pdf_download_status(task_id: int, pdf_path: str):
    """下载完成后，更新 PDF 路径、下载时间、下载状态"""
    logger.debug(f"数据库：更新PDF下载状态，任务ID: {task_id}, PDF路径: {pdf_path}")
    with sqlite3.connect(DB_PATH) as conn:
        conn.execute("""
            UPDATE visa_task
            SET
                pdf_path = ?,
                download_time = CURRENT_TIMESTAMP,
                is_downloaded = 1
            WHERE id = ?
        """, (pdf_path, task_id))
        logger.debug(f"✅ 数据库:PDF下载状态更新成功")


def get_task_display_fields(task_id: int) -> dict | None:
    """获取任务的显示字段"""
    logger.debug(f"数据库：获取任务显示字段，任务ID: {task_id}")
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.execute("""
            SELECT a.chinese_name, v.visa_validity_days, v.visa_type, v.entry_gate,v.visa_start_date
            FROM visa_task v
            JOIN applicant a ON a.id = v.applicant_id
            WHERE v.id = ?
        """, (task_id,))
        row = cursor.fetchone()
        if not row:
            logger.debug(f"未找到任务ID: {task_id}的显示字段")
            return None
        result = {
            "chinese_name": row[0],
            "valid_days": row[1],
            "visa_type": row[2],
            "entry_gate": row[3],
            "visa_start_date": row[4]
        }
        logger.debug(f"✅ 数据库：成功获取任务显示字段: {result}")
        return result

def find_task_by_name_and_dob(full_name: str, dob: str) -> Optional[int]:
    """通过姓名和出生日期查找 visa_task.id"""
    # 标准化输入的姓名：去除多余空格，转为大写
    normalized_name = " ".join(full_name.strip().upper().split())
    logger.debug(f"数据库：通过姓名和出生日期查找任务，标准化姓名: '{normalized_name}', 出生日期: '{dob}'")
    with sqlite3.connect(DB_PATH) as conn:
        # 使用标准化的SQL查询 - 允许大小写和空格数量的差异，但保持单词完整性
        cursor = conn.execute("""
            SELECT vt.id
            FROM visa_task vt
            JOIN applicant a ON a.id = vt.applicant_id
            WHERE TRIM(UPPER(a.surname || ' ' || a.given_name)) = ?
              AND a.dob = ?
            ORDER BY vt.created_at DESC
            LIMIT 1
        """, (normalized_name, dob))
        row = cursor.fetchone()
        
        if row:
            logger.debug(f"✅ 数据库：找到匹配任务ID: {row[0]}")
            return row[0]
        
        # 如果精确匹配失败，记录调试信息
        logger.debug(f"未找到匹配记录，查询参数: full_name='{full_name}', normalized='{normalized_name}', dob='{dob}'")
        
        # 查看数据库中的一些记录用于调试
        cursor = conn.execute("""
            SELECT a.id, a.surname, a.given_name, a.dob, UPPER(a.surname || ' ' || a.given_name) as full_name
            FROM applicant a
            ORDER BY a.id DESC
            LIMIT 5
        """)
        rows = cursor.fetchall()
        for row in rows:
            logger.debug(f"数据库记录: ID={row[0]}, surname='{row[1]}', given_name='{row[2]}', dob='{row[3]}', full_name='{row[4]}'")
        
        return None


def insert_payment_record(task_id: int, data: dict):
    """插入付款记录"""
    logger.debug(f"数据库：插入付款记录，任务ID: {task_id}, 金额: {data.get('payment_amount')}, 方式: {data.get('payment_method')}")
    with sqlite3.connect(DB_PATH) as conn:
        conn.execute("""
            INSERT INTO payment_record (
                visa_task_id, payment_time, payment_amount, card_number_tail,
                payment_method, payment_status, transaction_id, source_email
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            task_id,
            data.get("payment_time"),
            data.get("payment_amount"),
            data.get("card_number_tail"),
            data.get("payment_method"),
            data.get("payment_status", "PAID"),
            data.get("transaction_id"),
            data.get("source_email")
        ))
        logger.debug(f"✅ 数据库：付款记录插入成功")
        

def insert_email_notification(task_id: Optional[int], data: dict):
    """插入邮件记录，可绑定任务或留空"""
    logger.debug(f"数据库：插入邮件通知记录，任务ID: {task_id}, 发件人: {data.get('from_address')}, 主题: {data.get('subject')[:30]}...")
    with sqlite3.connect(DB_PATH) as conn:
        conn.execute("""
            INSERT INTO email_notification (
                visa_task_id, received_time, from_address, subject,
                content_excerpt, application_number, email_type
            )
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            task_id,
            data.get("received_time"),
            data.get("from_address"),
            data.get("subject"),
            data.get("content_excerpt"),
            data.get("application_number"),
            data.get("email_type")
        ))
        logger.debug(f"✅ 数据库：邮件通知记录插入成功")
def count_records():
    """统计数据库记录数量"""
    logger.debug("数据库：开始统计数据库记录数量")
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.execute("SELECT COUNT(*) FROM applicant")
        applicant_count = cursor.fetchone()[0]
        
        cursor = conn.execute("SELECT COUNT(*) FROM visa_task")
        task_count = cursor.fetchone()[0]
        
        cursor = conn.execute("SELECT COUNT(*) FROM visa_task WHERE submit_status = 'SUCCESS'")
        success_count = cursor.fetchone()[0]
        
        cursor = conn.execute("SELECT COUNT(*) FROM visa_task WHERE is_downloaded = 1")
        downloaded_count = cursor.fetchone()[0]
        
        result = {
            "applicants": applicant_count,
            "tasks": task_count,
            "success": success_count,
            "downloaded": downloaded_count
        }
        logger.debug(f"✅数据库:统计结果: {result}")
        return result
def verify_recent_records(limit=5):
    """验证最近的数据库记录"""
    logger.debug(f"数据库：验证最近 {limit} 条记录")
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        cursor = conn.execute("""
            SELECT vt.id, vt.created_at, a.chinese_name, a.surname, a.given_name, a.passport_number, a.dob, a.sex, a.customer_source,
                   vt.visa_validity_days, vt.visa_type, vt.entry_gate, vt.visa_start_date, a.email, vt.submit_status, vt.application_number, vt.approval_status,
                   pr.payment_status, pr.payment_time, pr.payment_amount, pr.payment_method, pr.card_number_tail, pr.transaction_id, pr.source_email
            FROM visa_task vt
            JOIN applicant a ON a.id = vt.applicant_id
            LEFT JOIN payment_record pr ON pr.visa_task_id = vt.id
            ORDER BY vt.created_at DESC
            LIMIT ?
        """, (limit,))
        
        records = [dict(row) for row in cursor.fetchall()]
        logger.debug(f"✅ 数据库：成功获取 {len(records)} 条最近记录")
        return records

def update_application_number(passport_number: str, application_number: str):
    """
    通过护照号码更新签证任务的申请编号
    
    Args:
        passport_number: 护照号码
        application_number: 签证申请编号
    """
    logger.debug(f"数据库：通过护照号码 {passport_number} 更新申请编号 {application_number}")
    
    with sqlite3.connect(DB_PATH) as conn:
        # 先查找最新的签证任务ID
        cursor = conn.execute("""
            SELECT vt.id 
            FROM visa_task vt
            JOIN applicant a ON a.id = vt.applicant_id
            WHERE a.passport_number = ?
            ORDER BY vt.created_at DESC
            LIMIT 1
        """, (passport_number,))
        
        row = cursor.fetchone()
        if not row:
            logger.warning(f"⚠️ 数据库：未找到护照号码 {passport_number} 对应的签证任务")
            return 
            
        task_id = row[0]
        
        # 更新申请编号
        conn.execute("""
            UPDATE visa_task
            SET application_number = ?,
                submit_status = 'SUCCESS'
            WHERE id = ?
        """, (application_number, task_id))
        
        logger.info(f"✅ 数据库：成功更新申请编号 {application_number} 到任务ID {task_id}")

def check_payment_exists(passport_number: str) -> bool:
    """检查指定护照号码是否已有付款记录"""
    logger.debug(f"数据库：检查护照号 {passport_number} 是否已有付款记录")
    with sqlite3.connect(DB_PATH) as conn:
        # 先查找申请人ID
        applicant_id = conn.execute(
            "SELECT id FROM applicant WHERE passport_number = ?", 
            (passport_number,)
        ).fetchone()
        
        if not applicant_id:
            logger.debug(f"数据库：未找到护照号 {passport_number} 的申请人记录")
            return False
            
        # 查找该申请人的任务记录
        task_ids = conn.execute(
            "SELECT id FROM visa_task WHERE applicant_id = ?",
            (applicant_id[0],)
        ).fetchall()
        
        if not task_ids:
            logger.debug(f"数据库：申请人 {passport_number} 无任务记录")
            return False
            
        # 查找任务关联的付款记录
        task_id_list = [t[0] for t in task_ids]
        placeholders = ','.join(['?'] * len(task_id_list))
        payment_records = conn.execute(
            f"SELECT COUNT(*) FROM payment_record WHERE visa_task_id IN ({placeholders}) AND payment_status = 'PAID'",
            task_id_list
        ).fetchone()
        
        has_payment = payment_records[0] > 0
        if has_payment:
            logger.info(f"✅ 数据库：申请人 {passport_number} 已有付款记录")
        else:
            logger.debug(f"数据库：申请人 {passport_number} 无付款记录")
        return has_payment
