-- Table: applicant
CREATE TABLE IF NOT EXISTS applicant (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    created_at TEXT,
    customer_source TEXT,
    chinese_name TEXT,
    surname TEXT,
    given_name TEXT,
    sex TEXT,
    dob TEXT,
    place_of_birth TEXT,
    passport_number TEXT UNIQUE,
    place_of_issue TEXT,
    date_of_issue TEXT,
    passport_expiry TEXT,
    nationality TEXT,
    email TEXT,
    telephone_number TEXT
);

-- Table: visa_task
CREATE TABLE IF NOT EXISTS visa_task (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    applicant_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    visa_type TEXT,
    visa_validity_days INTEGER,
    visa_start_date TEXT,
    entry_gate TEXT,
    application_number TEXT,
    submit_status TEXT,
    approval_status TEXT,
    is_downloaded INTEGER DEFAULT 0,
    pdf_path TEXT,
    download_time DATETIME,
    screenshot_path TEXT,
    log_path TEXT,
    error_message TEXT,
    session_id TEXT,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (applicant_id) REFERENCES applicant(id)
);

-- Table: payment_record
CREATE TABLE IF NOT EXISTS payment_record (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    visa_task_id INTEGER NOT NULL,
    payment_time DATETIME,
    payment_amount REAL,
    card_number_tail TEXT,
    payment_method TEXT,
    payment_status TEXT,
    transaction_id TEXT,
    source_email TEXT,
    FOREIGN KEY (visa_task_id) REFERENCES visa_task(id)
);

-- Table: email_notification
CREATE TABLE IF NOT EXISTS email_notification (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    visa_task_id INTEGER,  -- 可为空，允许未能匹配任务
    received_time DATETIME,
    from_address TEXT,
    subject TEXT,
    content_excerpt TEXT,
    application_number TEXT,
    email_type TEXT,
    FOREIGN KEY (visa_task_id) REFERENCES visa_task(id)
);
