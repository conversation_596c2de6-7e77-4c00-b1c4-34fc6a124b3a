/* 表单页专用样式 - Form Page Styles */

body {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
    background-color: var(--background-color);
}

/* 顶部导航栏 */
.top-navbar {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar-brand {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
}

.navbar-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-color);
    font-size: 0.9rem;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.8rem;
}

.logout-btn {
    padding: 0.5rem 1rem;
    background: #f3f4f6;
    color: var(--text-color);
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.logout-btn:hover {
    background: #e5e7eb;
    border-color: #d1d5db;
}

/* 主内容区域 */
.main-content {
    padding: 0 2rem 2rem 2rem;
}

/* 进度指示器 */
.progress-container {
    background: white;
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.progress-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.progress-header h3 {
    color: var(--text-color);
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    margin-bottom: 1rem;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    flex: 1;
    z-index: 2;
}

.step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    border: 2px solid #e5e7eb;
    background: white;
    color: #6b7280;
}

.step-circle.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.step-circle.completed {
    background: #10b981;
    border-color: #10b981;
    color: white;
}

.step-label {
    font-size: 0.8rem;
    color: #6b7280;
    text-align: center;
    max-width: 80px;
}

.step-label.active {
    color: var(--primary-color);
    font-weight: 600;
}

.step-label.completed {
    color: #10b981;
    font-weight: 600;
}

.progress-line {
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background: #e5e7eb;
    z-index: 1;
}

.progress-line-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-info {
    text-align: center;
    font-size: 0.9rem;
    color: #6b7280;
}

h1 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 3rem;
    font-size: 2.5rem;
    font-weight: 700;
}

.form-container {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    max-width: 800px;
    margin: 0 auto;
}

.form-header {
    margin-bottom: 2.5rem;
    text-align: center;
}

.form-header h2 {
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.form-header p {
    color: #6b7280;
    font-size: 0.95rem;
}

.form-group {
    margin-bottom: 1.25rem;
    position: relative;
}

.form-group.inline {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.25rem;
}

.form-group.inline .form-group {
    flex: 1;
}

.form-group.inline label {
    margin-bottom: 0.5rem;
}

.form-group.inline input,
.form-group.inline select {
    width: 100%;
}

/* 文件上传样式 */
.file-uploads {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.25rem;
}

.file-upload {
    background: white;
    padding: 1.25rem;
    border-radius: var(--border-radius);
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-input);
    width: 100%;
}

.file-upload:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-input-focus);
}

.file-upload label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.file-upload input[type="file"] {
    display: none;
}

.file-upload .upload-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--primary-color);
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.95rem;
}

.file-upload .upload-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-1px);
}

.file-upload .upload-btn::before {
    content: '';
    display: inline-block;
    width: 24px;
    height: 24px;
    background-image: url('/static/images/upload arrows.png');
    background-size: contain;
    background-repeat: no-repeat;
    margin-right: 0.5rem;
}

.file-preview {
    width: 100%;
    height: 200px;
    border: 2px dashed #e5e7eb;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
    background: #f9fafb;
    cursor: pointer;
    position: relative;
}

.file-preview:hover {
    border-color: var(--primary-color);
    background: white;
}

.file-preview.drag-over {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
    transform: scale(1.02);
}

.file-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.file-preview-text {
    text-align: center;
    color: #6b7280;
    font-size: 0.9rem;
}

.file-preview-text .drag-text {
    font-weight: 600;
    color: var(--primary-color);
}

.file-preview-text .file-types {
    font-size: 0.8rem;
    margin-top: 0.5rem;
    color: #9ca3af;
}

/* 单选框样式 */
.radio-group {
    display: flex;
    gap: 1.5rem;
    margin-top: 0.5rem;
    flex-wrap: wrap;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1 1 auto;
    min-width: 120px;
    border: 1px solid #e5e7eb;
}

.radio-option:hover {
    background: #f3f4f6;
    border-color: var(--primary-color);
}

.radio-option input[type="radio"] {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-radius: 50%;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
}

.radio-option input[type="radio"]:hover {
    border-color: var(--primary-color);
}

.radio-option input[type="radio"]:checked {
    border-color: var(--primary-color);
    background: var(--primary-color);
}

.radio-option input[type="radio"]:checked::after {
    content: '\2714';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(15deg);
    font-size: 16px;
    color: white;
    transition: all 0.2s ease;
    font-weight: 700;
    line-height: 1;
}

.radio-option label {
    font-weight: 500;
    color: var(--text-color);
    font-size: 1rem;
}

/* 表单操作区域 */
.form-actions {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
    text-align: right;
}

.submit-btn {
    padding: 1rem 2rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-md);
    font-size: 1rem;
}

.submit-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn:disabled {
    background-color: #e5e7eb;
    cursor: not-allowed;
    transform: none;
}

/* 表单章节样式 */
.form-section h3 {
    color: var(--text-color);
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

.form-section p {
    color: #6b7280;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.form-section .optional {
    color: #6b7280;
    font-size: 0.85rem;
}

.form-section .required {
    color: #ef4444;
    font-size: 0.85rem;
}

/* 日期输入样式 */
.date-input {
    display: flex;
    gap: 0.5rem;
}

/* Flatpickr 样式覆盖 */
.flatpickr-calendar {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
}

.flatpickr-day.selected, .flatpickr-day:hover {
    background-color: var(--primary-color);
}

.flatpickr-day.selected:hover {
    background-color: var(--secondary-color);
}

.flatpickr-day.today {
    border-color: var(--primary-color);
}

/* 实时验证样式 */
.validation-message {
    font-size: 0.8rem;
    margin-top: 0.25rem;
    padding: 0.25rem 0;
    transition: all 0.2s ease;
}

.validation-message.success {
    color: #10b981;
}

.validation-message.error {
    color: #ef4444;
}

.validation-message.warning {
    color: #f59e0b;
}

.validation-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1rem;
    pointer-events: none;
}

.validation-icon.success {
    color: #10b981;
}

.validation-icon.error {
    color: #ef4444;
}

/* 提交状态和错误处理样式 */
.submit-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.submit-overlay.show {
    opacity: 1;
    visibility: visible;
}

.submit-modal {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    text-align: center;
}

/* OCR 确认弹窗样式 */
.ocr-confirmation {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.ocr-confirmation.show {
    opacity: 1;
    visibility: visible;
}

.ocr-modal {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.ocr-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.ocr-header h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.ocr-fields {
    display: grid;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.ocr-field {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    background: #f9fafb;
}

.ocr-field-label {
    font-weight: 600;
    min-width: 120px;
    color: var(--text-color);
}

.ocr-field-value {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    background: white;
}

.ocr-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        padding: 0 1rem 2rem 1rem;
    }

    .top-navbar {
        padding: 1rem;
    }

    .form-container {
        padding: 1.5rem;
    }

    .form-group.inline {
        flex-direction: column;
        gap: 0;
    }

    .file-uploads {
        grid-template-columns: 1fr;
    }

    h1 {
        font-size: 2rem;
    }

    .progress-steps {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .step-circle {
        width: 35px;
        height: 35px;
        font-size: 0.8rem;
    }

    .step-label {
        font-size: 0.7rem;
        max-width: 60px;
    }

    .ocr-field {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .ocr-field-label {
        min-width: auto;
    }

    .ocr-actions {
        flex-direction: column;
    }
}
