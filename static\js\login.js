// 登录页JavaScript功能 - Login Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeLoginPage();
});

function initializeLoginPage() {
    // 初始化表单验证
    setupLoginValidation();
    
    // 初始化表单提交
    setupLoginSubmission();
    
    // 检查是否有错误消息需要显示
    checkForErrors();
    
    // 自动聚焦到用户名输入框
    const usernameInput = document.getElementById('username');
    if (usernameInput) {
        usernameInput.focus();
    }
}

function setupLoginValidation() {
    const form = document.querySelector('form');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    
    if (!form || !usernameInput || !passwordInput) return;
    
    // 实时验证用户名
    usernameInput.addEventListener('input', Utils.debounce(function() {
        validateUsername(this.value);
    }, 300));
    
    // 实时验证密码
    passwordInput.addEventListener('input', Utils.debounce(function() {
        validatePassword(this.value);
    }, 300));
    
    // 表单提交前验证
    form.addEventListener('submit', function(e) {
        const isUsernameValid = validateUsername(usernameInput.value);
        const isPasswordValid = validatePassword(passwordInput.value);
        
        if (!isUsernameValid || !isPasswordValid) {
            e.preventDefault();
            showValidationError('请检查输入的用户名和密码');
        }
    });
}

function validateUsername(username) {
    const usernameInput = document.getElementById('username');
    
    if (!username || username.trim() === '') {
        setInputState(usernameInput, 'error');
        return false;
    }
    
    if (username.length < 3) {
        setInputState(usernameInput, 'error');
        return false;
    }
    
    setInputState(usernameInput, 'valid');
    return true;
}

function validatePassword(password) {
    const passwordInput = document.getElementById('password');
    
    if (!password || password.trim() === '') {
        setInputState(passwordInput, 'error');
        return false;
    }
    
    if (password.length < 6) {
        setInputState(passwordInput, 'error');
        return false;
    }
    
    setInputState(passwordInput, 'valid');
    return true;
}

function setInputState(input, state) {
    input.classList.remove('valid', 'invalid');
    if (state === 'valid') {
        input.classList.add('valid');
    } else if (state === 'error') {
        input.classList.add('invalid');
    }
}

function setupLoginSubmission() {
    const form = document.querySelector('form');
    if (!form) return;
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const submitButton = form.querySelector('button[type="submit"]');
        
        // 禁用提交按钮
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = '登录中...';
        }
        
        try {
            const response = await fetch('/login', {
                method: 'POST',
                body: formData
            });
            
            if (response.ok) {
                // 登录成功，重定向到表单页面
                window.location.href = '/test-form';
            } else {
                // 登录失败，显示错误消息
                const errorData = await response.json();
                showValidationError(errorData.detail || '登录失败，请检查用户名和密码');
            }
        } catch (error) {
            console.error('登录错误:', error);
            showValidationError('网络连接出现问题，请稍后重试');
        } finally {
            // 恢复提交按钮
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.textContent = '登录';
            }
        }
    });
}

function showValidationError(message) {
    // 移除现有的错误消息
    const existingError = document.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
    
    // 创建新的错误消息
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;
    
    // 插入到表单前面
    const form = document.querySelector('form');
    if (form) {
        form.insertBefore(errorDiv, form.firstChild);
        
        // 3秒后自动隐藏错误消息
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }
}

function checkForErrors() {
    // 检查URL参数中是否有错误消息
    const urlParams = new URLSearchParams(window.location.search);
    const error = urlParams.get('error');
    
    if (error) {
        let errorMessage = '登录失败';
        switch (error) {
            case 'invalid_credentials':
                errorMessage = '用户名或密码错误';
                break;
            case 'account_locked':
                errorMessage = '账户已被锁定，请联系管理员';
                break;
            case 'session_expired':
                errorMessage = '会话已过期，请重新登录';
                break;
            default:
                errorMessage = '登录失败，请重试';
        }
        
        showValidationError(errorMessage);
        
        // 清除URL参数
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
    }
}

// 记住我功能
function setupRememberMe() {
    const rememberCheckbox = document.getElementById('remember');
    const usernameInput = document.getElementById('username');
    
    if (!rememberCheckbox || !usernameInput) return;
    
    // 页面加载时检查是否有保存的用户名
    const savedUsername = localStorage.getItem('rememberedUsername');
    if (savedUsername) {
        usernameInput.value = savedUsername;
        rememberCheckbox.checked = true;
    }
    
    // 表单提交时保存或清除用户名
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function() {
            if (rememberCheckbox.checked) {
                localStorage.setItem('rememberedUsername', usernameInput.value);
            } else {
                localStorage.removeItem('rememberedUsername');
            }
        });
    }
}

// 键盘快捷键支持
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Enter键提交表单
        if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.altKey) {
            const form = document.querySelector('form');
            const activeElement = document.activeElement;
            
            if (form && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'BUTTON')) {
                e.preventDefault();
                form.requestSubmit();
            }
        }
    });
}

// 初始化所有功能
document.addEventListener('DOMContentLoaded', function() {
    setupRememberMe();
    setupKeyboardShortcuts();
});

// 密码可见性切换
function togglePasswordVisibility() {
    const passwordInput = document.getElementById('password');
    const toggleButton = document.querySelector('.password-toggle');
    
    if (passwordInput && toggleButton) {
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleButton.textContent = '隐藏';
        } else {
            passwordInput.type = 'password';
            toggleButton.textContent = '显示';
        }
    }
}

// 自动填充检测
function detectAutofill() {
    const inputs = document.querySelectorAll('input');
    
    inputs.forEach(input => {
        // 检测浏览器自动填充
        const checkAutofill = () => {
            if (input.value && input.value !== input.defaultValue) {
                input.classList.add('autofilled');
            }
        };
        
        // 延迟检测，因为自动填充可能需要时间
        setTimeout(checkAutofill, 100);
        setTimeout(checkAutofill, 500);
        setTimeout(checkAutofill, 1000);
    });
}

// 页面加载完成后检测自动填充
window.addEventListener('load', detectAutofill);
